

content
下发采集任务label_group.gather_task格式说明

```json
{
    edgeGatewayId: 123,
    
    labelGroupId: 123, //冗余

    version: "1",
    gatewayIp: "",
    gatewayPort: 123,

    interval: 500, //ms
    begin: "2021-12-31 00:00:00",
    end: "2021-12-31 00:00:00", //null表示无穷

    //一个通道对应一个item
    gatherItems:[{
        channelId: 12345,
        driver: "Snap7",
        ip: "",
        port: 123,
        rack: 1,
        slot: 2,
        multiParam: [{
            labelId: 123,
            labelName: "temperature",
            paramType: int,
            isArray: false,
            address: "DB50.DBW1",
            length: 1, //如果是数组，表示数组元素个数且前端限制长度>1，如果不是数组，值为1
            stringBytes: 2, //如果是string数组，每个string元素的byte长度n
        },{
            labelId: 123,
            labelName: "temperature",
            paramType: int,
            isArray: false,
            address: "DB50.DBW2",
            length: 1,
        }],
    }] 
}
```