

## 下发计算任务compute_task.content格式说明

```json
{
    "taskId": 123456789,

    "interval": 500, //ms，冗余，用于网关定位采集任务

    "version": "1",
    "gatewayIp": "",
    "gatewayPort": 123,
    "computeItems": [{
        "type": "trigger", //条件触发上报
        //   device(123).temperature > 90 and 
        //   (  
        //     device(123).temperatureExceedEvent
        //   ) and
        //   device(123).temperatureLowEvent
        "trigger": [{
            "type": "compare", //类型，compare-对比, bracket-括号, single-单个
            "level": 0,

            "isArray": true, //前端冗余字段 
            "proteType": "", //前端冗余字段 
            "isProteType": true, //前端冗余字段 
            
            "left": {
                "labelId": 123,
                "deviceId": 123, 
                "property": "temperature",
            },
            "operator": ">", //运算符 >, <, =, <=, >=, !=
            "right": {
                "value": "90", //可以是bool/short/int/float/string类型的数据的字符串
            },
            "ttl": 4000, //单位ms
            "logic": "and", //逻辑符 and, or
        },{
            "type": "bracket",
            "bracket": "left",
            "level": 0,
        },{
            "type": "single",
            "level": 1,
            "single": {
                "deviceId": 123, 
                "event": "temperatureExceedEvent",
            },
            "ttl": 4000, //单位ms
        },{
            "type": "bracket",
            "bracket": "right",
            "level": 0,
            "logic": "and", //逻辑符 and, or
        },{
            "type": "single",
            "level": 0,
            "single": {
                "deviceId": 123, 
                "event": "temperatureLowEvent",
            }
            "ttl": 4000, //单位ms
        }],
        "eventName": "",
        "properties": [{ //触发成功后，要上报的属性
            "property": "temperature",
            "labelId": $labelId,
            "dataType": "int",
            "isArray": true,
            "length": 12,
            "stringBytes": 3,
        },{
            "property": "powerLevel",
            "labelId": $labelId,
            "dataType": "int",
            "isArray": true,
            "length": 12,
            "stringBytes": 3,
        }],
        "faultInputMap": {
            "propertyAaa": "inputBbb"
        },
        "topic": ""
    },{
        "type": "change", //改变上报
        "eventName": "",
        "property": "temperature",
        "labelId": 123,
        "dataType": "int",
        "isArray": true,
        "length": 1,
        "topic": ""
    },{
        "type": "report", //直接上报
        "eventName": "",
        "property": "temperature",
        "labelId": 123,
        "isArray": true,
        "dataType": "int",
        "length": 1,
        "topic": ""
    },{
        "type": "labelChange", //标签改变上报
        "labelId": 123,
        "labelName": "",
        "isArray": true,
        "dataType": "int",
        "length": 1,
        "topic": ""
    },{
        "type": "labelReport", //标签直接上报
        "labelId": 123,
        "labelName": "",
        "isArray": true,
        "dataType": "int",
        "length": 1,
        "topic": ""
    },{
        "type": "", //规则属性上报
    }],
}
```