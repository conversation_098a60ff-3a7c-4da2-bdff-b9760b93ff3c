
## metadata 过期

```json
{
    "propertyMetadata": {
        "lightSwitch": { //开关
            //物模型冗余属性，用于前端校验
            "name": "",
            "dataType": "",
            "isArray": false,
            //绑定标签
            "labelId": 123
        }
    },
   "eventMetadata": {
        "temperatureExceedEvent": { //温度过高警告事件
            "powerLevel1": 0,
            "temperature1": 60,
            "powerLevel2": 1,
            "temperature2": 90
        }
    }
}
```

## runtime_metadata

```json
{
  "tenantId": 1527470820947841000,
  "deviceName": "雪茄输出设备1",
  "resourceId": "1354451905552384",
  "productName": "雪茄输出机产品",
  "edgeGatewayId": 1354450655674368,
  "edgeGatewayName": "网关1",
  "properties": {
    "switch": {
      "access": "DB50.INT40",
      "length": 1,
      "address": "DB50.INT40",
      "isArray": false,
      "labelId": 1354453278818304,
      "persist": false,
      "dataType": {
        "spec": {
          "max": "",
          "min": "",
          "defaultValue": ""
        },
        "type": "short",
        "isArray": false
      },
      "property": "switch",
      "readOnly": false,
      "labelName": "标签4_int_开关",
      "stringBytes": 1
    }
  },
  "services": {
    "changeTemperature": {
      "isAsync": true,
      "serviceCode": "thing.setProperty(\"temperature\", input.data.boxNum);\n",
      "serviceName": "changeTemperature",
      "inputDataDefined": [
        {
          "jSON": true,
          "name": "data",
          "isArray": false,
          "dataType": 7,
          "descript": "",
          "dataModelId": 1354442140090368
        }
      ],
      "outputDataDefined": {
        "jSON": true,
        "isArray": false,
        "dataType": 4,
        "descript": "",
        "outputDataDescript": ""
      }
    }
  },
  "events": [
    {
      "name": "temperatureHight2",
      "type": "TRIGGER",
      "eventDefine": {
        "properties": [
          "temperaturePro",
          "temperature"
        ]
      }
    }
  ]
}
```
