# 连接测试

## 通道测试

参数

```json
// ChannelElm.java
[{
    "channelId": 12345, 
    "driver": "Snap7",
    "ip": "127.0.0.1",
    "port": 123,
    "rack": 0,
    "slot": 0,
    "userName": "",
    "password": "",
}]
```

响应

```json
{
    "signal": true,
    "result": [{
        "ok": true,
        "message": "",
        "channelId": 12345,
    }]
}
```


## 标签测试 & 事件测试

参数

```json
{
    "labelList": [{ // AccessElm.java
        "labelId": 123,
        "labelName": "temperatureLabel",
        "address": "DB50.BIT0",
        "dataType": "bool",
        "isArray": true,
        "length": 3,
        "stringBytes": 0,
        "channelId": 123,
    }],
    "channelList": [{ // ChannelElm.java
        "channelId": 12345, 
        "driver": "Snap7",
        "ip": "127.0.0.1",
        "port": 123,
        "rack": 0,
        "slot": 0,
        "userName": "",
        "password": "",
    }]
}
```

响应

```json
{
    "ok": true,
    "result": [{
        "ok": true,
        "message": "",
        "labelId": 12345,
        "value": "",
    }]
}
```

