

## model

格式参考阿里tsl

```json
{
    //属性，如：1:电源开关:rw:required:bool, 2:风速开关:rw:required:int:max=6,min=0
    properties: [{
        id: int, //属性唯一标识符(产品下唯一)
        name: string, //属性名称
        descript: string, //属性描述

        readOnly: true, //是否只读
        persist: true, //是否持久化
        
        bindLabel: true/false, //是否绑定标签
        dataType: { //属性类型
            type: bool/byte/short/int/float/string, //这里暂时不支持数据模型dataModel 
            isArray: false, //是否是数组
            spec: {
                defaultValue: "", //默认值
                min: 属性最小值(int,float,double类型特有),
                max: 属性最大值(int,float,double类型特有),
                unit: 属性单位,
                unitName: 单位的名称
            }
        }
    }]
    //事件
    events: [{
        id: int, //事件唯一标识符(产品下唯一)
        type: trigger, //事件类型，（trigger、change、report、fault），条件触发
        name: string, //事件名称，如temperatureExceedEvent温度过高警告事件
        descript: string, //事件描述
        required: true/false, //是否是必选事件
        method: string, //事件方法，根据id生成，作为ot向gw订阅事件的主题topic
        eventDefine: { //事件定义
            //采集字段
            properties: [
                temperature, 
                powerLevel,
            ],
            //触发条件
            trigger: "(#prop.powerLevel == #meta.powerLevel1 and #prop.temperature > #meta.temperature1 ) or (#prop.powerLevel == #meta.powerLevel2 and #prop.temperature > #meta.temperature2 )",
            faultBeginThreshold: 10, //故障开始阈值
            faultEndThreshold: 2, //故障结束阈值
            faultInput: [{ //故障入参映射
                property: "", //属性名
                input: "", //入参名
            }]
            //事件主题，由系统自动生成
            topic: /event/trigger/{thingModelId}/{event.name}/{deviceId}
        }
    }, {
        id: int, 
        type: change, //改变上报
        name: string,
        required: true/false,
        method: string, 
        eventDefine: { //事件定义
            //采集字段，数据类型不展开
            property: temperature,
            //事件主题，由系统自动生成
            topic: /event/change/{thingModelId}/{event.name}/{deviceId}
        }
    }, {
        id: int, 
        type: report, //直接上报
        name: string,
        required: true/false,
        method: string, 
        eventDefine: { //事件定义
            //采集字段
            property: temperature,
            //事件主题，由系统自动生成
            topic: /event/report/{thingModelId}/{event.name}/{deviceId}
        }
    }],
}
```

