

## 实现方案

服务的js代码，通过js引擎执行，内部实际是在运行java代码。
java代码发送http请求给网关接口代理，
网关接口代理是一个vertx的http服务，
接口代理收到请求后，会往mqtt发送消息并监听回复消息，
当收到回复消息后，才将结果放入response返回。


## input_data 数据结构：//JSON数组

```json

[
    {
        name:"",//属性名
        dataType:"",//数据类型，bool/byte/short/int/float/string/dataModel
        isArry:"",//是否是数组，0-非数组，1-数组
        descript:"",//描述
        dataModelId:"" //如果dataType是dataModel类型

    }
]
```

## output_data 数据结构：

```json

{
    dataType:"",//数据类型，bool/byte/short/int/float/string/dataModel
    isArry:"",//是否是数组，0-非数组，1-数组
    descript:"",//描述
    dataModelId:"" //如果dataType是dataModel类型
}

    -->

```

## AccessElm

```json
{
    "labelId": 123,
    "property": "temperature",
    "labelName": "",
    "address": "DB50.INT0",
    "dataType": "",
    "isArray": true,
    "length": 2,
    "stringBytes": 3,
    "access": "rw",
    "channel": {
        "channelId": 123,
        "ip": "***********",
        "port": 102,
        "rack": 0,
        "slot": 2,
    } 
}
```

## js编程模型

```js
things.getDevice('deviceId').getPropertyBbb();
things.getDevice('deviceId').setPropertyAaa(value); //如果readOnly报错
things.getDevice('deviceId').{serviceName}(param1, param2, ...);
```
