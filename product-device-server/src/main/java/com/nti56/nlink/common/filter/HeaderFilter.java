package com.nti56.nlink.common.filter;

import java.io.IOException;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import com.alibaba.fastjson.JSONObject;
import com.nti56.nlink.common.filter.wrapper.OtHeadersRequestWrapper;
import com.nti56.nlink.product.device.server.constant.Constant;

import lombok.extern.slf4j.Slf4j;


@WebFilter(urlPatterns = "/**", filterName = "oauthTokenValidFilter")
@Slf4j
@Order(0)
@Component
public class HeaderFilter extends OncePerRequestFilter {

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        OtHeadersRequestWrapper customRequest = new OtHeadersRequestWrapper(request);
        String otHeaders = request.getHeader(Constant.TENANT_HEADER);
        if (StringUtils.isNotBlank(otHeaders)) {
            customRequest.addHeader(Constant.TENANT_HEADER, otHeaders);
            //已传ot_headers参数,则直接放行
            filterChain.doFilter(customRequest, response);
            return;
        }

        // 创建一个包装了原始 HttpServletRequest 的 OtHeadersRequestWrapper 实例
        String tenantIdStr = request.getHeader(Constant.TENANT_ID);
        String moduleIdStr = request.getHeader(Constant.MODULE_ID);
        String spaceIdStr = request.getHeader(Constant.SPACE_ID);
        String engineeringIdStr = request.getHeader(Constant.ENGINEERING_ID);
        JSONObject jsonObject = new JSONObject();
        if (StringUtils.isNotBlank(tenantIdStr)) {
            try {
                long tenantId = Long.parseLong(tenantIdStr);
                jsonObject.put(Constant.TENANT_ID, tenantId);

            } catch (Exception e) {
                log.error("请求地址:{},tenantId的header参数有误:{}", request.getRequestURI(), tenantIdStr);
            }
        } else {
            log.warn("请求地址:{},缺失tenantId的header参数", request.getRequestURI());
        }

        if (StringUtils.isNotBlank(moduleIdStr)) {
            try {
                long moduleId = Long.parseLong(moduleIdStr);
                jsonObject.put(Constant.MODULE_ID, moduleId);

            } catch (Exception e) {
                log.error("请求地址:{},moduleId的header参数有误:{}", request.getRequestURI(), moduleIdStr);
            }
        } else {
            jsonObject.put(Constant.MODULE_ID, 0);
        }

        if (StringUtils.isNotBlank(engineeringIdStr)) {
            try {
                long engineeringId = Long.parseLong(engineeringIdStr);
                jsonObject.put(Constant.ENGINEERING_ID, engineeringId);

            } catch (Exception e) {
                log.error("请求地址:{},engineeringId的header参数有误:{}", request.getRequestURI(), engineeringIdStr);
            }
        } else {
            jsonObject.put(Constant.ENGINEERING_ID, 0);
        }

        if (StringUtils.isNotBlank(spaceIdStr)) {
            try {
                long spaceId = Long.parseLong(spaceIdStr);
                jsonObject.put(Constant.SPACE_ID, spaceId);

            } catch (Exception e) {
                log.error("请求地址:{},spaceId的header参数有误:{}", request.getRequestURI(), spaceIdStr);
            }
        } else {
            jsonObject.put(Constant.SPACE_ID, 0);
        }


        customRequest.addHeader(Constant.TENANT_HEADER, jsonObject.toJSONString());
        // 将请求传递给下一个过滤器或目标资源
        filterChain.doFilter(customRequest, response);

    }
}
