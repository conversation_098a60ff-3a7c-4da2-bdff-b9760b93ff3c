package com.nti56.nlink.common.util;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.BeanUtils;

import java.util.*;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @ClassName BeanUtilsIntensifier
 * @date 2022/3/18 9:04
 * @Version 1.0
 */
public class BeanUtilsIntensifier{

    public static <S,T> void propertyInjection(S s,T t){
        if (Optional.ofNullable(s).isPresent() && Optional.ofNullable(t).isPresent()) {
            BeanUtils.copyProperties(s,t);
        }
    }

    public static <T> T copyBean(Object source,Class<T> targetClass){
        T target = BeanUtils.instantiateClass(targetClass);
        if (Optional.ofNullable(source).isPresent()) {
            BeanUtils.copyProperties(source,target);
        }
        return target;
    }

    public static <T> Page<T> copyPage(Page source, Class<T> targetClass){
        List<T> list = new ArrayList<>();
        Optional.ofNullable(source.getRecords()).orElse(new ArrayList()).forEach(item ->{
            T target = BeanUtils.instantiateClass(targetClass);
            BeanUtils.copyProperties(item,target);
            list.add(target);
        });
        Page<T> page = copyBean(source, Page.class);
        return page.setRecords(list);
    }

    public static <T> Page<T> copyPage(Page source, Class<T> targetClass,Function func){
        Page<T> page = copyPage(source, targetClass);
        func.apply(page);
        return page;
    }

    public static <T> List<T> copyBeanList(Collection sourceList,Class<T> targetClass){
        List<T> result = new ArrayList<>();
        Optional.ofNullable(sourceList).orElse(new ArrayList<>()).forEach(source -> {
            T target = BeanUtils.instantiateClass(targetClass);
            BeanUtils.copyProperties(source,target);
            result.add(target);
        });
        return result;
    }

    public static Result stringToId(String stringId){
        Long id = 0L;
        try{
            id = Long.valueOf(stringId);
        }catch (NumberFormatException e){
            return Result.error(ServiceCodeEnum.CODE_PARAM_ERROR);
        }
        return Result.ok(id);
    }

//    /**
//     * 参数
//     * @param bean 要校验的对象
//     * @param properties 要校验对象的参数
//     * @param <B> 要校验的对象类型
//     * @return true - 参数不合格，false - 参数合格
//     */
//    public static <B,R> R[] beanPropertiesDo(B bean, Function ... properties){
//        if (Optional.ofNullable(bean).isPresent() && Optional.ofNullable(properties).isPresent()) {
//
//        }
//        return new R[];
//    }

    /**
     * 参数
     * @param bean 要校验的对象
     * @param properties 要校验对象的参数
     * @param <B> 要校验的对象类型
     * @return true - 参数不合格，false - 参数合格
     */
    public static <B> Boolean checkBeanAndProperties(B bean,Function<B,?> ... properties) {
        if (Optional.ofNullable(bean).isPresent() && Optional.ofNullable(properties).isPresent()) {
            for (Function property : properties) {
                if (!Optional.ofNullable(property.apply(bean)).isPresent()) {
                    return true;
                }
            }
            return false;
        }
        return true;
    }

    /**
     *
     * @param bean
     * @param getChildren
     * @param getId
     * @param <B>
     * @param <C>
     * @return
     */
    public static <B,C> List<Long> getChildrenIds(B bean, Function<B,List<C>> getChildren, Function<C,Long> getId) {
        return getChildrenParams(bean,getChildren,getId);
    }

    public static <C> Set<Long> getIdSet(List<C> list,Function<C,Long> getId) {
        Set<Long> somethings = new HashSet<>();
        Optional.ofNullable(list).orElse(new ArrayList<C>()).forEach(item -> {
            Long something = getId.apply(item);
            somethings.add(something);
        });
        return somethings;
    }

    public static <C> List<Long> getIds(List<C> list,Function<C,Long> getId) {
        return getSomething(list,getId);
    }

    public static <V,R> List<String> getIds(Map<String, V> map, Function<V,R> getId) {
        return getSomething2String(map,getId);
    }

    public static <C> List<String> getIds2String(List<C> list,Function<C,Long> getId) {
        return getSomething2String(list,getId);
    }

    public static <B,C,R> List<R> getChildrenParams(B bean,Function<B,List<C>> getChildren,Function<C,R> getFunc) {
        List<R> params = new ArrayList<>();
        if (Optional.ofNullable(bean).isPresent()) {
            params = getSomething(getChildren.apply(bean),getFunc);
        }
        return params;
    }

    public static <C,R,RC extends Collection<R>> void getSomething2Collection(Function<C,Boolean> condition, List<C> list, Function<C,R> getFunc, RC rc) {
        Optional.ofNullable(list).orElse(new ArrayList<C>()).forEach(item -> {
            if (condition.apply(item)) {
                R something = getFunc.apply(item);
                rc.add(something);
            }
        });
    }

    public static <C,R,RC extends Collection<R>> void getSomething2Collection(List<C> list, Function<C,R> getFunc, RC rc) {
        Optional.ofNullable(list).orElse(new ArrayList<C>()).forEach(item -> {
            R something = getFunc.apply(item);
            rc.add(something);
        });
    }

    public static <C,R> List<R> getSomething(List<C> list, Function<C,R> getFunc) {
        List<R> somethings = new ArrayList<>();
        Optional.ofNullable(list).orElse(new ArrayList<C>()).forEach(item -> {
            R something = getFunc.apply(item);
            somethings.add(something);
        });
        return somethings;
    }

    public static <C,R> List<String> getSomething2String(List<C> list, Function<C,R> getFunc) {
        List<String> somethings = new ArrayList<>();
        Optional.ofNullable(list).orElse(new ArrayList<C>()).forEach(item -> {
            if (Optional.ofNullable(getFunc).isPresent()) {
                R something = getFunc.apply(item);
                somethings.add(something.toString());
            }else {
                somethings.add(item.toString());
            }
        });
        return somethings;
    }


    public static <R, K, V> List<R> getSomething(Map<K, V> map, Function<V,R> getFunc) {
        List<R> somethings = new ArrayList<>();
        Optional.ofNullable(map).orElse(new HashMap<>()).forEach((k,v) ->{
            R something = getFunc.apply(v);
            somethings.add(something);
        });
        return somethings;
    }

    public static <R, K, V> List<String> getSomething2String(Map<K, V> map, Function<V,R> getFunc) {
        List<String> somethings = new ArrayList<>();
        Optional.ofNullable(map).orElse(new HashMap<>()).forEach((k,v) ->{
            R something = getFunc.apply(v);
            somethings.add(something.toString());
        });
        return somethings;
    }

    public static <K,V> Map<K,V> collection2Map(Collection<V> collection,Function<V,K> getKey){
        Map<K,V> map = new HashMap<>();
        if (Optional.ofNullable(collection).isPresent() && collection.size() > 0) {
            collection.forEach( v -> {
                K k = getKey.apply(v);
                map.put(k,v);
            });
        }
        return map;
    }

    public static <K,T,V> Map<K,V> collection2Map(Collection<T> collection,Function<T,K> getKey,Function<T,V> getValue){
        Map<K,V> map = new HashMap<>();
        if (Optional.ofNullable(collection).isPresent() && collection.size() > 0) {
            collection.forEach( t -> {
                K k = getKey.apply(t);
                V v = getValue.apply(t);
                map.put(k,v);
            });
        }
        return map;
    }

}
