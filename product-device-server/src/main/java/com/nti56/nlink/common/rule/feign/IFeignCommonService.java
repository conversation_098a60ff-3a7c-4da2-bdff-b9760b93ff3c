/*
package com.nti56.nlink.common.rule.feign;

import com.nti56.nlink.common.rule.model.dto.RedirectReferenceDto;
import com.nti56.nlink.common.util.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

*/
/**
 * common-server
 *//*

@FeignClient(name = "common-server")
public interface IFeignCommonService {

    @GetMapping(value = "redirect/redirectCount", produces = MediaType.APPLICATION_JSON_VALUE)
    R redirectCount(@RequestHeader("ot_headers") String tenantIsolation);

    @PostMapping(value = "redirect/updateReference", produces = MediaType.APPLICATION_JSON_VALUE)
    R updateReference(@RequestHeader("ot_headers") String tenantIsolation, @RequestBody RedirectReferenceDto redirectReferenceDto);

}
*/
