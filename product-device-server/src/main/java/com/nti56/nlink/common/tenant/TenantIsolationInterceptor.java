package com.nti56.nlink.common.tenant;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import com.nti56.nlink.common.dto.TenantIsolation;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/5/10 13:24<br/>
 * @since JDK 1.8
 */

@Slf4j
@Component
public class TenantIsolationInterceptor implements HandlerInterceptor {

    private static final String DEFAULT_ID  = "0";

    private static final String ENGINEERING_ID = "engineeringId";

    private static final String SPACE_ID = "spaceId";

    private static final String MODULE_ID = "moduleId";

    private static final String TENANT_ID = "tenantId";

    private static final String TENANT_ISOLATION_DATA = "ot_headers";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String headerJson = request.getHeader(TENANT_ISOLATION_DATA);
        log.debug("请求头："+headerJson);
        TenantIsolation tenantIsolation = JSON.parseObject(headerJson, TenantIsolation.class);
        if (tenantIsolation == null){
            tenantIsolation = new TenantIsolation();
        }
        TenantIsolationThreadLocal.set(tenantIsolation);
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        TenantIsolationThreadLocal.remove();
    }
}
