package com.nti56.nlink.common.rule.feign;

import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * 导出规则引擎
 */
//@FeignClient(name = "auth-server")
//public interface IFeignAuthService {
//  @PostMapping(value = "export/auth", produces = MediaType.APPLICATION_JSON_VALUE)
//  R export(@RequestBody TenantIsolation tenantIsolation);
//
//}
