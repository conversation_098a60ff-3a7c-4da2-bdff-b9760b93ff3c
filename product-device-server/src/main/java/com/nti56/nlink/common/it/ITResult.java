package com.nti56.nlink.common.it;

public class ITResult<T> implements java.io.Serializable {

    private static final long serialVersionUID = -6525861904109424436L;

    public ITResult() {

    }

    public ITResult(T data, int code, String message, String serviceErrMsg) {
        this.data = data;
        this.code = code;
        this.message = message;
        this.serviceErrMsg = serviceErrMsg;
    }

    /**
     * 响应数据
     */
    private T data;

    /**
     * 响应编码
     */
    private int code;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 异常信息
     */
    private String serviceErrMsg;

    public static final Integer SUCCESS_CODE = 0;
    public static final Integer ERROR_CODE = -1;
    public static final Integer WARN_CODE = -2;
    public static final Integer INFO_CODE = 1;

    public static final String SUCCESS_MSG = "success";

    /**
     * @param msg 消息
     * @param <T> 数据对象类型
     * @return 返回结果
     */
    public static <T> ITResult<T> succeed(String msg) {

        return succeedWith(null, SUCCESS_CODE, msg);
    }

    public static <T> ITResult<T> succeed(T model, String msg) {

        return succeedWith(model, SUCCESS_CODE, msg);
    }

    public static <T> ITResult<T> succeed(T model) {

        return succeedWith(model, SUCCESS_CODE, SUCCESS_MSG);
    }

    public static <T> ITResult<T> succeedWith(T data, Integer code, String msg) {

        return new ITResult<>(data, code, msg, null);
    }

    public static <T> ITResult<T> fail(String msg) {

        return failWith(null, ERROR_CODE, msg, null);
    }

    public static <T> ITResult<T> fail(Integer code, String msg) {

        return failWith(null, code, msg, null);
    }

    public static <T> ITResult<T> fail(Integer code, String msg, String serviceErrMsg) {

        return failWith(null, code, msg, serviceErrMsg);
    }

    public static <T> ITResult<T> failWith(T data, Integer code, String msg, String serviceErrMsg) {

        return new ITResult<>(data, code, msg, serviceErrMsg);
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getServiceErrMsg() {
        return serviceErrMsg;
    }

    public void setServiceErrMsg(String serviceErrMsg) {
        this.serviceErrMsg = serviceErrMsg;
    }
}
