package com.nti56.nlink.common.rule.feign;

import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 导出规则引擎
 */
//@FeignClient(name = "user-server")
//public interface IFeignUserService {
//
//  @PostMapping(value = "export/user", produces = MediaType.APPLICATION_JSON_VALUE)
//  R export(@RequestBody TenantIsolation tenantIsolation);
//
//
//  @GetMapping(value = "tenant/listAllTenant", produces =  MediaType.APPLICATION_JSON_VALUE)
//  R listAllTenant();
//
//}
