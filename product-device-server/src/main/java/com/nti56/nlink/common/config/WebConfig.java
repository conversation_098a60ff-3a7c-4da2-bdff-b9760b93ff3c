package com.nti56.nlink.common.config;

import com.nti56.nlink.common.filter.HeaderFilter;
import com.nti56.nlink.common.tenant.TenantIsolationInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;


/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/5/10 13:34<br/>
 * @since JDK 1.8
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Autowired
    private TenantIsolationInterceptor tenantIsolationInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(tenantIsolationInterceptor)
                .excludePathPatterns("/v3/**")
                .addPathPatterns(
                        "/**"
                );
    }
}
