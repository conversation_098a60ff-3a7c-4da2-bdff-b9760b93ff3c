package com.nti56.nlink.common.rule.feign;

import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.rule.model.RuleParam;
import com.nti56.nlink.common.util.Result;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-05-26 09:26:27
 * @since JDK 1.8
 */
public interface IRuleTasksService {

    //物理删规则计算任务，然后创建计算任务
    Result<Void> upsertRuleTask(RuleParam ruleParam, TenantIsolation tenantIsolation);

    //软删除计算任务
    Result<Void> stopRuleTask(Long instanceId, TenantIsolation tenantIsolation);

    //同步任务到网关
    Result<Void> syncRuleTask(Long instanceId, TenantIsolation tenantIsolation);

    //启用计算任务
    Result<Void> enableRuleTask(Long instanceId, TenantIsolation tenantIsolation);

    //硬删除计算任务
    Result<Void> deleteRuleTask(Long instanceId, TenantIsolation tenantIsolation);

}
