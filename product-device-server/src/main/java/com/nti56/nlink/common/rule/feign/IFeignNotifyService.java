package com.nti56.nlink.common.rule.feign;

import com.nti56.nlink.product.device.server.service.IFeignExportService;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.R;

/**
 * 导出通知
 */
//@FeignClient(name = "notify-server")
//public interface IFeignNotifyService {
//
//  @PostMapping(value = "export/notify", produces = MediaType.APPLICATION_JSON_VALUE)
//  R export(@RequestBody TenantIsolation tenantIsolation);
//
//
//}
