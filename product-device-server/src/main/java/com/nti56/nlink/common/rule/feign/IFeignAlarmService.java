package com.nti56.nlink.common.rule.feign;

import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.rule.model.WarningLogParam;
import com.nti56.nlink.common.util.R;
import com.nti56.nlink.common.util.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

/**
 * 导出通知
 */
//@FeignClient(name = "alarm-server")
//public interface IFeignAlarmService {
//
//    @PostMapping(value = "export/alarm", produces = MediaType.APPLICATION_JSON_VALUE)
//    R export(@RequestBody TenantIsolation tenantIsolation);
//
//    @GetMapping(value = "warning/workConsoleData", produces =  MediaType.APPLICATION_JSON_VALUE)
//    R getWorkConsoleData(@RequestHeader("ot_headers") String tenantIsolation);
//
//    @PostMapping(value = "warning", produces = MediaType.APPLICATION_JSON_VALUE)
//    Result<Void> addWarningLog(@RequestHeader("ot_headers") String tenantIsolation, @RequestBody WarningLogParam warningLogParam);
//
//    @PostMapping(value = "warning/updateFaultEndTime", produces = MediaType.APPLICATION_JSON_VALUE)
//    Result<Void> updateFaultEndTime(@RequestHeader("ot_headers") String tenantIsolation, @RequestBody WarningLogParam warningLogParam);
//
//
//}
