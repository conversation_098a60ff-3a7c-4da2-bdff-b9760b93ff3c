package com.nti56.nlink.common.mybatis;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.base.Stopwatch;
import com.nti56.nlink.product.device.server.exception.BizException;
import com.nti56.nlink.product.device.server.exception.ServiceDisableInvokeException;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName BaseServiceImpl
 * @date 2022/4/25 13:35
 * @Version 1.0
 */
@Slf4j
public class BaseServiceImpl<M extends CommonMapper<T>, T> extends ServiceImpl<M,T> implements IBaseService<T>{


    @Deprecated
    @SneakyThrows
    @Override
    public T getOne(Wrapper<T> queryWrapper, boolean throwEx) {
        throw new ServiceDisableInvokeException();
    }

    @Deprecated
    @SneakyThrows
    @Override
    public Map<String, Object> getMap(Wrapper<T> queryWrapper) {
        throw new ServiceDisableInvokeException();
    }

    @Deprecated
    @SneakyThrows
    @Override
    public <V> V getObj(Wrapper<T> queryWrapper, Function<? super Object, V> mapper) {
        throw new ServiceDisableInvokeException();
    }

    @Override
    @Transactional(rollbackFor = {Exception.class})
    public boolean insertBatchSomeColumn(Collection<T> entityList, int batchSize) {
        Stopwatch sw = Stopwatch.createStarted();
        try {
            int size = entityList.size();
            int idxLimit = Math.min(batchSize, size);
            int i = 1;
            //保存单批提交的数据集合
            List<T> oneBatchList = new ArrayList<>();
            String name = "";
            for (Iterator<T> var7 = entityList.iterator(); var7.hasNext(); ++i) {
                T element = var7.next();
                if (StringUtils.isBlank(name)){
                    name = element.getClass().getName();
                }
                oneBatchList.add(element);
                if (i == idxLimit) {
                    baseMapper.insertBatchSomeColumn(oneBatchList);
                    //每次提交后需要清空集合数据
                    oneBatchList.clear();
                    idxLimit = Math.min(idxLimit + batchSize, size);
                }
            }
            log.info(">>>>>>>>>>>>>>>>>>>>>>>>>>>>> "+name+" :" + entityList.size() + ":" + sw.elapsed(TimeUnit.SECONDS) + "s<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<");
            sw.stop();
        } catch (Exception e) {
            throw new BizException(e.getMessage());
        }
        return true;
    }


    @Override
    @Transactional(rollbackFor = {Exception.class})
    public boolean insertBatchSomeColumn(Collection<T> entityList) {
        return this.insertBatchSomeColumn(entityList,50000);
    }

}
