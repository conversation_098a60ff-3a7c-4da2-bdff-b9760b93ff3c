package com.nti56.nlink.common.rule.model;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 类说明：
 *
 * @ClassName WarningLogParam
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/7/11 13:50
 * @Version 1.0
 */

@Data
@Builder
public class WarningLogParam implements Serializable {

    /**
     * 来源类型 0-规则实例
     */
    private Integer sourceType;

    /**
     * 来源名称 0-实例名称
     */
    private String sourceName;

    /**
     * 触发实例id
     */
    private Long instanceId;

    /**
     * 告警名称
     */
    private String warnName;

    /**
     * 告警内容
     */
    private String warnContent;

    /**
     * 告警级别 1一般 2告警 3严重 4 致命
     */
    private Integer warnLevel;

    /**
     * 触发类型 1-定时触发 2-触发
     */
    private Integer triggerType;


    /**
     * 触发模式 1-属性触发 2-事件触发
     */
    private Integer triggerModel;

    /**
     * 触发条件
     */
    private String triggerCondition;

    /**
     * 触发时间
     */
    private Date occurTime;




}
