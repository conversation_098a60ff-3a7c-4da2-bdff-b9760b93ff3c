package com.nti56.nlink.common.rule.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2023/3/8 11:59<br/>
 * @since JDK 1.8
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class RuleEngineWorkConsoleDto {
   
    private Integer dailyTriggerCount;
    
    private Integer ruleInstanceCount;
    
    private Integer ruleEngineCount;
}
