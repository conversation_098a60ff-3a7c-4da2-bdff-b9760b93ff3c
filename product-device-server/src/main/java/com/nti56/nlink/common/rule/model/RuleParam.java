package com.nti56.nlink.common.rule.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-05-26 09:26:35
 * @since JDK 1.8
 */
@Data
@Schema(description = "创建采集任务参数")
public class RuleParam {
    
    @Schema(description = "实例id")
    private Long instanceId;
    @Schema(description = "属性列表")
    private List<RulePropertyItem> properties;
    @Schema(description = "推送主题")
    private String topic;

}
