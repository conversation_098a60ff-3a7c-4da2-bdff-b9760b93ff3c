package com.nti56.nlink.common.mybatis;

import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Collection;

/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName IBaseService
 * @date 2022/4/25 14:19
 * @Version 1.0
 */
public interface IBaseService<T> extends IService<T> {
    boolean insertBatchSomeColumn(Collection<T> entityList);

    boolean insertBatchSomeColumn(Collection<T> entityList, int batchSize);

}
