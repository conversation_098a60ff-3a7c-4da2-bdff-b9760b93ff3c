package com.nti56.nlink.common.filter.wrapper;


import com.alibaba.fastjson.JSONObject;
import com.nti56.nlink.product.device.server.constant.Constant;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.util.Collections;
import java.util.Enumeration;
import java.util.LinkedHashMap;
import java.util.Map;

@Slf4j
public class OtHeadersRequestWrapper extends HttpServletRequestWrapper {

    private final Map<String, String> headers = new LinkedHashMap<>();

    public OtHeadersRequestWrapper(HttpServletRequest request) {
        super(request);
    }

    public void addHeader(String name, String value) {
        headers.put(name, value);
    }

    @Override
    public String getHeader(String name) {

        if (headers.containsKey(name)) {
            return headers.get(name);
        }
        // 如果没有，则返回原始请求中的 header
        return super.getHeader(name);
    }

    @Override
    public Enumeration<String> getHeaders(String name) {
        // 如果有自定义的 header，则只返回自定义的 header
        if (headers.containsKey(name)) {
            return new Enumeration<String>() {
                private boolean hasMore = true;

                @Override
                public boolean hasMoreElements() {
                    return hasMore;
                }

                @Override
                public String nextElement() {
                    hasMore = false;
                    return headers.get(name);
                }
            };
        }
        // 否则返回原始请求中的 header
        return super.getHeaders(name);
    }

    @Override
    public Enumeration<String> getHeaderNames() {
        // 返回所有 header 名称，包括自定义的 header
        Enumeration<String> originalNames = super.getHeaderNames();
        Map<String, Object> names = new LinkedHashMap<>();
        while (originalNames.hasMoreElements()) {
            String name = originalNames.nextElement();
            names.put(name, null);
        }
        for (String name : headers.keySet()) {
            names.put(name, null);
        }
        return Collections.enumeration(names.keySet());
    }
}