package com.nti56.nlink.common.tenant;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.nti56.model.BaseModel;
import com.nti56.nlink.common.util.JwtUserInfoUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2021/1/11 10:27 <br/>
 * @since JDK1.8
 */

@Slf4j
@Component
public class MyMetaObjectHandler implements MetaObjectHandler {


    @Override
    public void insertFill(MetaObject metaObject) {


        String userJson = JwtUserInfoUtils.userJson();

        if (StringUtils.isNotBlank(userJson)) {

            JSONObject jsonObject = JSON.parseObject(userJson);
            Long userId = jsonObject.getLong("userId");
            String userName = jsonObject.getString("realname");
            String from = JwtUserInfoUtils.getRequest().getHeader("from");
            if("it".equals(from)){
                String userInfo = jsonObject.getString("userInfo");
                JSONObject userInfoObject = JSON.parseObject(userInfo);
                userId = userInfoObject.getLong("id");
                userName = userInfoObject.getString("empName");
            }
            this.strictInsertFill(metaObject, "creatorId", Long.class, userId)
                    .strictInsertFill(metaObject, "creator", String.class, userName)
                    .strictInsertFill(metaObject, "updatorId", Long.class, userId)
                    .strictInsertFill(metaObject, "updator", String.class, userName);
        }

        if (metaObject.getValue("createTime") == null) {
            this.strictInsertFill(metaObject, "createTime", LocalDateTime.class, LocalDateTime.now());
        }
        try {
            if (metaObject.getValue("updateTime") == null) {
                this.strictInsertFill(metaObject, "updateTime", LocalDateTime.class, LocalDateTime.now());
            }
        } catch (Exception e) {

        }

        this.strictInsertFill(metaObject, "tenantId", Long.class, TenantIsolationThreadLocal.getTenantId());
        this.strictInsertFill(metaObject, "engineeringId", Long.class, TenantIsolationThreadLocal.getEngineeringId());
        this.strictInsertFill(metaObject, "moduleId", Long.class, TenantIsolationThreadLocal.getModuleId());
        this.strictInsertFill(metaObject, "spaceId", Long.class, TenantIsolationThreadLocal.getSpaceId());
    }


    @Override
    public void updateFill(MetaObject metaObject) {
        String userJson = JwtUserInfoUtils.userJson();
        if (StringUtils.isNotBlank(userJson)) {
            com.alibaba.fastjson.JSONObject userJsonObject = JSON.parseObject(userJson);
            Long userId = userJsonObject.getLong("userId");
            String userName = userJsonObject.getString("realname");
            String from = JwtUserInfoUtils.getRequest().getHeader("from");
            if("it".equals(from)){
                String userInfo = userJsonObject.getString("userInfo");
                JSONObject userInfoObject = JSON.parseObject(userInfo);
                userId = userInfoObject.getLong("id");
                userName = userJsonObject.getString("empName");
            }
            this.setFieldValByName("updatorId", userId, metaObject)
                    .setFieldValByName("updator", userName, metaObject);
        }
        this.setFieldValByName("updateTime", LocalDateTime.now(), metaObject);
    }

    /**
     * @param metaObject
     * @param model
     * @param type       1 insert 2 update
     */
    private void systemFill(MetaObject metaObject, BaseModel model, int type) {
        switch (type) {
            case 1:
                if (model.getCreatorId() == null) {
                    this.strictInsertFill(metaObject, "creatorId", Long.class, 1L);
                }
                if (model.getUpdatorId() == null) {
                    this.strictInsertFill(metaObject, "updatorId", Long.class, 1L);
                }
                if (model.getCreator() == null) {
                    this.strictInsertFill(metaObject, "creator", String.class, "test");
                }
                if (model.getUpdator() == null) {
                    this.strictInsertFill(metaObject, "updator", String.class, "test");
                }
                break;
            case 2:
                this.strictUpdateFill(metaObject, "updatorId", Long.class, 1L);
                this.strictUpdateFill(metaObject, "updator", String.class, "test");
                break;
            default:

        }


    }
}