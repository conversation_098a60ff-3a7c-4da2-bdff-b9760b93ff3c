package com.nti56.nlink.common.util;

import lombok.Getter;

/**
 * 类说明: 服务码枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-17 10:05:02
 * @since JDK 1.8
 */
public enum ServiceCodeEnum {
    // 1-Snap7，2-Modbus，3-OPC UA，4-<PERSON>ig<PERSON><PERSON>，5-BLE
    OK(1, "操作成功", "业务正常"),

    CODE_UNKNOWN_ERROR(99, "未知错误", "程序抛出未知异常"),
    CODE_TEL_NOT_BIND(98, "未绑定手机号", "未绑定手机号"),
    CODE_USER_NOT_EXIST(97, "用户不存在", "用户不存在"),
    CODE_BIZ_ERROR(96, "业务错误", "业务错误"),
    CODE_ALREADY_LOGIN(95, "用户已经登录", "用户已经登录"),
    CODE_GOTO_REGIST(94, "请跳转到注册页", "请跳转到注册页"),

    CODE_PARAM_ERROR(2, "参数异常，请求失败", "参数异常，不想具体透露信息时统一返回"),
    CODE_DELETE_FAIL(3, "删除数据失败", "删除数据失败或数据已删除"),
    CODE_UPDATE_FAIL(4, "更新数据失败", "更新数据失败"),
    CODE_GET_FAIL(5, "获取数据失败", "获取数据失败"),
    CODE_GET_PAGE_FAIL(6, "获取分页失败", "获取分页失败"),
    CODE_GET_LIST_FAIL(7, "获取列表失败", "获取列表失败"),
    CODE_CREATE_FAIL(8, "新增失败", "数据插入失败"),
    TENANT_ERROR(9, "用户权限错误", "租户横向越权"),
    //网关通道相关,
    CODE_GET_CHANNEL_FAIL(5009, "网关不存在", "网关不存在"),
    CODE_EDGE_GATEWAY_CHANNEL_BING(5010, "网关通道已绑定", "网关通道已绑定"),

    //标签相关,
    LABEL_NAME_REPEAT(1201, "标签分组下已存在同名标签", "标签分组下已存在同名标签"),
    LABEL_GROUP_NAME_REPEAT(1202, "同级分组下已存在同名分组", "同级分组下已存在同名分组"),
    LABEL_INPUT_FORMAT_ERROR(1204, "标签名称导入格式错误", "标签名称导入格式错误"),
    LABEL_GROUP_LEVEL_NAME_ERROR(1205, "导入标签分组名称不能以'.'开头和结束，不能存在空格和两个及以上连续的字符，且从第一级到最后一级分组总长度不能超过128个字符'.'，请检查后修改重试！", "导入标签分组名称不能以'.'开头和结束，不能存在空格和两个及以上连续的'.'，请检查后修改重试！"),
    LABEL_GROUP_NAME_ERROR(1206, "标签分组名称不能存在空格和'.',且从第一级到最后一级总长度不能超过128个字符，请检查后修改重试！", "标签分组名称不能存在空格和'.',且从第一级到最后一级总长度不能超过128个字符，请检查后修改重试！"),
    LABEL_NAME_ERROR(1207, "标签名称只能由英文字母开头，支持英文字母、数字、下划线,且不能超过128个字符", "标签名称只能由英文字母开头，支持英文字母、数字、下划线"),
    INPUT_LABEL_NAME_REPEAT(1208, "请勿导入同名标签！", "请勿导入同名标签！"),
    LABEL_ALIAS_NAME_ERROR(1209, "标签别名不能超过256个字符", "标签别名不能超过256个字符"),
    DEVICE_LABEL_BING_FAIL(1101,"标签绑定失败","标签校验失败"),
    DEVICE_LABEL_NULL(1102,"请传入标签","标签不能为空"),
    DEVICE_LABEL_ADDRESS_NULL(1103,"请为标签设置地址","标签缺少地址"),
    DEVICE_LABEL_ADDRESS_ERROR(1104,"标签地址格式错误,ID:{0, number, #},address:{1},其他信息：{2}","标签地址格式错误"),
    DEVICE_LABEL_IS_ARRAY_NULL(1105,"请为标签设置是否是数组，ID:{0, number, #}","标签缺少是否数组"),
    DEVICE_LABEL_IS_ARRAY_ERROR(1106,"标签是否数组数据错误，labelId:{0, number, #},isArray:{1}","标签是否数组数据错误"),
    DEVICE_LABEL_DATATYPE_NULL(1107,"请为标签设置数据类型","标签缺少数据类型"),
    DEVICE_LABEL_DATATYPE_ERROR(1108,"标签数据类型错误,ID:{0, number, #},dataType:{1}","标签数据类型错误"),
    DEVICE_LABEL_LENGTH_NULL(1109,"请为标签设置数组长度,ID:{0, number, #}","标签缺少数组长度"),
    DEVICE_LABEL_LENGTH_ERROR(1110,"如果是数组请为标签设置大于1的数组长度，如果不是请让数组长度为1,ID:{0, number, #},length:{1}","如果是数组，标签数组类型数组长度必须大于1"),
    DEVICE_LABEL_STRING_BYTES_ERROR(1111,"字符串类型标签需要指定字符串长度,ID:{0, number, #}","标签字符串类型数据需要指定字符串长度stringBytes"),
    DEVICE_METADATA_ERROR(1112,"此更新导致元数据与物模型不匹配，请修改物模型或者元数据","此更新导致元数据与物模型不匹配，请修改物模型或者元数据"),
    DEVICE_LABEL_THING_MODEL_NULL(1113,"属性所属物模型为空","属性所属物模型为空"),
    DEVICE_LABEL_PARAM_NULL(1114, "绑定标签属性为空", "绑定标签属性为空"),
    DEVICE_LABEL_THING_MODEL_ERROR(1115, "物模型定义错误", "设备物模型定义错误"),
    DEVICE_LABEL_PROPERTY_MISMATCH(1116, "标签与属性不匹配", "标签与属性不匹配"),
    DEVICE_LABEL_UNBIND(1117, "设备未绑定标签", "设备未绑定标签"),
    PROPERTY_IS_BIND(1118, "该属性已经绑定事件，不可删除或修改数据类型及只读定义", "该属性已经绑定事件，不可变更"),
    PROPERTY_UNDEFINED(1119, "属性未定义", "属性未定义"),
    EVENT_PROPERTY_IS_UNBIND(1120, "事件属性未绑定标签，属性名：{}", "事件属性未绑定标签"),
    MODBUS_DEVICE_LABEL_ADDRESS_ERROR(1121,"标签地址格式错误,{}","标签地址格式错误"),
    DEVICE_LABEL_READ_ONLY_NULL(1122, "请为标签设置是否只读", "请为标签设置是否只读"),
    LABEL_EXIST_BIND(1123, "标签已绑定其他资源", "标签已绑定其他资源"),

    DEVICE_THING_MODEL_UNDEFINED(1000, "资源模型未定义", "设备领域初始化时未找到模型定义"),
    DEVICE_PRODUCT_NO_FIND(1001, "未找到产品" , "未找到产品"),
    DEVICE_THING_MODEL_PROPERTY_UNDEFINED(1002, "资源模型属性未定义", "设备领域初始化时未找到模型属性定义"),
    DEVICE_EDGE_GATEWAY_NO_FIND(1003, "未找到网关", "未找到网关"),

    THING_SERVICE_OVERRIDE_ERROR(2000, "方法覆盖错误", "方法覆盖错误"),
    THING_SERVICE_UNDEFINED(2001, "物服务未定义", "物服务未定义"),
    SERVICE_ENGINE_CREATE_FAIL(2002, "服务引擎创建失败", "服务引擎创建失败"),
    THING_SERVICE_CODE_ERROR(2003, "物服务代码错误:{0}", "物服务代码错误"),
    THING_SERVICE_INVOKE_FAIL(2004, "服务调用异常", "禁止服务自身递归调用"),
    THING_SERVICE_NAME_ERROR(2005, "服务名称格式错误，只支持英文数字下划线，且英文首字母", "服务名称格式错误，只支持英文数字下划线，且英文首字母"),
    DEVICE_UNDEFINED(2006, "设备未定义", "设备未定义"),
    THING_SERVICE_INVOKE_JS_CODE_FAIL(2007,"物服务代码执行异常：{0}", "物服务代码执行异常"),
    THING_SERVICE_PARAM_TYPE_ERROR(2008, "参数类型错误", "参数类型错误"),
    DEVICE_NO_ACTIVATION(2009, "设备未激活", "设备未激活"),
    DEVICE_ONLINE(2010, "注意：设备正在运行，无法执行该操作", "注意：设备正在运行，无法执行该操作"),
    DEVICE_EDGE_GATEWAY_UPDATE_ERROR(2011, "设备无法修改关联网关", "设备无法修改关联网关"),
    DEVICE_PRODUCT_UPDATE_ERROR(2012, "设备无法修改关联产品", "设备无法修改关联产品"),
    SERVICE_INPUT_NAME_REPEAT(2013, "服务入参重名", "服务入参重名"),
    SERVICE_NAME_REVISE_ERROR(2014, "服务名称不可修改", "服务名称不可修改"),
    THING_SERVICE_ARRAY_PARAM_ERROR(2015, "服务入参为数组时，元素不能为空,且元素数据类型要一致！", "服务入参为数组时，元素不能为空,且元素数据类型要一致！"),
    DEVICE_NOT_ONLINE(2016, "设备未定义或者未上线", "设备未上线"),
    DEVICE_RESOURCE_UNDEFINED(2017, "设备资源未定义", "设备资源未定义"),
    DEVICE_OFFLINE_ERROR(2018, "设备停用失败", "设备停用失败"),
    DEVICE_SYNC_ERROR(2019, "设备同步失败", "设备同步失败"),
    DEVICE_OPTION_ERROR(2020, "设备操作失败", "设备操作失败"),

    SERVICE_CREATE_FAIL(3000, "SPI获取服务时服务创建失败", "SPI获取服务时服务创建失败"),
    SUBSCRIPTION_CODE_ERROR(3100, "订阅数据转换代码错误！，错误信息：{0}","订阅数据转换代码错误！" ),

    CHANNEL_PARAMS_FORMAT_FAIL(4000, "通道参数格式异常", "通道参数格式异常"),
    CHANNEL_NO_LABEL_BINDING(4001,"通道中没有一个标签绑定属性","通道中没有一个标签绑定属性"),
    CHANNEL_INTERVAL_ERROR(4002, "通道轮询间隔不能小于10ms", "通道轮询间隔不能小于10ms"),
    WEBSOCKET_THREAD_POOL_FULL(4101, "后台繁忙请稍后重试！", "websocket线程池满！"),

    EDGE_GATEWAY_OFFLINE(5000, "网关不在线！", "网关不在线"),

    IS_RUNTIME(6000, "发布失败，只能在平台发布版本", "运行时禁用发布版本"),
    BASE_PACKAGE_NOT_EXIST(6001, "发布失败，正在更新主版本，请稍后重试", "主版本路径为空"),

    TAG_BIND_ERROR(7000, "绑定标签失败", "绑定标签失败"),
    
    CONNECTOR_CODE_ERROR(3100, "连接器处理转换代码错误！，错误信息：{0}","连接器处理转换代码错误！" );
    
    
    @Getter
    private Integer code;

    @Getter
    private String message;

    @Getter
    private String descript;

    ServiceCodeEnum(Integer code, String message, String descript) {
        this.code = code;
        this.message = message;
        this.descript = descript;
    }

    public static ServiceCodeEnum typeOfValue(Integer code){
        ServiceCodeEnum[] values = ServiceCodeEnum.values();
        for (ServiceCodeEnum v : values) {
            if (v.code.equals(code)) {
                return v;
            }
        }
        return null;
    }

    public static ServiceCodeEnum typeOfName(String message){
        ServiceCodeEnum[] values = ServiceCodeEnum.values();
        for (ServiceCodeEnum v : values) {
            if (v.message.equals(message)) {
                return v;
            }
        }
        return null;
    }

    public static ServiceCodeEnum typeOfNameDesc(String descript){
        ServiceCodeEnum[] values = ServiceCodeEnum.values();
        for (ServiceCodeEnum v : values) {
            if (v.descript.equals(descript)) {
                return v;
            }
        }
        return null;
    }
}
