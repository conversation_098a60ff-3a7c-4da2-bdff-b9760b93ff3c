package com.nti56.nlink.common.exception;

import com.nti56.nlink.common.util.ServiceCodeEnum;
import lombok.Getter;

/**
 * 类说明: 自定义运行时业务异常
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-04-27 09:41:28
 * @since JDK 1.8
 */
public class BizException extends RuntimeException {

    @Getter
    private int code;
    
    public BizException() {
        super();
        this.code = ServiceCodeEnum.CODE_UNKNOWN_ERROR.getCode();
    }

    public BizException(String message) {
        super(message);
        this.code = ServiceCodeEnum.CODE_UNKNOWN_ERROR.getCode();
    }

    public BizException(int code, String message) {
        super(message);
        this.code = code;
    }

    public BizException(ServiceCodeEnum serviceCode) {
        super(serviceCode.getMessage());
        this.code = serviceCode.getCode();
    }
}
