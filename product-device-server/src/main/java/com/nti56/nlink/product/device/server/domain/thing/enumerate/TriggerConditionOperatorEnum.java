package com.nti56.nlink.product.device.server.domain.thing.enumerate;

import lombok.Getter;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-05-26 09:00:30
 * @since JDK 1.8
 */
public enum TriggerConditionOperatorEnum {
    GT(">", "大于", "GreaterThen", ">"),
    LT("<", "小于", "LessThen", "<"),
    GE(">=", "大于等于", "GreaterEqual", ">="),
    LE("<=", "小于等于", "LessEqual", "<="),
    EQ("=", "等于", "Equal", "=="),
    NE("!=", "不等于", "NotEqual", "!=")
    ;
    
    @Getter
    private String value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    @Getter
    public String spelValue;

    TriggerConditionOperatorEnum(String value, String name, String nameDesc, String spelValue) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
        this.spelValue = spelValue;
    }

    public static TriggerConditionOperatorEnum typeOfValue(String value){
        TriggerConditionOperatorEnum[] values = TriggerConditionOperatorEnum.values();
        for (TriggerConditionOperatorEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static TriggerConditionOperatorEnum typeOfName(String name){
        TriggerConditionOperatorEnum[] values = TriggerConditionOperatorEnum.values();
        for (TriggerConditionOperatorEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static TriggerConditionOperatorEnum typeOfNameDesc(String nameDesc){
        TriggerConditionOperatorEnum[] values = TriggerConditionOperatorEnum.values();
        for (TriggerConditionOperatorEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
