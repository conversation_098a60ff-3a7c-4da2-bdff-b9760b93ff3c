package com.nti56.nlink.product.device.server.domain.thing.enumerate;

import lombok.Getter;

/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName SubscriptionEventTypeEnum
 * @date 2023/2/9 11:58
 * @Version 1.0
 */
public enum SubscriptionEventTypeEnum {

    FAULT(0,"fault",""),
    TRIGGER(1,"trigger",""),
    DATA_CHANGE(2,"dataChange",""),
    GATEWAY_EVENT(3,"gatewayEvent",""),
    CHANNEL_EVENT(4,"channelEvent",""),
    NO_CHANGE(5,"noChange",""),
    ERROR(6,"error","")
    ;
    
    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    SubscriptionEventTypeEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static SubscriptionEventTypeEnum typeOfValue(Integer value){
        SubscriptionEventTypeEnum[] values = SubscriptionEventTypeEnum.values();
        for (SubscriptionEventTypeEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static SubscriptionEventTypeEnum typeOfName(String name){
        SubscriptionEventTypeEnum[] values = SubscriptionEventTypeEnum.values();
        for (SubscriptionEventTypeEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static SubscriptionEventTypeEnum typeOfNameDesc(String nameDesc){
        SubscriptionEventTypeEnum[] values = SubscriptionEventTypeEnum.values();
        for (SubscriptionEventTypeEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
    
}
