package com.nti56.nlink.product.device.server.service.cache.redis2Mem;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

import org.apache.commons.compress.utils.Lists;

import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Sets;
import com.google.common.collect.Table;
import com.nti56.nlink.product.device.client.model.dto.json.DeviceRuntimeMetadataField;
import com.nti56.nlink.product.device.server.domain.thing.modelbase.Subscription;
import com.nti56.nlink.product.device.server.entity.ComputeTaskEntity;



public class MemoryCache {


    private static Map<Long, Boolean> deviceEnableCache = new ConcurrentHashMap<>();

    private static Map<Long, Map<String, Object>> actualPropertiesValueCache = new ConcurrentHashMap<>();


    private static Map<String,Map<String,Object>> groupDeviceMapCache=new ConcurrentHashMap<>();


    private static Map<Long,Set<String>> deviceBindCache=new ConcurrentHashMap<>();

    private static Map<Long,Map<String,String>> deviceIdLabelPropertyCache=new ConcurrentHashMap<>();

    private static Map<Long,ComputeTaskEntity> deviceIdComputeTaskCache=new ConcurrentHashMap();

    private static Table<Long,String,List<Subscription>> deviceIdSubscriptionCache=HashBasedTable.create();

    private static Map<Long,DeviceRuntimeMetadataField> deviceRuntimeMetaDataCache=new ConcurrentHashMap<>();

    public static void putDeviceEnable(Long key, Boolean enable) {
        deviceEnableCache.put(key, enable);
    }

    public static Boolean getDeviceEnable(Long key) {
        return deviceEnableCache.get(key);
    }

    public static void removeDeviceEnable(Long key) {
        deviceEnableCache.remove(key);
    }

    public static void removeDeviceEnable(List<Long> keys) {
        if (keys != null && !keys.isEmpty()) {
            keys.forEach(x -> deviceEnableCache.remove(x));
        }
    }

    public static boolean setActualPropertiesValue(Long deviceId, Map<String, Object> propertiesValueMap) {
        Map<String, Object> tempActualMap = actualPropertiesValueCache.get(deviceId);
        if(tempActualMap!=null){
            if(propertiesValueMap!=null&&!propertiesValueMap.isEmpty()){
                tempActualMap.putAll(propertiesValueMap);
            }
        }else{
            actualPropertiesValueCache.put(deviceId, propertiesValueMap);
        }
        return true;
    }

     public static  Map<String, Object> getActualPropertiesValue(Long deviceId) {
        return actualPropertiesValueCache.getOrDefault(deviceId,new HashMap<>());
    }

    public static void deleteActualProperties(Long deviceId, Set<String> oldPropertyNames) {
        if (oldPropertyNames != null && !oldPropertyNames.isEmpty()) {
            Map<String, Object> propertiesValueMap = actualPropertiesValueCache.get(deviceId);
            if (propertiesValueMap != null) {
                oldPropertyNames.forEach(prop -> propertiesValueMap.remove(prop));
            }
        }
    }

    public static Object getActualPropValue(Long deviceId, String property) {
        Map<String, Object> propertiesValueMap = actualPropertiesValueCache.get(deviceId);
        if (propertiesValueMap != null) {
            return propertiesValueMap.get(property);
        }
        return null;
    }


    public static void setGroupDeviceMap(String groupDeviceMapKey,Map<String,Object> idLabelNamesMap){
        groupDeviceMapCache.put(groupDeviceMapKey,idLabelNamesMap);
    }

    public static void deleteGroupDeviceMap(String groupDeviceMapKey,String deviceId) {
        Map<String, Object> groupDeviceMap = groupDeviceMapCache.get(groupDeviceMapKey);
        if(groupDeviceMap!=null){
            groupDeviceMap.remove(deviceId);
        }
    }


    public static Map<String, Object> getGroupDeviceMap(String groupDeviceMapKey) {
        return groupDeviceMapCache.getOrDefault(groupDeviceMapKey,new HashMap<>());
    }


    public static Set<String> getDeviceBind(Long deviceId){
        return  deviceBindCache.getOrDefault(deviceId,new HashSet<>());
    }

    public static void setDeviceBind(Long deviceId,Set<String> deviceBingKeys){
        Set<String> set = deviceBindCache.get(deviceId);
        if(set!=null&& deviceBingKeys!=null){
            set.addAll(deviceBingKeys);
        }else{
           deviceBindCache.put(deviceId,deviceBingKeys) ;
        }
    }

    public static void deleteDeviceBind(Long devcieId){
        deviceBindCache.remove(devcieId);
    }



    public static void setDeviceLabelPropertyMap(Long deviceId,Map<String,String> labelPropertyMap){
        deviceIdLabelPropertyCache.put(deviceId, labelPropertyMap);
    }

    public static Map<String, String> getLabelPropertyMap(Long deviceId) {
        return deviceIdLabelPropertyCache.getOrDefault(deviceId, new ConcurrentHashMap<>());
    }

    public static void deleteDeviceLabelPropertyMap(Long deviceId){
        deviceIdLabelPropertyCache.remove(deviceId);
    }

    //TODO 只有set 不需要用到get？
    public static void setComputeTask(Long deviceId, ComputeTaskEntity task) {
        deviceIdComputeTaskCache.put(deviceId, task);
    }



    public static List<Subscription> getSubscriptionListByDeviceIdKey(Long deviceId,String key) {
        List<Subscription> result= deviceIdSubscriptionCache.get(deviceId,key);
        if(result==null){
            result= Lists.newArrayList();
        }
        return result;
    }

    public static List<Subscription> getSubscriptionListByDeviceId(Long deviceId) {
        List<Subscription> subscriptionList = Lists.newArrayList();
        Map<String, List<Subscription>> result = deviceIdSubscriptionCache.row(deviceId);
        if (result != null) {
            result.values().stream().flatMap(x -> x.stream()).forEach(subscriptionList::add);
        }
        return subscriptionList;
    }


    public static void addSubscriptionListByDeviceId(Long deviceId,String key, List<Subscription> subscriptionList) {

        List<Subscription> list = deviceIdSubscriptionCache.get(deviceId, key);
        if(list!=null){
            if(subscriptionList!=null){
                list.addAll(subscriptionList);
            }
        }else{
            if(subscriptionList!=null){
                list=subscriptionList;
            }else{
                list=Lists.newArrayList();
            }   
            deviceIdSubscriptionCache.put(deviceId,key,list);
        }
       
    }

    public static void deleteSubscriptionListByDeviceId(Long deviceId){
        Map<String,List<Subscription>> subscriptionList=deviceIdSubscriptionCache.row(deviceId);
        if(subscriptionList!=null){
            Set<String> tempKeySet = Sets.newHashSet(subscriptionList.keySet());
            tempKeySet.forEach(x->deviceIdSubscriptionCache.remove(deviceId,x));
        }
    }
    

    public static void setDeviceRuntimeMetaDataCache(Long deviceId,DeviceRuntimeMetadataField deviceRuntimeMetadataField){
        deviceRuntimeMetaDataCache.put(deviceId, deviceRuntimeMetadataField);
    }

    public static DeviceRuntimeMetadataField getDeviceRuntimeMetadataField(Long deviceId){
        return deviceRuntimeMetaDataCache.get(deviceId);
    }

    public static void deleteDeviceRuntimeMetaData(List<Long> deviceId){
        deviceId.forEach(x->deviceRuntimeMetaDataCache.remove(x));
    }


}
