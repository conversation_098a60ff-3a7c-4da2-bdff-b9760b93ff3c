package com.nti56.nlink.product.device.server.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * InfluxDB缓存配置
 * 
 * <AUTHOR>
 * @date 2025-06-26
 */
@Data
@Component
@ConfigurationProperties(prefix = "influxdb.cache")
public class InfluxDBCacheConfig {
    
    /**
     * 每个租户的最大缓存点数
     */
    private int maxPointsPerTenant = 10000;
    
    /**
     * 总缓存最大点数
     */
    private int maxTotalPoints = 100000;
    
    /**
     * 批量写入大小
     */
    private int batchSize = 10000;
    
    /**
     * 批量写入间隔(毫秒)
     */
    private long writeInterval = 10000;
    
    /**
     * 是否启用缓存机制
     */
    private boolean enabled = true;
    
    /**
     * 缓存清理间隔(毫秒)
     */
    private long cleanupInterval = 60000;
}