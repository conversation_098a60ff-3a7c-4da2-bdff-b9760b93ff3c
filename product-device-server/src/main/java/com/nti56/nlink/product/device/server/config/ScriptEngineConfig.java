package com.nti56.nlink.product.device.server.config;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class ScriptEngineConfig {
        
    @Bean("ScriptEngine")
    public ScriptEngine scriptEngine(){
        ScriptEngineManager manager = new ScriptEngineManager();
        ScriptEngine engine = manager.getEngineByName("nashorn");
        return engine;
    }
    
}
