package com.nti56.nlink.product.device.server.domain.thing.enumerate;

import lombok.Getter;

/**
 * 类说明: 事件类型
 * 
 * 版本1.4把值改变和直接上报挪到属性中了
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-24 14:06:47
 * @since JDK 1.8
 */
public enum EventTypeEnum {
    TRIGGER(1, "trigger", "条件触发"),
    // ,
    // CHANGE(2, "change", "值改变")
    // ,
    FAULT(4, "fault", "告警上报"),
    ERROR(3, "error", "故障上报")
    ;
    
    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    EventTypeEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static EventTypeEnum typeOfValue(Integer value){
        EventTypeEnum[] values = EventTypeEnum.values();
        for (EventTypeEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static EventTypeEnum typeOfName(String name){
        EventTypeEnum[] values = EventTypeEnum.values();
        for (EventTypeEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static EventTypeEnum typeOfNameDesc(String nameDesc){
        EventTypeEnum[] values = EventTypeEnum.values();
        for (EventTypeEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
