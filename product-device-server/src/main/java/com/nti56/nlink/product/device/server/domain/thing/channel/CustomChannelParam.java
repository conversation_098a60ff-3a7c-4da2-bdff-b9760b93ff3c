package com.nti56.nlink.product.device.server.domain.thing.channel;

import java.util.Map;
import java.util.stream.Collectors;
import java.util.List;
import com.nti56.nlink.product.device.server.entity.ChannelParamEntity;

import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.client.model.dto.json.ChannelRuntimeInfoField;
import com.nti56.nlink.product.device.client.model.dto.json.device.ChannelElm;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.CustomDriverModeEnum;
import com.nti56.nlink.product.device.server.util.RegexUtil;

import lombok.Getter;

@Getter
public class CustomChannelParam extends ChannelParam{
    private String ip;
    private Integer port;
    private CustomDriverModeEnum mode;
    private Boolean debug;

    public static final String[] requiredParam = new String[]{
            "ip::true",
            "port::true",
            "mode:async:true:async,sync:模式（async异步，sync同步）",
            "debug:false:true:true,false:是否开启调试",
            "reconnectGapMs:3000:true::断线重连间隔（毫秒）",
            "maxConnection:1:true::通道最多连接数",
            "delayIdleMs:0:true::延迟空闲时间（毫秒）"
    };

    public static final String[] serverRequiredParam = new String[]{
            "ip::true",
            "port::true",
            "mode:async:true:async,sync:模式（async异步，sync同步）",
            "debug:false:true:true,false:是否开启调试",
            "maxConnection:1:true::通道最多连接数"
    };

    public static Result<ChannelParam> checkParam(Boolean isServer, List<ChannelParamEntity> channelParamList){
        if(isServer == null){
            isServer = false;
        }

        CustomChannelParam param = new CustomChannelParam();

        Map<String, String> paramMap = null;
        if(isServer){
            List<ChannelParamEntity> list = ChannelParam.mergeDefault(serverRequiredParam, channelParamList);
            Result<Void> uniqueResult = ChannelParam.checkUnique(list);
            if(!uniqueResult.getSignal()){
                return Result.error(uniqueResult.getMessage());
            }
            param.rawChannelParamList = list;

            paramMap = list.stream().collect(Collectors.toMap(
                ChannelParamEntity::getName, 
                ChannelParamEntity::getValue
            ));
            
            //maxConnection
            String maxConnectionStr = paramMap.get("maxConnection");
            if(maxConnectionStr == null){
                return Result.error("通道参数缺少maxConnection");
            }
            if(!RegexUtil.checkInt(maxConnectionStr)){
                return Result.error("通道maxConnection格式错误，maxConnection:" + maxConnectionStr);
            }
            param.maxConnection = Integer.parseInt(maxConnectionStr);

        }else{
            Result<Map<String, String>> baseResult = ChannelParam.checkBase(
                param, requiredParam, channelParamList
            );
            if(!baseResult.getSignal()){
                return Result.error(baseResult.getMessage());
            }
            paramMap = baseResult.getResult();
        }

        //ip
        String ipStr = paramMap.get("ip");
        if(ipStr == null){
            return Result.error("通道参数缺少ip");
        }
        if(!RegexUtil.checkIpv4(ipStr)){
            return Result.error("通道ip格式错误，ip:" + ipStr);
        }
        param.ip = ipStr;

        //port
        String portStr = paramMap.get("port");
        if(portStr == null){
            return Result.error("通道参数缺少port");
        }
        if(!RegexUtil.checkPort(portStr)){
            return Result.error("通道port格式错误，port:" + portStr);
        }
        param.port = Integer.parseInt(portStr);

        //channelKey
        param.channelKey = param.ip + param.port;

        //mode
        String modeStr = paramMap.get("mode");
        if(modeStr == null){
            return Result.error("通道参数缺少模式（mode）");
        }
        CustomDriverModeEnum mode = CustomDriverModeEnum.typeOfName(modeStr);
        if(mode == null){
            return Result.error("通道mode格式错误，mode:" + modeStr);
        }
        param.mode = mode;

        //debug
        String debugStr = paramMap.get("debug");
        if(debugStr == null){
            return Result.error("通道参数缺少调试（debug）");
        }
        if("true".equals(debugStr)){
            param.debug = true;
        }else if("false".equals(debugStr)){
            param.debug = false;
        }else{
            return Result.error("通道mode格式错误，mdebug:" + debugStr);
        }


        return Result.ok(param);

    }

    @Override
    public void processRuntimeInfo(ChannelRuntimeInfoField info) {
        processBaseRuntimeInfo(info);
        info.setIp(ip);
        info.setPort(port);
        info.setCustomDriverMode(mode.getName());
        info.setDebug(debug);
    }

    @Override
    public void processChannelElm(ChannelElm channelElm) {
        processBaseChannelElm(channelElm);
        
        channelElm.setIp(ip);
        channelElm.setPort(port);
        channelElm.setCustomDriverMode(mode.getName());
        channelElm.setDebug(debug);
    }

}
