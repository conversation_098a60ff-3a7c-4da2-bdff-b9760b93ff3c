package com.nti56.nlink.product.device.server.controller;

import com.nti56.nlink.common.util.R;
import com.nti56.nlink.product.device.server.model.channel.dto.CheckChannelParamsDTO;
import com.nti56.nlink.product.device.server.service.IChannelParamService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 类说明: 通道参数controller
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 13:32:04
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/")
@Tag(name = "通道参数模块")
public class ChannelParamController {
    
    @Autowired
    IChannelParamService channelParamService;

    @PostMapping("channel-param/checkChannelParams")
    @Operation(summary = "检查通道参数，当code=4000时为警告")
    public R checkChannelParams(@RequestBody @Validated CheckChannelParamsDTO dto){
        return R.result(channelParamService.checkChannelParams(dto));
    }
    
}
