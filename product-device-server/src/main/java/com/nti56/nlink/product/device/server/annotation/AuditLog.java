package com.nti56.nlink.product.device.server.annotation;

import com.nti56.nlink.product.device.server.enums.ActionEnum;
import com.nti56.nlink.product.device.server.enums.AuditTargetEnum;

import java.lang.annotation.*;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface AuditLog {

    ActionEnum action() ; // 操作类型
    AuditTargetEnum target() ; // 目标对象
    String details() default ""; // 操作详情

}
