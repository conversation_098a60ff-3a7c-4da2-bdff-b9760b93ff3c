package com.nti56.nlink.product.device.server.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.*;
import com.nti56.nlink.product.device.server.entity.DataModelEntity;
import com.nti56.nlink.product.device.server.service.IDataModelService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <p>
 * 数据模型表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2022-04-13 13:31:16
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/")
@Tag( name = "数据模型模块") 
public class DataModelController {

    @Autowired
    IDataModelService service;

    @GetMapping("data-model/page")
    @Operation(summary = "获取分页",
            parameters  = {
                    @Parameter(name = "current",description = "要获取的页码",required = true),
                    @Parameter(name="size",description = "每页获取多少条",required = true)
            }
    )
    public R page(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, 
                    PageParam pageParam,String name
    ){
        Page<DataModelEntity> page = pageParam.toPage(DataModelEntity.class);
        Result<Page<DataModelEntity>> result = service.page(tenantIsolation, name,page);
        return R.result(result);
    }

    @GetMapping("data-model/list")
    @Operation(summary = "获取列表" )
    public R list(@RequestHeader("ot_headers") TenantIsolation tenantIsolation){
        Result<List<DataModelEntity>> result = service.list(tenantIsolation);
        return R.result(result);
    }

    @PostMapping("data-model")
    @Operation(summary = "创建对象")
    public R create(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, 
                    @Parameter(description = "对象") @RequestBody DataModelEntity entity
    ){
        Result<DataModelEntity> result = service.save(tenantIsolation, entity);
        return R.result(result);
    }

    @PutMapping("data-model")
    @Operation(summary = "更新")
    public R update(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                    @Parameter(description = "对象") @RequestBody DataModelEntity entity
    ){
        if (BeanUtilsIntensifier.checkBeanAndProperties(entity, DataModelEntity::getId)) {
            return R.error(ServiceCodeEnum.CODE_PARAM_ERROR);
        }
        Result<Integer> result = service.update(tenantIsolation, entity);
        return R.result(result);
    }

    @DeleteMapping("data-model/{entityId}")
    @Operation(summary = "删除对象")
    public R delete(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                    @Parameter(description = "目标ID") @PathVariable Long entityId
    ){
        Result<Integer> result = service.deleteById(tenantIsolation, entityId);
        return R.result(result);
    }

    @GetMapping("data-model/{entityId}")
    @Operation(summary = "获取对象")
    public R get(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                @Parameter(description = "目标ID") @PathVariable Long entityId
    ){
        Result<DataModelEntity> result = service.getById(tenantIsolation, entityId);
        return R.result(result);
    }

}
