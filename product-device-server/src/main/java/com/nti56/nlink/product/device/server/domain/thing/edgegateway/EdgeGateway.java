package com.nti56.nlink.product.device.server.domain.thing.edgegateway;

import cn.hutool.core.util.ObjectUtil;
import com.nti56.nlink.common.base.FieldValue;
import com.nti56.nlink.common.base.UniqueConstraint;
import com.nti56.nlink.common.base.UniqueConstraint.Unique;
import com.nti56.nlink.common.fetcher.CommonFetcher;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.domain.thing.channel.Channel;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.EdgeGatewayTypeEnum;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.ModelTypeEnum;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.SyncStatusEnum;
import com.nti56.nlink.product.device.server.domain.thing.modelbase.Subscription;
import com.nti56.nlink.product.device.server.entity.*;
import com.nti56.nlink.product.device.server.util.RegexUtil;

import java.util.*;
import lombok.Getter;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-05-26 08:59:28
 * @since JDK 1.8
 */
@Getter
public class EdgeGateway {

    private static final UniqueConstraint hostPortUniqueConstraint = new UniqueConstraint("host", "port");
    private static final UniqueConstraint nameUniqueConstraint = new UniqueConstraint("name");

    public static final String TASK_PREFIX = "label_change_";

    @Getter
    private Long id;
    private String name;
    private String descript;
    private EdgeGatewayTypeEnum type;
    private String host;
    private Integer port;
    private String imei;
    private String trafficCard;
    private Integer operators;
    private List<Channel> channels;
    private SyncStatusEnum syncStatus;
    private String heartbeatUuid;
    private Boolean visitPublicMqtt;
    private String publicMqttIp;
    private Integer publicMqttPort;
    private List<Subscription> subscriptions;
    
    
    private static Result<EdgeGateway> checkBase(
        String name, String descript, Integer type, 
        String host, Integer port, String imei, 
        String trafficCard, Integer operators, 
        Boolean visitPublicMqtt, String publicMqttIp, Integer publicMqttPort
    ){
        //类型
        if(type == null){
            return Result.error("网关类型不能为空");
        }
        EdgeGatewayTypeEnum typeEnum = EdgeGatewayTypeEnum.typeOfValue(type);
        if(typeEnum == null){
            return Result.error("网关类型不支持");
        }
        EdgeGateway edgeGateway = new EdgeGateway();
        edgeGateway.type = typeEnum;

        //名称
        if(name == null || "".equals(name)){
            return Result.error("网关名称不能为空");
        }
        edgeGateway.name = name;

        //host
        if(host != null && !"".equals(host)){
            if(!RegexUtil.checkIpv4(host)){
                return Result.error("网关host格式错误");
            }
            edgeGateway.host = host;
        }
        
        //port
        if(port != null){
            if(port <= 0 || port > 65535){
                return Result.error("网关port格式错误");
            }
            edgeGateway.port = port;
        }

        //描述
        edgeGateway.descript = descript;
        edgeGateway.imei = imei;
        edgeGateway.trafficCard = trafficCard;
        edgeGateway.operators = operators;
        edgeGateway.visitPublicMqtt = visitPublicMqtt;
        edgeGateway.publicMqttIp = publicMqttIp;
        edgeGateway.publicMqttPort = publicMqttPort;

        return Result.ok(edgeGateway);
    }

    public static Result<EdgeGateway> checkCreate(
            String name, String descript, 
            Integer type, String host, Integer port, 
            String imei, String trafficCard, 
            Integer operators, Boolean visitPublicMqtt,
            String publicMqttIp, Integer publicMqttPort,
            CommonFetcher commonFetcher
    ){
        Result<EdgeGateway> baseResult = checkBase(name, descript, type, host, port, imei, trafficCard, operators, visitPublicMqtt, publicMqttIp, publicMqttPort);
        if(!baseResult.getSignal()){
            return baseResult;
        }
        EdgeGateway edgeGateway = baseResult.getResult();
        
        //名称唯一性检查
        Unique nameUnique = nameUniqueConstraint.buildUnique(new FieldValue(name));
        EdgeGatewayEntity sameNameEntity = commonFetcher.get(nameUnique, EdgeGatewayEntity.class);
        if(sameNameEntity != null){
            return Result.error("网关名称重复");
        }
        
        if(host != null && !"".equals(host) && port != null){
            //host port 联合唯一性校验
            Unique hostPortUnique = hostPortUniqueConstraint.buildUnique(new FieldValue(host), new FieldValue(port));
            EdgeGatewayEntity sameHostPortEntity = commonFetcher.get(hostPortUnique, EdgeGatewayEntity.class);
            if(sameHostPortEntity != null){
                return Result.error("网关host port重复");
            }
        }

        //同步状态
        edgeGateway.syncStatus = SyncStatusEnum.HAVE_SYNC;

        //心跳uuid
        edgeGateway.heartbeatUuid = UUID.randomUUID().toString();

        return Result.ok(edgeGateway);
    }

    public EdgeGatewayEntity toEntity(){
        EdgeGatewayEntity entity = new EdgeGatewayEntity();
        entity.setId(id);
        entity.setName(name);
        entity.setDescript(descript);
        entity.setType(type.getValue());
        entity.setHost(host);
        entity.setPort(port);
        entity.setImei(imei);
        entity.setOperators(operators);
        entity.setTrafficCard(trafficCard);
        entity.setSyncStatus(syncStatus.getValue());
        entity.setHeartbeatUuid(heartbeatUuid);
        entity.setVisitPublicMqtt(visitPublicMqtt);
        entity.setPublicMqttIp(publicMqttIp);
        entity.setPublicMqttPort(publicMqttPort);
        return entity;
    }

    public static Result<EdgeGateway> checkInfo(EdgeGatewayEntity entity,CommonFetcher commonFetcher){

        Result<EdgeGateway> baseResult = checkBase(
            entity.getName(), 
            entity.getDescript(),
            entity.getType(), 
            entity.getHost(), 
            entity.getPort(),
            entity.getImei(),
            entity.getTrafficCard(),
            entity.getOperators(),
            entity.getVisitPublicMqtt(),
            entity.getPublicMqttIp(),
            entity.getPublicMqttPort()
        );

        if(!baseResult.getSignal()){
            return baseResult;
        }
        EdgeGateway edgeGateway = baseResult.getResult();

        if(entity.getId() == null){
            return Result.error("网关id不能为空");
        }
        edgeGateway.id = entity.getId();
    
        //自己的订阅
        List<SubscriptionEntity> subscriptionEntities = commonFetcher.preloader("from_id", entity.getId(), SubscriptionEntity.class)
            .filter(SubscriptionEntity::getModelType, ModelTypeEnum.GATEWAY_MODEL.getValue());
        Result<List<Subscription>> selfSubscriptionsResult = Subscription.batchCheckInfo(entity.getId(),entity.getName(),subscriptionEntities);
        if(!selfSubscriptionsResult.getSignal()){
            return Result.error(selfSubscriptionsResult.getMessage() + ", from_id:" + entity.getId());
        }
        List<Subscription> selfSubscriptions = selfSubscriptionsResult.getResult();
    
        //检查自己的物模型中是否订阅重名
        Result<List<Subscription>> subscriptionResult = Subscription.checkRepeat(null,selfSubscriptions);
        if(!subscriptionResult.getSignal()){
            return Result.error(subscriptionResult.getMessage() + ", 模型ID:" + entity.getId());
        }
        edgeGateway.subscriptions = subscriptionResult.getResult();
        return Result.ok(edgeGateway);

    }

    public static Result<EdgeGateway> checkUpdate(
        Long id,
        String name, String descript, Integer type, 
        String host, Integer port, String imei, 
        String trafficCard, Integer operators, 
        Boolean visitPublicMqtt, String publicMqttIp, Integer publicMqttPort,
        CommonFetcher commonFetcher
    ){
        //取出旧entity
        if(id == null){
            return Result.error("网关id不能为空");
        }
        EdgeGatewayEntity oldEntity = commonFetcher.get(id, EdgeGatewayEntity.class);
        if(oldEntity == null){
            return Result.error("找不到网关，id:" + id);
        }
        
        //检查更新值基本信息
        Result<EdgeGateway> baseResult = checkBase(
            name, descript, type, host, port, imei, trafficCard, operators, 
            visitPublicMqtt, publicMqttIp, publicMqttPort
        );
        if(!baseResult.getSignal()){
            return baseResult;
        }
        EdgeGateway edgeGateway = baseResult.getResult();

        //把部分旧值复制过来
        edgeGateway.id = id;

        //名称唯一性检查，要排除自己
        Unique nameUnique = nameUniqueConstraint.buildUnique(new FieldValue(name));
        EdgeGatewayEntity sameNameEntity = commonFetcher.get(nameUnique, EdgeGatewayEntity.class);
        if(sameNameEntity != null && !id.equals(sameNameEntity.getId())){
            return Result.error("网关名称重复");
        }
        
        Unique hostPortUnique = hostPortUniqueConstraint.buildUnique(new FieldValue(host), new FieldValue(port));
        if(host != null && !"".equals(host) && port != null){
            //host port 联合唯一性校验，要排除自己
            EdgeGatewayEntity sameHostPortEntity = commonFetcher.get(hostPortUnique, EdgeGatewayEntity.class);
            if(sameHostPortEntity != null && !id.equals(sameHostPortEntity.getId())){
                return Result.error("网关host port重复");
            }

        }
        //检查通过

        //如果修改了host:port，需要更新uuid
        Unique oldHostPortUnique = hostPortUniqueConstraint.buildUnique(
            new FieldValue(oldEntity.getHost()), 
            new FieldValue(oldEntity.getPort())
        );
        if(!hostPortUnique.equals(oldHostPortUnique)){
            edgeGateway.heartbeatUuid = UUID.randomUUID().toString();
        }


        edgeGateway.syncStatus = SyncStatusEnum.HAVE_SYNC;
        //更新为未同步状态
        if (!(ObjectUtil.equals(name,oldEntity.getName())
                && ObjectUtil.equals(oldEntity.getType(),type)
                && ObjectUtil.equals(oldEntity.getHost(),host)
                && ObjectUtil.equals(oldEntity.getPort(),port))){
            edgeGateway.syncStatus = SyncStatusEnum.NOT_SYNC;
        }


        return Result.ok(edgeGateway);
    }

    public static List<Channel> getChannelWithLabelGroup(Long id,CommonFetcher commonFetcher){
        List<Channel> channels = new ArrayList<>();
        List<ChannelEntity> channelEntities = commonFetcher.list("edge_gateway_id", id, ChannelEntity.class);
        Optional.ofNullable(channelEntities).orElse(new ArrayList<>())
                .stream()
                .sorted(Comparator.comparing(ChannelEntity::getName))
                .forEach(channelEntity -> {
            channels.add(Channel.getWithFlatLabelGroup(channelEntity, commonFetcher));
        });
        return channels;
    }

}
