package com.nti56.nlink.product.device.server.domain.thing.enumerate;

import lombok.Getter;

public enum InheritTypeEnum {
    INHERITED_IN((byte)1, "INHERITED_IN", "继承于"),
    INHERITED((byte)2, "INHERITED", "被继承");

    @Getter
    private Byte value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    InheritTypeEnum(Byte value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static InheritTypeEnum typeOfValue(Byte value){
        InheritTypeEnum[] values = InheritTypeEnum.values();
        for (InheritTypeEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static InheritTypeEnum typeOfName(String name){
        InheritTypeEnum[] values = InheritTypeEnum.values();
        for (InheritTypeEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static InheritTypeEnum typeOfNameDesc(String nameDesc){
        InheritTypeEnum[] values = InheritTypeEnum.values();
        for (InheritTypeEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
