package com.nti56.nlink.product.device.server.constant;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/4/15 11:45<br/>
 * @since JDK 1.8
 */
public class AliYunConstant {

    public static final String REQUEST_SUCCESS = "OK";

    public static final String ACCESS_KEY_ID = "accessKeyId";

    public static final String ACCESS_KEY_SECRET = "accessKeySecret";

    public static final String SIGN_NAME = "signName";

    public static final String WEBHOOK = "webhook";

    public static final String SECRET = "secret";

    public static final int DEFAULT_TEMPLATE_TYPE = 1;

    public static final String DEFAULT_REASON = "用于物联网项目通知";

    public static final String DEFAULT_CONNECTION_CONTEND = "机器人连接OT平台成功";

    public static final String SMS_TEMPLATE_ILLEGAL = "isv.SMS_TEMPLATE_ILLEGAL";
}
