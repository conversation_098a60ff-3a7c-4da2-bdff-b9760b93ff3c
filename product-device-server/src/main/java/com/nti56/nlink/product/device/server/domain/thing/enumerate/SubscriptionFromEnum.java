package com.nti56.nlink.product.device.server.domain.thing.enumerate;

import lombok.Getter;

public enum SubscriptionFromEnum {
    FAULT(1, "FAULT", "故障"),
    DEVICE_OFFLINE(2, "DEVICE_OFFLINE", "离线"),
    WRITE_PROPERTY_SPI(3, "WRITE_PROPERTY_SPI", "spi写属性"),
    WRITE_PROPERTY_MEMORY(4, "WRITE_PROPERTY_MEMORY", "内存写属性"),
    WRITE_PROPERTY_OLD(5, "WRITE_PROPERTY_OLD", "旧的写属性"),
    WRITE_PROPERTY_DEVICE_STATUS(6, "WRITE_PROPERTY_DEVICE_STATUS", "设备状态改变"),
    UP_DATA_TO_REDIS(7, "UP_DATA_TO_REDIS", "上报数据"),
    SPI_READ_PROPERTY(8, "SPI_READ_PROPERTY", "spi读取后写入"),
    WRITE_PROPERTY_TWIN(9, "WRITE_PROPERTY_TWIN", "直接写入Twin"),
    EDGE_GATEWAY_STATUS(10, "EDGE_GATEWAY_STATUS", "网关状态改变"),
    CHANNEL_STATUS(11, "CHANNEL_STATUS", "网关通道改变"),
    TRIGGER(12, "TRIGGER", "属性改变事件"),
    NO_CHANGE(13, "NO_CHANGE", "属性未改变事件")
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    SubscriptionFromEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static SubscriptionFromEnum typeOfValue(Integer value){
        SubscriptionFromEnum[] values = SubscriptionFromEnum.values();
        for (SubscriptionFromEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static SubscriptionFromEnum typeOfName(String name){
        SubscriptionFromEnum[] values = SubscriptionFromEnum.values();
        for (SubscriptionFromEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static SubscriptionFromEnum typeOfNameDesc(String nameDesc){
        SubscriptionFromEnum[] values = SubscriptionFromEnum.values();
        for (SubscriptionFromEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
