package com.nti56.nlink.product.device.server.annotation.aspect;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.nti56.nlink.product.device.server.annotation.GatewayChange;
import com.nti56.nlink.product.device.server.constant.RedisConstant;
import com.nti56.nlink.product.device.server.model.edgegateway.EdgeGatewayChangeState;
import com.nti56.nlink.product.device.server.service.IEdgeGatewayService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

@Component
@Aspect
@Slf4j
public class GatewayChangeAspect {

    /**方法参数名解析器*/
    private final DefaultParameterNameDiscoverer nameDiscoverer = new DefaultParameterNameDiscoverer();
    /**Spel表达式解析器*/
    private final SpelExpressionParser parser = new SpelExpressionParser();
    /**SPEL表达式标识符*/
    public final String SPEL_FLAG = "#";

    @Autowired
    private IEdgeGatewayService edgeGatewayService;

    @Pointcut("@annotation(com.nti56.nlink.product.device.server.annotation.GatewayChange)")
    private void haxServiceAspect() {}

    @After(value = "haxServiceAspect()")
    public void haxServiceAspect(JoinPoint joinPoint) throws Throwable {
        MethodSignature methodSignature = (MethodSignature)joinPoint.getSignature();
        Method method = methodSignature.getMethod();
        final String methodName = method.getName();

        // 方法形参名称
        String[] argNames = nameDiscoverer.getParameterNames(method);
        // 方法实参值
        Object[] args = joinPoint.getArgs();

        GatewayChange gatewayChange = method.getAnnotation(GatewayChange.class);
        boolean isArray = true;
        String expressionStr = null;
        if (!StringUtils.isEmpty(gatewayChange.value())) {
            isArray = false;
            expressionStr = gatewayChange.value();
        }else if (!StringUtils.isEmpty(gatewayChange.values())){
            expressionStr = gatewayChange.values();
        }else {
            return ;
        }
        Object edgeGatewayObj = this.analysisSpel(expressionStr, argNames, args);
        LocalDateTime now = LocalDateTime.now();
        log.info("update gateway change state,methodName:{},id:{}",methodName,edgeGatewayObj);
        if (Objects.nonNull(edgeGatewayObj)) {
            try {
                if (isArray) {
                    List<Long> ids = (List<Long>) edgeGatewayObj;
                    if (CollectionUtil.isNotEmpty(ids)) {
                        ids.forEach(id -> updateEdgeGatewayChangeState(id,now));
                    }
                }else {
                    updateEdgeGatewayChangeState((Long)edgeGatewayObj,now);
                }
            }catch (Exception e){
                log.warn("update gateway change state error:{},methodName:{},id:{}",e.getMessage(),methodName,edgeGatewayObj);
            }
        }
    }

    @Autowired @Lazy
    StringRedisTemplate stringRedisTemplate;

    private void updateEdgeGatewayChangeState(Long edgeGatewayId, LocalDateTime now) {
        String stateKey = String.format(RedisConstant.OT_GATEWAY_CHANGE_STATE, edgeGatewayId);
        EdgeGatewayChangeState state =  EdgeGatewayChangeState.builder().isEdgeGatewayChange(true).changeTime(now).build();
        stringRedisTemplate.opsForValue().set(stateKey, JSON.toJSONString(state));
    }


    /**
     * 解析SPEL表达式
     *
     * @param spel          SPEL表达式
     * @param argNames      形参名称数组
     * @param args          实参数组
     */
    private Object analysisSpel(String spel, String[] argNames, Object[] args) {
        if (!StrUtil.contains(spel, SPEL_FLAG)) {
            return spel;
        }
        Expression expression = parser.parseExpression(spel);
        EvaluationContext context = new StandardEvaluationContext();
        for (int i = 0; i < argNames.length; i++) {
            context.setVariable(argNames[i], args[i]);
        }
        Object value = expression.getValue(context);
        return value;
    }

}
