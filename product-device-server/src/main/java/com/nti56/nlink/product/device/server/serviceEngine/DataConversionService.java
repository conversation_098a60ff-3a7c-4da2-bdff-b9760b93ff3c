package com.nti56.nlink.product.device.server.serviceEngine;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.nlink.product.device.server.constant.RedisConstant;
import com.nti56.nlink.product.device.server.domain.thing.modelbase.EventData;
import com.nti56.nlink.product.device.server.domain.thing.modelbase.Subscription;
import com.nti56.nlink.product.device.server.exception.BizException;
import com.nti56.nlink.product.device.server.service.cache.redis2Mem.MemoryCache;
import com.nti56.nlink.product.device.server.util.ApplicationContextUtil;
import com.nti56.nlink.product.device.server.util.redis.RedisUtil;
import jdk.nashorn.api.scripting.ScriptObjectMirror;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.script.Invocable;
import javax.script.ScriptEngine;
import javax.script.ScriptException;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName DataConversionService
 * @date 2022/9/26 17:25
 * @Version 1.0
 */
@Component
@Slf4j
public class DataConversionService implements InitializingBean {

    private static ServiceEngine engine;

    @Autowired @Lazy
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    ServiceEngineFactory serviceEngineFactory;


    @Override
    public void afterPropertiesSet() throws Exception {
        ServiceEngine engine = serviceEngineFactory.getServiceEngine();
        if (!Optional.ofNullable(engine).isPresent()) {
            return;
        }
        this.engine = engine;
    }

    public static Result<ScriptObjectMirror> dataConversion(Long subscriptionId, Map<String,Object> actual, String eventName, Long timestamp, EventData eventData,int i){
        if (i < 0){
            log.error("出现递归调用，subscriptionId:{}",subscriptionId);
            return Result.error("订阅调用失败");
        }
        log.debug("开始调用订阅数据转换，subscriptionId:{},input:{}",subscriptionId,eventData);
        Invocable invocable = (Invocable) engine.engine;
        try {
            //:me,eventName,eventTime,eventData
            ScriptObjectMirror result = (ScriptObjectMirror) invocable.invokeFunction(
                getServiceName(subscriptionId),
                actual,
                eventName,
                timestamp,
                eventData
            );
            return Result.ok(result);
        } catch (ScriptException e) {
            log.warn("订阅数据转换代码运行异常：subscriptionId:{},input:{},errorMsg:{}",subscriptionId,eventData,e.getMessage());
            return Result.error("订阅数据转换代码运行异常");
        } catch (NoSuchMethodException e) {
            log.error("订阅数据转换代码没找到：subscriptionId:{},input:{},errorMsg:{} actual id:{}",subscriptionId,eventData,e.getMessage(),actual.get("id"));
            RedisTemplate redisTemplate = ApplicationContextUtil.getBean("redisTemplate", RedisTemplate.class);
            String code = (String) redisTemplate.opsForValue().get(String.format(RedisConstant.SUBSCRIPTION_CODE, subscriptionId));
            String executableCode = initServiceName(subscriptionId,code);
            loadCode(executableCode,engine.engine);
            return dataConversion(subscriptionId,actual,eventName,timestamp,eventData,i-1);
        }
    }

    public static void loadCode(String executableCode, ScriptEngine engine){
        try {
            engine.eval(executableCode);
        } catch (ScriptException e) {
            throw new BizException(ServiceCodeEnum.SUBSCRIPTION_CODE_ERROR.getCode(),ServiceCodeEnum.SUBSCRIPTION_CODE_ERROR.getMessage(),e.getMessage());
        } catch (Exception e){
            throw new BizException(ServiceCodeEnum.SUBSCRIPTION_CODE_ERROR.getCode(),ServiceCodeEnum.SUBSCRIPTION_CODE_ERROR.getMessage(),e.getMessage());
        }
    }

    public void codePreload(Long subscriptionId,String code){
        String key = String.format(RedisConstant.SUBSCRIPTION_CODE, subscriptionId);
        Boolean hasKey = redisTemplate.hasKey(key);
        String oldCode = null;
        if (hasKey){
            oldCode = (String) redisTemplate.opsForValue().get(key);
        }
        if (!StringUtils.isEmpty(oldCode) && oldCode.equals(code)) {
            return;
        }
        log.info("subscription data conversion code change ,subscriptionId:{}",subscriptionId);
        String executableCode = initServiceName(subscriptionId,code);
        loadCode(executableCode, engine.engine);
        redisTemplate.opsForValue().set(key, code);
    }

    public void codePreload(Long deviceId, TenantIsolation tenantIsolation){
        redisTemplate.opsForValue().set(String.format(RedisConstant.DEVICE_TENANT,deviceId),tenantIsolation.getTenantId());
        
        List<Subscription> subscriptionList=MemoryCache.getSubscriptionListByDeviceId(deviceId);
        subscriptionList.forEach(subscription -> {
            codePreload(subscription.getId(),subscription.getDataConversionCode());
        });

        // // 优化：使用索引键获取设备的订阅注册表键，避免scan操作
        // Set<String> keys = RedisSubscriptionIndexUtil.getDeviceSubscriptionKeys(redisTemplate, deviceId);
        
        // if (CollectionUtil.isNotEmpty(keys)) {
            
        //     Set<Object> union = redisTemplate.opsForSet().union(keys);
        //     if (CollectionUtil.isNotEmpty(union)) {
        //         union.forEach(subscription -> {
        //             Subscription s = JSON.parseObject((String) subscription,Subscription.class);
        //             codePreload(s.getId(),s.getDataConversionCode());
        //         });
        //     }
        // }
    }

    public static String initServiceName(Long subscriptionId, String code) {
        StringBuilder sb = new StringBuilder();
        sb.append("function ")
                .append("dataConversionCode_")
                .append(subscriptionId)
                .append("(me,eventName,eventTime,event){")
                .append("var result = {};")
                .append(code)
                .append("return result;")
                .append("}");
        return sb.toString();
    }

    private static String getServiceName(Long subscriptionId) {
        StringBuilder sb = new StringBuilder();
        sb.append("dataConversionCode_")
                .append(subscriptionId);
        return sb.toString();
    }

}
