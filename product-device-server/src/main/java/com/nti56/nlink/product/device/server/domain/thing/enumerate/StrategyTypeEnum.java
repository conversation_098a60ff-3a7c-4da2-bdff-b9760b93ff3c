package com.nti56.nlink.product.device.server.domain.thing.enumerate;

import lombok.Getter;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-24 14:06:57
 * @since JDK 1.8
 */
public enum StrategyTypeEnum {
    INTERVAL(1, "interval", "按时间间隔"), 
    CRON(2, "cron", "按cron表达式")
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    StrategyTypeEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static StrategyTypeEnum typeOfValue(Integer value){
        StrategyTypeEnum[] values = StrategyTypeEnum.values();
        for (StrategyTypeEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static StrategyTypeEnum typeOfName(String name){
        StrategyTypeEnum[] values = StrategyTypeEnum.values();
        for (StrategyTypeEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static StrategyTypeEnum typeOfNameDesc(String nameDesc){
        StrategyTypeEnum[] values = StrategyTypeEnum.values();
        for (StrategyTypeEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
