package com.nti56.nlink.product.device.server.domain.thing.modelbase;

import java.util.*;

import com.nti56.nlink.common.util.BeanUtilsIntensifier;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.client.model.dto.json.condition.TriggerConditionElm;
import com.nti56.nlink.product.device.client.model.dto.json.dpo.EventDefineDpo;
import com.nti56.nlink.product.device.client.model.dto.json.modelfield.EventDefineElm;
import com.nti56.nlink.product.device.client.model.dto.json.modelfield.PropertyInputMapElm;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.EventTypeEnum;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 类说明: 事件定义领域对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-24 18:09:49
 * @since JDK 1.8
 */

public class EventDefine {

    @Getter
    private EventTrigger trigger;
    @Getter
    private Integer faultBeginThreshold; //故障开始阈值
    @Getter
    private Integer faultEndThreshold; //故障结束阈值
    @Getter
    private Map<String, String> faultInputMap; //故障参数映射，属性 -> 参数

    @Getter
    private List<String> properties;
    @Getter
    private String property;
    @Getter
    private Long thingServiceId;
    @Getter
    private List<PropertyInputMap> faultInput;
    @Getter
    private FaultLevelDefine faultLevelDefine;


    public static Result<EventDefine> checkInfo(
        String eventName,
        EventTypeEnum eventType,
        EventDefineElm eventDefineElm,
        Map<String, Property> propertyNameMap
    ) {
        
        if(eventDefineElm == null){
            return Result.error("事件定义不能为空");
        }


        switch (eventType) {
            case TRIGGER: {
                Result<EventDefine> eventDefineResult = checkTrigger(eventDefineElm, propertyNameMap, eventName);
                if(!eventDefineResult.getSignal()){
                    return eventDefineResult;
                }
                return eventDefineResult;
            }
            case ERROR:
            case FAULT: {
                Result<EventDefine> eventDefineResult = checkTrigger(eventDefineElm, propertyNameMap, eventName);
                if(!eventDefineResult.getSignal()){
                    return eventDefineResult;
                }
                EventDefine eventDefine = eventDefineResult.getResult();

                if(eventDefineElm.getFaultBeginThreshold() == null || eventDefineElm.getFaultBeginThreshold() <= 0){
                    return Result.error("故障开始阈值不能小于0");
                }
                eventDefine.faultBeginThreshold = eventDefineElm.getFaultBeginThreshold();

                if(eventDefineElm.getFaultEndThreshold() == null || eventDefineElm.getFaultEndThreshold() <= 0){
                    return Result.error("故障结束阈值不能小于0");
                }
                eventDefine.faultEndThreshold = eventDefineElm.getFaultEndThreshold();
                if(eventDefineElm.getFaultInput() != null){
                    Map<String, String> faultInputMap = new HashMap<>();
                    for(PropertyInputMapElm elm:eventDefineElm.getFaultInput()){
                        if(StringUtils.isNotBlank(elm.getProperty())){
                            faultInputMap.put(elm.getProperty(), elm.getInput());
                        }

                    }
                    eventDefine.faultInputMap = faultInputMap;
                }
                eventDefine.faultInput = BeanUtilsIntensifier.copyBeanList(eventDefineElm.getFaultInput(),PropertyInputMap.class);
                eventDefine.thingServiceId = eventDefineElm.getThingServiceId();
                if(!Objects.isNull(eventDefineElm.getFaultLevelDefine())){
                    Result<FaultLevelDefine> faultLevelDefineResult = FaultLevelDefine.checkInfo(eventDefineElm.getFaultLevelDefine());
                    if(faultLevelDefineResult.getSignal()){
                        eventDefine.faultLevelDefine = faultLevelDefineResult.getResult();
                    }
                }
                return Result.ok(eventDefine);
            }
            default: {
                return Result.error("事件类型错误, eventName:" + eventName);
            }
        }

    }

    private static Result<EventDefine> checkTrigger(EventDefineElm eventDefineElm, Map<String, Property> propertyNameMap, String eventName) {

        EventDefine eventDefine = new EventDefine();
        //触发条件
        List<TriggerConditionElm> triggerConditionElmList = eventDefineElm.getTrigger();

        if(triggerConditionElmList == null){
            return Result.error("事件定义缺少触发条件, eventName:" + eventName);
        }
        Result<EventTrigger> result = EventTrigger.checkInfo(
            triggerConditionElmList,
            propertyNameMap
        );
        if(!result.getSignal()){
            return Result.error(result.getMessage() + ", eventName:" + eventName);
        }
        eventDefine.trigger = result.getResult();

        //采集字段
        List<String> elmProperties = eventDefineElm.getProperties();
        if(elmProperties == null || elmProperties.size() <= 0){
            return Result.error("事件定义缺少采集字段, eventName:" + eventName);
        }
        List<String> properties = new ArrayList<>();
        for(String p:elmProperties){
            if(!propertyNameMap.containsKey(p)){
                return Result.error("事件定义采集字段不存在，property:" + p +", eventName:" + eventName);
            }
            properties.add(p);
        }
        eventDefine.properties = properties;
        return Result.ok(eventDefine);
    }

    public EventDefineDpo toDpo() {
        EventDefineDpo dpo = new EventDefineDpo();
        if(trigger != null){
            dpo.setTrigger(trigger.getTriggerConditionElmList());
        }
        if(faultBeginThreshold != null){
            dpo.setFaultBeginThreshold(faultBeginThreshold);
        }
        if(faultEndThreshold != null){
            dpo.setFaultEndThreshold(faultEndThreshold);
        }
        if(faultInputMap != null){
            dpo.setFaultInputMap(faultInputMap);
        }
        if(faultInput != null){
            dpo.setFaultInput(BeanUtilsIntensifier.copyBeanList(faultInput,PropertyInputMapElm.class));
        }
        dpo.setProperties(properties);
        dpo.setProperty(property);
        dpo.setThingServiceId(thingServiceId);
        if(!Objects.isNull(faultLevelDefine)){
            dpo.setFaultLevelDefine(faultLevelDefine.getLevelDefine());
        }
        return dpo;
    }

}
