package com.nti56.nlink.product.device.server.domain.thing.enumerate;

import lombok.Getter;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-05-26 08:59:40
 * @since JDK 1.8
 */
public enum BracketEnum {
    LEFT("left"),
    RIGHT("right")
    ;

    @Getter
    private String value;

    BracketEnum(String value) {
        this.value = value;
    }

    public static BracketEnum typeOfValue(String value){
        BracketEnum[] values = BracketEnum.values();
        for (BracketEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }
}