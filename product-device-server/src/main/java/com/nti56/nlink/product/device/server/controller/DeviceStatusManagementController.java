package com.nti56.nlink.product.device.server.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.*;
import com.nti56.nlink.product.device.server.entity.DeviceEntity;
import com.nti56.nlink.product.device.server.model.DeviceDto;
import com.nti56.nlink.product.device.server.model.DeviceRequestBo;
import com.nti56.nlink.product.device.server.model.DeviceRespondBo;
import com.nti56.nlink.product.device.server.service.IDeviceStatusManagementService;
import com.nti56.nlink.product.device.server.service.ITaskService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 类说明: 设备controller
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 13:32:04
 * @since JDK 1.8
 */
@RestController
@RequestMapping("device/status")
@Tag(name = "设备状态管理模块")
@Slf4j
public class DeviceStatusManagementController {

    @Autowired
    IDeviceStatusManagementService deviceService;

    @Autowired
    ITaskService taskService;

    @PutMapping("batch/online")
    @Operation(summary = "按条件批量上线设备")
    public R deviceBatchOnline(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@RequestBody DeviceRequestBo requestBo) {
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        RequestContextHolder.setRequestAttributes(servletRequestAttributes, true);
        Result<List<DeviceRespondBo>> result = deviceService.deviceBatchOnline(tenantIsolation, requestBo.getIds());
        return R.result(result);
    }

    @PutMapping("batch/offline")
    @Operation(summary = "按条件批量下线设备")
    public R deviceBatchOffline(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@RequestBody DeviceRequestBo requestBo) {
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        RequestContextHolder.setRequestAttributes(servletRequestAttributes, true);
        Result<List<DeviceRespondBo>> result = deviceService.deviceBatchOffline(tenantIsolation, requestBo.getIds());
        return R.result(result);
    }

    @PutMapping("batch/sync")
    @Operation(summary = "按条件批量同步设备")
    public R deviceBatchSync(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@RequestBody DeviceRequestBo requestBo) {
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        RequestContextHolder.setRequestAttributes(servletRequestAttributes, true);
        Result<List<DeviceRespondBo>> result = deviceService.deviceBatchSync(tenantIsolation,requestBo.getIds());
        return R.result(result);
    }
    
    @PutMapping("batch/syncAll")
    @Operation(summary = "按条件批量同步设备")
    public R deviceBatchSync(@RequestHeader("ot_headers") TenantIsolation tenantIsolation) {
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        RequestContextHolder.setRequestAttributes(servletRequestAttributes, true);
        Result<List<DeviceRespondBo>> result = deviceService.deviceBatchSyncAll(tenantIsolation);
        return R.result(result);
    }

    @PutMapping("sync/by/thing/model/{thingModelId}")
    @Operation(summary = "根据物模型批量同步设备")
    public R syncByThingModel(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@PathVariable("thingModelId") Long thingModelId) {
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        RequestContextHolder.setRequestAttributes(servletRequestAttributes, true);
        Result<List<DeviceRespondBo>> result = deviceService.syncByThingModel(tenantIsolation,thingModelId);
        return R.result(result);
    }


    @PutMapping("sync/test/{deviceId}")
    @Operation(summary = "测试")
    public R syncTest(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@PathVariable("deviceId") Long deviceId) {
        List<Long> deviceIdList = new ArrayList<>();
        deviceIdList.add(deviceId);
        Result<Void> result = deviceService.syncBeforeCheck(tenantIsolation,deviceIdList);
        return R.result(result);
    }


    @PutMapping("sync/testCheckInfo")
    @Operation(summary = "测试")
    public R testCheckIInfo(@RequestHeader("ot_headers") TenantIsolation tenantIsolation) {
        Result<Void> result = deviceService.syncBeforeCheckInfo(tenantIsolation);
        return R.result(result);
    }
    @GetMapping("sync/syncCommonType")
    @Operation(summary = "同步基础模型")
    public R syncCommonType() {
         deviceService.syncCommonType();
        return R.ok();
    }

}
