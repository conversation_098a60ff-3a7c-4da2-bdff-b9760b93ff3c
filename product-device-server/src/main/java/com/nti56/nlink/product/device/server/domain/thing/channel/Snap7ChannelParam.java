package com.nti56.nlink.product.device.server.domain.thing.channel;

import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.client.model.dto.json.ChannelRuntimeInfoField;
import com.nti56.nlink.product.device.client.model.dto.json.device.ChannelElm;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.Snap7SpecEnum;
import com.nti56.nlink.product.device.server.util.RegexUtil;
import lombok.Getter;

import java.util.Map;
import java.util.HashMap;
import java.util.List;
import com.nti56.nlink.product.device.server.entity.ChannelParamEntity;


/**
 * 类说明: snap7驱动通道参数领域对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-24 14:07:25
 * @since JDK 1.8
 */
@Getter
public class Snap7ChannelParam extends ChannelParam{
    private String ip;
    private Integer port;
    private Integer rack;
    private Integer slot;
    private Snap7SpecEnum spec;
    private Integer maxVars;

    public static final String[] requiredParam = new String[]{
        "ip::true", 
        "port::true", 
        "rack:0:true", 
        "slot:0:true", 
        "spec:S200Smart:true:S200Smart,S200,S300,S400,S1200,S1500:型号",
        "maxVars:19:true::最大读写参数数量",
        "reconnectGapMs:3000:true::断线重连间隔（毫秒）",
        "maxConnection:1:true::通道最多连接数",
        "delayIdleMs:0:true::延迟空闲时间（毫秒）"
    };

    
    public static Result<ChannelParam> checkParam(List<ChannelParamEntity> channelParamList){

        Snap7ChannelParam param = new Snap7ChannelParam();

        Result<Map<String, String>> baseResult = ChannelParam.checkBase(
            param, requiredParam, channelParamList
        );
        if(!baseResult.getSignal()){
            return Result.error(baseResult.getMessage());
        }
        Map<String, String> paramMap = baseResult.getResult();

        //ip
        String ipStr = paramMap.get("ip");
        if(ipStr == null){
            return Result.error("通道参数缺少ip");
        }
        if(!RegexUtil.checkIpv4(ipStr)){
            return Result.error("通道ip格式错误，ip:" + ipStr);
        }
        param.ip = ipStr;

        //port
        String portStr = paramMap.get("port");
        if(portStr == null){
            return Result.error("通道参数缺少port");
        }
        if(!RegexUtil.checkPort(portStr)){
            return Result.error("通道port格式错误，port:" + portStr);
        }
        param.port = Integer.parseInt(portStr);

        //channelKey
        param.channelKey = param.ip + param.port;

        //rack
        String rackStr = paramMap.get("rack");
        if(rackStr == null){
            return Result.error("通道参数缺少rack");
        }
        if(!RegexUtil.checkInt(rackStr)){
            return Result.error("通道rack格式错误，rack:" + rackStr);
        }
        param.rack = Integer.parseInt(rackStr);

        //slot
        String slotStr = paramMap.get("slot");
        if(slotStr == null){
            return Result.error("通道参数缺少slot");
        }
        if(!RegexUtil.checkInt(slotStr)){
            return Result.error("通道slot格式错误，slot:" + slotStr);
        }
        param.slot = Integer.parseInt(slotStr);

        //spec
        String specStr = paramMap.get("spec");
        if(specStr == null){
            return Result.error("通道参数缺少型号（spec）");
        }
        Snap7SpecEnum spec = Snap7SpecEnum.typeOfName(specStr);
        if(spec == null){
            return Result.error("通道spec格式错误，spec:" + specStr);
        }
        param.spec = spec;

        //maxVars
        String maxVarsStr = paramMap.get("maxVars");
        if(maxVarsStr != null && !"".equals(maxVarsStr) ){
            if(!RegexUtil.checkInt(maxVarsStr)){
                return Result.error("通道maxVars格式错误，maxVars:" + maxVarsStr);
            }
            param.maxVars = Integer.parseInt(maxVarsStr);
        }

        return Result.ok(param);

    }

    @Override
    public void processRuntimeInfo(ChannelRuntimeInfoField info) {
        processBaseRuntimeInfo(info);
       
        info.setIp(ip);
        info.setPort(port);
        info.setRack(rack);
        info.setSlot(slot);
        info.setSpec(spec.getName());
        info.setMaxVars(maxVars);
    }

    @Override
    public void processChannelElm(ChannelElm channelElm) {
        processBaseChannelElm(channelElm);
       
        channelElm.setIp(ip);
        channelElm.setPort(port);
        channelElm.setRack(rack);
        channelElm.setSlot(slot);
        channelElm.setSpec(spec.getName());
        channelElm.setMaxVars(maxVars);
    }
    
}
