package com.nti56.nlink.product.device.server.domain.thing.channel;

import java.util.List;
import java.util.Map;

import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.client.model.dto.json.ChannelRuntimeInfoField;
import com.nti56.nlink.product.device.client.model.dto.json.device.ChannelElm;
import com.nti56.nlink.product.device.server.entity.ChannelParamEntity;

public class HttpChannelParam extends ChannelParam{
    private String method;
    private String url;
    private String header;
    private String body;

    public static final String[] requiredParam = new String[]{
        "url::true::url地址", 
        "method:GET:true:GET,POST,PUT:请求方法", 
        "header::false::请求头",
        "body::false::请求体",
        "reconnectGapMs:3000:true::断线重连间隔（毫秒）",
        "maxConnection:100:true::通道最多连接数",
        "delayIdleMs:0:true::延迟空闲时间（毫秒）"
    };

    
    public static Result<ChannelParam> checkParam(List<ChannelParamEntity> channelParamList){
        
        HttpChannelParam param = new HttpChannelParam();

        Result<Map<String, String>> baseResult = ChannelParam.checkBase(
            param, requiredParam, channelParamList
        );
        if(!baseResult.getSignal()){
            return Result.error(baseResult.getMessage());
        }
        Map<String, String> paramMap = baseResult.getResult();

        //url
        String url = paramMap.get("url");
        if(url == null){
            return Result.error("通道参数缺少url");
        }
        param.url = url;

        //method
        String method = paramMap.get("method");
        param.method = method;

        //header
        String header = paramMap.get("header");
        param.header = header;

        //body
        String body = paramMap.get("body");
        param.body = body;

        //channelKey
        param.channelKey = param.url;

        return Result.ok(param);

    }

    @Override
    public void processRuntimeInfo(ChannelRuntimeInfoField info) {
        processBaseRuntimeInfo(info);
        info.setMethod(method);
        info.setUrl(url);
        info.setHeader(header);
        info.setBody(body);
    }

    @Override
    public void processChannelElm(ChannelElm channelElm) {
        processBaseChannelElm(channelElm);
        channelElm.setMethod(method);
        channelElm.setUrl(url);
        channelElm.setHeader(header);
        channelElm.setBody(body);
    }

    
}
