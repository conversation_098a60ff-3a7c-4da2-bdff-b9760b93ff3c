//package com.nti56.nlink.product.device.server.config;
//
//import org.neo4j.ogm.session.SessionFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.context.annotation.DependsOn;
//import org.springframework.context.annotation.Primary;
//import org.springframework.data.neo4j.transaction.Neo4jTransactionManager;
//import org.springframework.data.transaction.ChainedTransactionManager;
//import org.springframework.jdbc.datasource.DataSourceTransactionManager;
//import org.springframework.transaction.PlatformTransactionManager;
//
//import javax.sql.DataSource;
//
//
///**
// * 类说明：
// *
// * @ClassName TransactionConfig
// * @Description TODO
// * <AUTHOR>
// * @Date 2022/11/28 17:57
// * @Version 1.0
// */
//
//
//@Configuration
//@DependsOn("sessionFactory")
//public class TransactionConfig {
//
//
//    @Bean("transactionManager")
//    @Primary
//    public DataSourceTransactionManager transactionManager(DataSource dataSource) {
//        return new DataSourceTransactionManager(dataSource);
//    }
//
//    @Bean("neo4jTransactionManager")
//    public Neo4jTransactionManager neo4jTransactionManager(SessionFactory sessionFactory) {
//        return new Neo4jTransactionManager(sessionFactory);
//    }
//
//    @Autowired
//    @Bean(name = "multiTransactionManager")
//    public PlatformTransactionManager multiTransactionManager(
//            Neo4jTransactionManager neo4jTransactionManager,
//            DataSourceTransactionManager mysqlTransactionManager) {
//        return new ChainedTransactionManager(
//                neo4jTransactionManager, mysqlTransactionManager);
//    }
//
//
//}
