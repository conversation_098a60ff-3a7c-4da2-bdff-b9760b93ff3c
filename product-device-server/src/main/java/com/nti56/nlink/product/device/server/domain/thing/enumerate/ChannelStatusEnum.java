package com.nti56.nlink.product.device.server.domain.thing.enumerate;

import lombok.Getter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 类说明: 状态枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 13:33:25
 * @since JDK 1.8
 */
public enum ChannelStatusEnum {
    OFFLINE(0, "offline", "停用"),
    ONLINE(1, "online", "启用")
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    ChannelStatusEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static ChannelStatusEnum typeOfValue(Integer value){
        ChannelStatusEnum[] values = ChannelStatusEnum.values();
        for (ChannelStatusEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static ChannelStatusEnum typeOfName(String name){
        ChannelStatusEnum[] values = ChannelStatusEnum.values();
        for (ChannelStatusEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static ChannelStatusEnum typeOfNameDesc(String nameDesc){
        ChannelStatusEnum[] values = ChannelStatusEnum.values();
        for (ChannelStatusEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }


    public static List toList(){
        List<Map> result = new ArrayList<>();
        ChannelStatusEnum[] values = ChannelStatusEnum.values();
        Map<String,Object> map ;
        for (ChannelStatusEnum v : values) {
            map = new HashMap<>();
            map.put("name",v.name);
            map.put("value",v.value);
            map.put("nameDesc",v.nameDesc);
            result.add(map);
        }
        return result;
    }
}
