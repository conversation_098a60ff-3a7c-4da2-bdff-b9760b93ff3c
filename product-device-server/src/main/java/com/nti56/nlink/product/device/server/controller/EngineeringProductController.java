package com.nti56.nlink.product.device.server.controller;

import com.nti56.nlink.common.util.R;
import com.nti56.nlink.product.device.client.model.dto.engineering.ProductDeviceServerDataDTO;
import com.nti56.nlink.product.device.server.exception.BizException;
import com.nti56.nlink.product.device.server.service.IEngineeringProductService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/11/4 9:39<br/>
 * @since JDK 1.8
 */
//@Slf4j
//@RestController
//@Tag(name = "工程文件-产品")
//public class EngineeringProductController implements EngineeringProductServiceClient {
//
//    @Autowired
//    private IEngineeringProductService engineeringProductService;
//
//
//    @Override
//    public R deleteProductDataByTenantId(Long tenantId) {
//        return R.result(engineeringProductService.deleteProductDataByTenantId(tenantId));
//    }
//
//    @Override
//    public R getProductDataByTenantId(Long tenantId) {
//        return R.result(engineeringProductService.getProductDataByTenantId(tenantId));
//    }
//
//    @Override
//    public R createProductData(ProductDeviceServerDataDTO dto){
//        return R.result(engineeringProductService.createProductData(dto));
//    }
//
//    @Override
//    public R initProductData(ProductDeviceServerDataDTO dto,@PathVariable("tenantId") Long tenantId) {
//        return R.result(engineeringProductService.initProductData(dto,tenantId));
//    }
//}
