package com.nti56.nlink.product.device.server.controller;

import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.PageParam;
import com.nti56.nlink.common.util.R;
import com.nti56.nlink.product.device.server.model.connector.dto.CreateConnectorDTO;
import com.nti56.nlink.product.device.server.model.connector.dto.EditConnectorDTO;
import com.nti56.nlink.product.device.server.model.connector.dto.QueryConnectorDTO;
import com.nti56.nlink.product.device.server.service.IConnectorService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 类说明: 连接器controller
 * <AUTHOR>
 * @version 1.0
 * @since JDK 1.8
 */
@RestController
@RequestMapping("connector")
@Tag(name = "连接器")
public class ConnectorController {
    
    @Autowired
    private IConnectorService connectorService;
    
    @GetMapping("page")
    @Operation(summary = "获取连接器分页")
    public R pageConnector(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, PageParam pageParam, QueryConnectorDTO queryConnectorDTO) {
        return R.result(connectorService.pageConnector(pageParam, queryConnectorDTO, tenantIsolation));
    }
    
    @GetMapping("{id}")
    @Operation(summary = "获取连接器详情")
    public R getConnectorInfo(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, @PathVariable Long id) {
        return R.result(connectorService.getConnectorInfo(id));
    }
    
    @PostMapping("")
    @Operation(summary = "新增连接器")
    public R createConnector(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, @RequestBody @Validated CreateConnectorDTO createConnectorDTO) {
        return R.result(connectorService.createConnector(createConnectorDTO, tenantIsolation));
    }
    
    @PutMapping("{id}")
    @Operation(summary = "修改连接器")
    public R editConnector(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, @RequestBody @Validated EditConnectorDTO editConnectorDTO) {
        return R.result(connectorService.editConnector(editConnectorDTO, tenantIsolation));
    }
    
    @DeleteMapping("{id}")
    @Operation(summary = "逻辑删除连接器")
    public R deleteConnector(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, @PathVariable Long id) {
        return R.result(connectorService.deleteConnector(id, tenantIsolation));
    }
    
    @PutMapping("changeStatus/{id}/{status}")
    @Operation(summary = "修改连接器状态")
    public R changeStatus(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, @PathVariable Long id, @PathVariable("status") Integer status) {
        return R.result(connectorService.changeStatus(id, status, tenantIsolation));
    }
}
