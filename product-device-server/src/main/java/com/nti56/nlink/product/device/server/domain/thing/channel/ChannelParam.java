package com.nti56.nlink.product.device.server.domain.thing.channel;


import cn.hutool.core.bean.BeanUtil;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.client.model.dto.json.ChannelRuntimeInfoField;
import com.nti56.nlink.product.device.client.model.dto.json.device.ChannelElm;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.DriverEnum;
import com.nti56.nlink.product.device.server.entity.ChannelParamEntity;
import com.nti56.nlink.product.device.server.model.channel.vo.ChannelParamVO;
import com.nti56.nlink.product.device.server.util.RegexUtil;
import lombok.Getter;

import java.util.*;
import java.util.Map.Entry;
import java.util.stream.Collectors;

/**
 * 类说明: 抽象通道参数领域对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-05-25 11:56:35
 * @since JDK 1.8
 */
public abstract class ChannelParam {
    
    protected String channelKey;

    protected Integer reconnectGapMs;
    protected Integer maxConnection;
    protected Integer delayIdleMs;

    protected List<ChannelParamEntity> rawChannelParamList;

    @Getter
    private static class RequiredParam {
        private String name;
        private String defaultValue;
        private Boolean necessary;
        private List<String> options;
        private String descript;

        public RequiredParam(String str){
            String[] split = str.split(":");
            Integer length = split.length;
            this.name = split[0];
            if(length > 1){
                this.defaultValue = split[1];
            }
            if(length > 2){
                this.necessary = "true".equals(split[2]);
            }
            if(length > 3){
                this.options = Arrays.asList(split[3].split(","));
            }
            if(length > 4){
                this.descript = split[4];
            }
        }
    }

    public static List<ChannelParamEntity> mergeDefault(
        String[] requiredParam, 
        List<ChannelParamEntity> channelParamList
    ){
        //list转map
        Map<String, ChannelParamEntity> channelParamMap = channelParamList.stream()
            .collect(Collectors.toMap(ChannelParamEntity::getName, t -> t));

        //解析requiredParam
        List<RequiredParam> requiredList = Arrays.asList(requiredParam).stream()
            .map(t -> {
                return new RequiredParam(t);
            })
            .collect(Collectors.toList());
        Set<String> requiredNameSet = requiredList.stream()
            .map(t -> {
                return t.getName();
            })
            .collect(Collectors.toSet());

        
        //必填参数合并默认参数
        List<ChannelParamEntity> list1 = requiredList.stream().map(item -> {
            ChannelParamEntity entity = channelParamMap.get(item.getName());
            if(entity != null){
                return entity;
            }else{
                entity = new ChannelParamEntity();
                entity.setName(item.getName());
                entity.setValue(item.getDefaultValue());
                entity.setDescript(item.getDescript());
                entity.setNecessary(item.getNecessary());
                return entity;
            }
        }).collect(Collectors.toList());
        //用户自定义额外参数
        List<ChannelParamEntity> list2 = channelParamList.stream()
            .filter(t -> {
                return !requiredNameSet.contains(t.getName());
            })
            .collect(Collectors.toList());
        
        List<ChannelParamEntity> list = new ArrayList<>();
        list.addAll(list1);
        list.addAll(list2);
        return list;
    } 

    public static Result<ChannelParam> checkParam(
        DriverEnum driverEnum, 
        Boolean isServer, 
        List<ChannelParamEntity> channelParamList
    ){
        switch(driverEnum){
            case SNAP7: {
                return Snap7ChannelParam.checkParam(channelParamList);
            }
            case MODBUS:
            case ModbusRtuOverTcp: {
                return ModbusChannelParam.checkParam(channelParamList);
            }
            case OPCUA: {
                return OpcUaChannelParam.checkParam(channelParamList);
            }
            case ETHER_IP: {
                return EtherIpChannelParam.checkParam(channelParamList);
            }
            case MELSEC: {
                return MelsecChannelParam.checkParam(channelParamList);
            }
            case KEYENCE_NANO: {
                return KeyenceChannelParam.checkParam(channelParamList);
            }
            case ModbusRtu: {
                return ModbusRtuChannelParam.checkParam(channelParamList);
            }
            case BACNET_IP: {
                return BacnetIpChannelParam.checkParam(channelParamList);
            }
            case ADS: {
                return AdsChannelParam.checkParam(channelParamList);
            }
            case IEC104: {
                return Iec104ChannelParam.checkParam(channelParamList);
            }
            case Panasonic: {
                return PanasonicChannelParam.checkParam(channelParamList);
            }
            case CUSTOM: {
                return CustomChannelParam.checkParam(isServer, channelParamList);
            }
            case FINS_TCP: {
                return FinsChannelParam.checkParam(channelParamList);
            }
            case INOVANCE_TCP: {
                return InovanceChannelParam.checkParam(channelParamList);
            }
            case CVC600: {
                return CVC600ChannelParam.checkParam(channelParamList);
            }
            case DLT645: {
                return DLT645ChannelParam.checkParam(channelParamList);
            }
            case HTTP: {
                return HttpChannelParam.checkParam(channelParamList);
            }
            default: {
                return Result.error("协议不支持");
            }
        }
    }

    public static Result<Void> checkUnique(List<ChannelParamEntity> list){
        //唯一性检查
        Map<String, Long> counting = list.stream()
            .collect(Collectors.groupingBy(ChannelParamEntity::getName, Collectors.counting()));
        Optional<Entry<String, Long>> any = counting.entrySet().stream().filter(t -> {
            return t.getValue() > 1;
        }).findAny();
        if(any.isPresent()){
            return Result.error("通道参数名重复：" + any.get().getKey());
        }

        return Result.ok();
    }
    
    public static Result<Map<String, String>> checkBase(
        ChannelParam param, 
        String[] requiredParam, 
        List<ChannelParamEntity> channelParamList
    ){
        if(channelParamList == null || channelParamList.size() <= 0){
            return Result.error("通道参数不能为空");
        }

        List<ChannelParamEntity> list = mergeDefault(requiredParam, channelParamList);
        
        Result<Void> uniqueResult = checkUnique(list);
        if(!uniqueResult.getSignal()){
            return Result.error(uniqueResult.getMessage());
        }
        param.rawChannelParamList = list;

        //检查公共参数
        Map<String, String> paramMap = list.stream().collect(Collectors.toMap(
            ChannelParamEntity::getName, 
            ChannelParamEntity::getValue
        ));

        //reconnectGapMs
        String reconnectGapMsStr = paramMap.get("reconnectGapMs");
        if(reconnectGapMsStr == null){
            return Result.error("通道参数缺少reconnectGapMs");
        }
        if(!RegexUtil.checkInt(reconnectGapMsStr)){
            return Result.error("通道reconnectGapMs格式错误，reconnectGapMs:" + reconnectGapMsStr);
        }
        param.reconnectGapMs = Integer.parseInt(reconnectGapMsStr);

        //maxConnection
        String maxConnectionStr = paramMap.get("maxConnection");
        if(maxConnectionStr == null){
            return Result.error("通道参数缺少maxConnection");
        }
        if(!RegexUtil.checkInt(maxConnectionStr)){
            return Result.error("通道maxConnection格式错误，maxConnection:" + maxConnectionStr);
        }
        param.maxConnection = Integer.parseInt(maxConnectionStr);

        //delayIdleMs
        String delayIdleMsStr = paramMap.get("delayIdleMs");
        if(delayIdleMsStr == null){
            return Result.error("通道参数缺少delayIdleMs");
        }
        if(!RegexUtil.checkInt(delayIdleMsStr)){
            return Result.error("通道delayIdleMs格式错误，delayIdleMs:" + delayIdleMsStr);
        }
        param.delayIdleMs = Integer.parseInt(delayIdleMsStr);

        return Result.ok(paramMap);
    }

    public abstract void processRuntimeInfo(ChannelRuntimeInfoField info);


    protected void processBaseRuntimeInfo(ChannelRuntimeInfoField info){
        info.setChannelKey(channelKey);
        info.setReconnectGapMs(reconnectGapMs);
        info.setMaxConnection(maxConnection);
        info.setDelayIdleMs(delayIdleMs);
    }

    public abstract void processChannelElm(ChannelElm channelElm);

    protected void processBaseChannelElm(ChannelElm channelElm){
        channelElm.setChannelKey(channelKey);
        channelElm.setReconnectGapMs(reconnectGapMs);
        channelElm.setMaxConnection(maxConnection);
        channelElm.setDelayIdleMs(delayIdleMs);
    }

    public List<ChannelParamVO> toVoList() {
        return rawChannelParamList.stream()
            .map(t -> {
                ChannelParamVO vo = new ChannelParamVO();
                BeanUtil.copyProperties(t, vo);
                return vo;
            })
            .collect(Collectors.toList());
    }

    public List<ChannelParamEntity> toEntityList() {
        return rawChannelParamList;
    }


}
