package com.nti56.nlink.product.device.server.controller;

import com.alibaba.fastjson.JSON;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.*;
import com.nti56.nlink.product.device.server.entity.DeviceEntity;
import com.nti56.nlink.product.device.server.exception.BizException;
import com.nti56.nlink.product.device.server.model.*;
import com.nti56.nlink.product.device.server.service.IDeviceBindRelationService;
import com.nti56.nlink.product.device.server.service.IDeviceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.List;
import java.util.function.Function;

/**
 * 类说明: 设备controller
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 13:32:04
 * @since JDK 1.8
 */
@RestController
@RequestMapping("device/bind")
@Tag(name = "设备绑定模块")
@Slf4j
public class DeviceBindRelationController {

    @Autowired
    IDeviceBindRelationService deviceBindRelationService;

    @GetMapping("{edgeGatewayId}/{channelId}/{labelGroupName}")
    @Operation(summary = "获取设备绑定关系")
    public R getRelationByGroup(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@PathVariable Long edgeGatewayId,@PathVariable Long channelId,@PathVariable String labelGroupName) {
        if (ObjectUtils.isEmpty(edgeGatewayId) || ObjectUtils.isEmpty(channelId) || ObjectUtils.isEmpty(labelGroupName)) {
            return R.error(ServiceCodeEnum.CODE_PARAM_ERROR);
        }
        Result<List<DeviceEntity>> result = deviceBindRelationService.getRelationByGroup(tenantIsolation, edgeGatewayId,channelId,labelGroupName);
        return R.result(result);
    }

    @PutMapping("batch/label_group/{operate}/{level}")
    @Operation(summary = "批量设备分组绑定操作" ,parameters = {
            @Parameter(name = "operate",description = "1-修改分组，2-增加分组，3-删减分组"),
            @Parameter(name = "level",description = "要操作的层级")
    })
    public R deviceBatchLabelGroupBind(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@RequestBody DeviceRequestBo requestBo,@PathVariable Integer operate,@PathVariable Integer level) {
        checkDeviceRequestParam(tenantIsolation,requestBo,DeviceRequestBo::getIds);
        Result<List<DeviceRespondBo>> result = deviceBindRelationService.deviceBatchLabelGroupBind(tenantIsolation, requestBo, operate, level);
        return R.result(result);
    }

    @PutMapping("batch/channel/bind")
    @Operation(summary = "批量设备通道绑定")
    public R deviceBatchChannelBind(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@RequestBody DeviceRequestBo requestBo) {
        checkDeviceRequestParam(tenantIsolation,requestBo,DeviceRequestBo::getChannel,DeviceRequestBo::getIds);
        Result<List<DeviceRespondBo>> result = deviceBindRelationService.deviceBatchChannelBind(tenantIsolation, requestBo);
        return R.result(result);
    }


    @PutMapping("")
    @Operation(summary = "创建绑定关系")
    public R createOrEditBindRelation(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@RequestBody DeviceRequestBo requestBo) {
        checkDeviceRequestParam(tenantIsolation,requestBo,DeviceRequestBo::getChannel,DeviceRequestBo::getSource,DeviceRequestBo::getId);
        Result<Void> result = deviceBindRelationService.createOrEditBindRelation(tenantIsolation,requestBo);
        return R.result(result);
    }

    @DeleteMapping("batch/delete")
    @Operation(summary = "按条件批量删除设备绑定关系")
    public R deviceBindRelationBatchDelete(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@RequestBody DeviceRequestBo requestBo) {
        checkDeviceRequestParam(tenantIsolation,requestBo,DeviceRequestBo::getIds);
        Result<List<DeviceRespondBo>> result = deviceBindRelationService.deviceBindRelationBatchDelete(tenantIsolation,requestBo.getIds());
        return R.result(result);
    }

    private void checkDeviceRequestParam(TenantIsolation tenantIsolation,DeviceRequestBo dto, Function<DeviceRequestBo,?> ... checkProperties) {
        log.info("维护设备绑定关系，租户：{}，入参：{}",tenantIsolation.getTenantId(), JSON.toJSONString(dto));
        if (BeanUtilsIntensifier.checkBeanAndProperties(tenantIsolation, TenantIsolation::getTenantId)) {
            throw new BizException(ServiceCodeEnum.CODE_PARAM_ERROR);
        }
        if (BeanUtilsIntensifier.checkBeanAndProperties(dto, checkProperties)) {
            throw new BizException(ServiceCodeEnum.CODE_PARAM_ERROR);
        }
    }

}
