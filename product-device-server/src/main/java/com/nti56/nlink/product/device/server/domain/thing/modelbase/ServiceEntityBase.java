package com.nti56.nlink.product.device.server.domain.thing.modelbase;

import com.nti56.nlink.product.device.client.model.dto.json.InputDataField;
import com.nti56.nlink.product.device.client.model.dto.json.OutputDataField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-05-26 09:01:21
 * @since JDK 1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ServiceEntityBase {
    
    private Long id;
    private String serviceName;
    private String descript;
    private Boolean override;
    private Boolean async;
    private InputDataField[] inputData;
    private OutputDataField outputData;
    private String outputDataDescript;
    private String serviceCode;
    private Integer serviceType;

}
