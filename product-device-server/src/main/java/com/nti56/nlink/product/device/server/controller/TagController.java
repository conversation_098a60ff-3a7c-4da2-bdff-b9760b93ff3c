package com.nti56.nlink.product.device.server.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.R;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.nlink.product.device.client.model.req.ListTagReq;
import com.nti56.nlink.product.device.client.model.req.TagReq;
import com.nti56.nlink.product.device.client.model.rsp.TagRsp;
import com.nti56.nlink.product.device.server.service.ITagService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lombok.extern.slf4j.Slf4j;
import org.dozer.Mapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Validator;
import java.util.List;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/2/14 17:52<br/>
 * @since JDK 1.8
 */
@RestController
@RequestMapping("tags")
@Slf4j
@io.swagger.v3.oas.annotations.tags.Tag(name = "tag", description = "标记模块")
@SecurityRequirement(name = "token")
public class TagController {

  @Autowired
  private ITagService tagService;

  @Autowired
  private Mapper dozerMapper;

  @Autowired
  private Validator validator;

  @GetMapping(value = "page", produces = MediaType.APPLICATION_JSON_VALUE)
  @Operation(summary = "标记分页查询", parameters = {
          @Parameter(name = "size", in = ParameterIn.QUERY, schema = @Schema(defaultValue = "10", allowableValues = {"10", "20", "50", "100"}),
                  description = "每页获取多少条", required = true),
          @Parameter(name = "current", in = ParameterIn.QUERY, schema = @Schema(defaultValue = "1")
                  , description = "要获取的页码", required = true),
          @Parameter(name = "req", in = ParameterIn.QUERY, schema = @Schema(implementation = TagReq.class), description = "查询参数"),
          @Parameter(name = "ot_headers", schema = @Schema(implementation = TenantIsolation.class), in = ParameterIn.HEADER, description = "请求头", required = true)
  },
          responses = {
                  @ApiResponse(content = {
                          @Content(
                                  mediaType = "application/json",
                                  array = @ArraySchema(schema = @Schema(implementation = TagRsp.class)))
                  })},
          tags = {"tag"}
  )
  public R pageList(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                    TagReq req, Integer size, Integer current) {
    try {
      Result<Page<TagRsp>> page = tagService.findByPage(req, size, current, tenantIsolation);
      if (page.getSignal()) {
        return R.ok(page.getResult());
      }
      return R.error(page.getServiceCode(), page.getMessage());
    } catch (Exception e) {
      log.error("engine page error {}", e.getMessage(), e);
    }
    return R.error(ServiceCodeEnum.CODE_GET_PAGE_FAIL);
  }

  @GetMapping(value = "lists", produces = MediaType.APPLICATION_JSON_VALUE)
  @Operation(summary = "标签查询", parameters = {
          @Parameter(name = "req", in = ParameterIn.QUERY, schema = @Schema(implementation = ListTagReq.class), description = "查询参数"),
          @Parameter(name = "ot_headers", schema = @Schema(implementation = TenantIsolation.class), in = ParameterIn.HEADER, description = "请求头", required = true)
  },
          responses = {
                  @ApiResponse(content = {
                          @Content(
                                  mediaType = "application/json",
                                  array = @ArraySchema(schema = @Schema(implementation = TagRsp.class)))
                  })},
          tags = {"tag"}
  )
  public R list(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, @Validated ListTagReq req) {
    try {
      Result<List<TagRsp>> page = tagService.list(req, tenantIsolation);
      if (page.getSignal()) {
        return R.ok(page.getResult());
      }
      return R.error(page.getServiceCode(), page.getMessage());
    } catch (Exception e) {
      log.error("tag list error {}", e.getMessage(), e);
    }
    return R.error(ServiceCodeEnum.CODE_GET_FAIL);
  }

  @PostMapping(value = "lists/ids", produces = MediaType.APPLICATION_JSON_VALUE)
  @Operation(summary = "根据ids获取标记列表",
          parameters = {
                  @Parameter(name = "ot_headers", schema = @Schema(implementation = TenantIsolation.class),
                          in = ParameterIn.HEADER, description = "请求头", required = true)

          },
          responses = {
                  @ApiResponse(content = {
                          @Content(
                                  mediaType = "application/json",
                                  array = @ArraySchema(schema = @Schema(implementation = TagRsp.class)))
                  })},
          tags = {"tag"}
  )
  public R listByIds(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                     @Parameter(name = "ids", content = @Content(
                             array = @ArraySchema(schema = @Schema(implementation = Long.class))),
                             description = "标记id列表") @RequestBody List<Long> ids) {
    try {

      if(ids == null || ids.size() <= 0){
        return R.ok();
      }
      Result<List<TagRsp>> page = tagService.listByIds(tenantIsolation, ids);
      log.info("{}", JSON.toJSONString(page));
      if (page.getSignal()) {
        return R.ok(page.getResult());
      }
      return R.ok();
    } catch (Exception e) {
      log.error("tag list ids error {}", e.getMessage(), e);
      return R.error(ServiceCodeEnum.CODE_GET_FAIL);
    }
  }

  @PostMapping(produces = MediaType.APPLICATION_JSON_VALUE)
  @Operation(summary = "创建标记",
          description = "创建一个标记",
          parameters = {
                  @Parameter(name = "ot_headers", schema = @Schema(implementation = TenantIsolation.class),
                          in = ParameterIn.HEADER, description = "请求头", required = true)

          },
          responses = {
                  @ApiResponse(content = {
                          @Content(
                                  mediaType = "application/json",
                                  schema = @Schema(implementation = TagRsp.class))
                  })}, tags = {"tag"}
  )
  public R save(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                @Parameter(name = "req", schema = @Schema(implementation = TagReq.class),
                        description = "新增参数")
                @RequestBody @Validated TagReq req) {
    try {
      Result<TagRsp> rsp = tagService.save(req, tenantIsolation);
      if (rsp.getSignal()) {
        return R.ok(rsp.getResult());
      }
      return R.error(rsp.getServiceCode(), rsp.getMessage());
    } catch (Exception e) {
      log.error(e.getMessage(), e);
    }
    return R.error(ServiceCodeEnum.CODE_CREATE_FAIL);
  }

  @GetMapping(value = "/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
  @Operation(summary = "获取一个标记", parameters = {
          @Parameter(name = "id", in = ParameterIn.PATH, schema = @Schema(implementation = Long.class), description = "查询参数"),
          @Parameter(name = "ot_headers", schema = @Schema(implementation = TenantIsolation.class),
                  in = ParameterIn.HEADER, description = "请求头", required = true)},
          responses = {
                  @ApiResponse(content = {
                          @Content(
                                  mediaType = "application/json",
                                  schema = @Schema(implementation = TagRsp.class))
                  })},
          tags = {"tag"}
  )
  public R get(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
               @PathVariable Long id) {
    Result<TagRsp> result = tagService.get(tenantIsolation, id);
    if (result.getSignal()) {
      return R.ok(result.getResult());
    }
    return R.error(result.getServiceCode(), result.getMessage());
  }

  @PutMapping(value = "/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
  @Operation(summary = "更新标记", parameters = {
          @Parameter(name = "id", in = ParameterIn.PATH, schema = @Schema(implementation = Long.class), description = "查询参数"),
          @Parameter(name = "ot_headers", schema = @Schema(implementation = TenantIsolation.class),
                  in = ParameterIn.HEADER, description = "请求头", required = true)
  },
          responses = {
                  @ApiResponse(content = {
                          @Content(
                                  mediaType = "application/json",
                                  schema = @Schema(implementation = Boolean.class))
                  })},
          tags = {"tag"}
  )
  public R update(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                  @PathVariable Long id, @RequestBody @Validated TagReq req) {
    try {
      Result<Boolean> result = tagService.update(req, id, tenantIsolation);
      if (result.getSignal()) {
        return R.ok(true);
      }
    } catch (Exception e) {
      log.error("update {}", e.getMessage(), e);
    }
    return R.error(ServiceCodeEnum.CODE_UPDATE_FAIL);


  }

  @DeleteMapping(value = "/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
  @Operation(summary = "删除标记", parameters = {
          @Parameter(name = "id", in = ParameterIn.PATH, schema = @Schema(implementation = Long.class), description = "查询参数"),
          @Parameter(name = "ot_headers", schema = @Schema(implementation = TenantIsolation.class),
                  in = ParameterIn.HEADER, description = "请求头", required = true),
  },
          responses = {
                  @ApiResponse(content = {
                          @Content(
                                  mediaType = "application/json",
                                  schema = @Schema(implementation = Boolean.class))
                  })},
          tags = {"tag"}
  )
  public R delete(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                  @PathVariable Long id,
                  boolean verifyBind) {
    try {
      Result<Boolean> result = tagService.delete(tenantIsolation, id);
      if (result.getSignal()) {
        return R.ok(true);
      }
    } catch (Exception e) {
      log.error("delete {}", e.getMessage(), e);
    }
    return R.error(ServiceCodeEnum.CODE_DELETE_FAIL);
  }

  @GetMapping(value = "/verifyBind/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
  @Operation(summary = "查询标记是否被引用", parameters = {
          @Parameter(name = "id", in = ParameterIn.PATH, schema = @Schema(implementation = Long.class), description = "查询参数"),
          @Parameter(name = "ot_headers", schema = @Schema(implementation = TenantIsolation.class),
                  in = ParameterIn.HEADER, description = "请求头", required = true)},
          responses = {
                  @ApiResponse(content = {
                          @Content(
                                  mediaType = "application/json",
                                  schema = @Schema(implementation = TagRsp.class))
                  })},
          tags = {"tag"}
  )
  public R verifyBind(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                      @PathVariable Long id) {
    Result<Boolean> result = tagService.verifyBind(tenantIsolation.getTenantId(), id);
    return R.ok(result.getResult());
  }


}
