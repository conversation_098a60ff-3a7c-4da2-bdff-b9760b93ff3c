package com.nti56.nlink.product.device.server.config.websocket;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ConcurrentHashSet;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.socket.WebSocketSession;

import java.io.IOException;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName WsSessionManager
 * @date 2022/9/22 18:36
 * @Version 1.0
 */
@Slf4j
public class WsSessionManager {

    public static final String LABEL_SESSION_KEY_PREFIX = "LABEL#";
    public static final String PROPERTY_SESSION_KEY_PREFIX = "PROPERTY#";

    /**
     * 保存连接 session 的地方
     */
    private static ConcurrentHashMap<String, WebSocketSession> SESSION_POOL = new ConcurrentHashMap<>();

    private static ConcurrentHashMap<String, ConcurrentHashSet<Long>> SESSION_INIT_POOL = new ConcurrentHashMap<>();

    /**
     * 添加 session
     *
     * @param key
     */
    public static void add(String key, WebSocketSession session) {
        log.info("websocket客户端已连接：{}",key);
        SESSION_POOL.put(key, session);
    }

    /**
     * 删除 session,会返回删除的 session
     *
     * @param key
     * @return
     */
    public static WebSocketSession remove(String key) {
        log.info("websocket客户端已删除：{}",key);
        return SESSION_POOL.remove(key);
    }

    /**
     * 删除并同步关闭连接
     *
     * @param key
     */
    public static void removeAndClose(String key) {
        WebSocketSession session = remove(key);
        log.info("websocket客户端已删除：{}",key);
        if (session != null) {
            try {
                session.close();
                log.info("websocket客户端已关闭：{}",key);
            } catch (IOException e) {
                log.error("websocket客户端关系异常,key:{},errorMsg:{}",key,e.getMessage());
            }
        }
    }

    /**
     * 判断是否存在 session
     *
     * @param key
     * @return
     */
    public static Boolean containsKey(String key) {
        if (SESSION_POOL.containsKey(key)) {
            return true;
        }
        return false;
    }
    
    public static Boolean containsPrefix(String prefix) {
        Optional<String> first = SESSION_POOL.keySet().stream().filter(s -> s.startsWith(prefix)).findFirst();
        return first.isPresent();
    }


    /**
     * 获得 session
     *
     * @param key
     * @return
     */
    public static WebSocketSession get(String key) {
        log.info("获取websocket客户端：{}",key);
        return SESSION_POOL.get(key);
    }

    public static Set<Map.Entry<String, WebSocketSession>> list(String prefix) {
        log.debug("获取网关：{}相关websocket客户端",prefix);
        return SESSION_POOL.entrySet().stream().filter(entry -> entry.getKey().startsWith(prefix)).collect(Collectors.toSet());
    }

    
    public static void initSessionPool(String sessionId, ConcurrentHashSet<Long> channelIds){
        SESSION_INIT_POOL.put(sessionId,channelIds);
    }

    public static boolean isInit(String sessionId){
        return SESSION_INIT_POOL.containsKey(sessionId);
    }

    public static void removeSessionInit(String sessionId,Long channelId){
        if (SESSION_INIT_POOL.containsKey(sessionId)) {
            SESSION_INIT_POOL.get(sessionId).remove(channelId);
        }
    }

    public static void removeSessionInit(String sessionId){
        SESSION_INIT_POOL.remove(sessionId);
    }

}
