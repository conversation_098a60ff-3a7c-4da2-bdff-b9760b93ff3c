package com.nti56.nlink.product.device.server.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.*;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.ResourceTypeEnum;
import com.nti56.nlink.product.device.server.entity.TagBindRelationEntity;
import com.nti56.nlink.product.device.server.model.DeviceDto;
import com.nti56.nlink.product.device.server.service.IDeviceService;
import com.nti56.nlink.product.device.server.service.ITagBindRelationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;


/**
 * <p>
 * 标志关系表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2022-04-11 17:27:49
 * @since JDK 1.8
 */
@RestController
@RequestMapping("")
@Tag(name = "标志关系表")
public class TagBindRelationController {

    @Autowired
    ITagBindRelationService service;

    @Autowired
    IDeviceService deviceService;

    @GetMapping("tag_bind_relation/list")
    @Operation(summary = "获取列表")
    public R list(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, TagBindRelationEntity entity) {
        BeanUtilsIntensifier.propertyInjection(tenantIsolation,entity);
        Result<List<TagBindRelationEntity>> result = service.list(entity);
        return R.result(result);
    }

    @GetMapping("tag_bind_relation/list/{resourceType}")
    @Operation(summary = "获取标记列表")
    public R listTag(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@PathVariable @Param("资源类型:1-产品 2-设备 3-网关 4-标签 5-物模型") Integer resourceType){
        ResourceTypeEnum type = ResourceTypeEnum.typeOfValue(resourceType);
        if (!Optional.ofNullable(type).isPresent()) {
            return R.error(ServiceCodeEnum.CODE_PARAM_ERROR);
        }
        return service.getTagList(tenantIsolation,type.getValue());
    }

    @DeleteMapping("tag_bind_relation/{entityId}")
    @Operation(summary = "删除对象")
    public R delete(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@Parameter(description = "目标ID") @PathVariable Long entityId) {
        Result result = service.deleteById(tenantIsolation.getTenantId(),entityId);
        return R.result(result);
    }

}
