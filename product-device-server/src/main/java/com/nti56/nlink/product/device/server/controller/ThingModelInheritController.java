package com.nti56.nlink.product.device.server.controller;

import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.R;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.entity.ThingModelInheritEntity;
import com.nti56.nlink.product.device.server.model.ThingModelInheritBo;
import com.nti56.nlink.product.device.server.service.IThingModelInheritService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.function.Function;


/**
 * <p>
 * 物模型继承表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2022-04-13 15:37:55
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/")
@Tag( name = "物模型继承模块")
public class ThingModelInheritController {

    @Autowired
    IThingModelInheritService service;

    @GetMapping("thing-model-inherit/list")
    @Operation(summary = "获取物模型已继承的物模型列表",
    parameters = {
            @Parameter(name = "thingModelId",description = "物模型id",required = true)
    })
    public R list(
        @RequestHeader("ot_headers") TenantIsolation tenantIsolation,
        Long thingModelId
    ){
        Result<List<ThingModelInheritBo>> result = service.listBoByThingModelId(tenantIsolation, thingModelId);
        return R.result(result);
    }

    @PostMapping("thing-model-inherit")
    @Operation(summary = "创建对象")
    public R create(
        @RequestHeader("ot_headers") TenantIsolation tenantIsolation,
        @Parameter(description = "对象") @RequestBody ThingModelInheritEntity entity
    ){
        
        Result<ThingModelInheritEntity> result = service.save(tenantIsolation, entity);
        return R.result(result);
    }

    @DeleteMapping("thing-model-inherit/{entityId}")
    @Operation(summary = "删除对象")
    public R delete(
        @RequestHeader("ot_headers") TenantIsolation tenantIsolation,
        @Parameter(description = "目标ID") @PathVariable Long entityId
    ){
        Result<Integer> result = service.deleteById(tenantIsolation, entityId);
        return R.result(result);
        }

    @GetMapping("thing-model-inherit/{entityId}")
    @Operation(summary = "获取对象")
    public R get(
        @RequestHeader("ot_headers") TenantIsolation tenantIsolation,
        @Parameter(description = "目标ID") @PathVariable Long entityId
    ){
        Result<ThingModelInheritEntity> result = service.getById(tenantIsolation, entityId);
        return R.result(result);
    }

}
