package com.nti56.nlink.product.device.server.domain.redirect;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.entity.InstanceRedirectEntity;
import com.nti56.nlink.product.device.server.service.impl.strategy.MQTTClientPool;
import io.netty.handler.codec.mqtt.MqttQoS;
//import io.vertx.mqtt.MqttClient;
//import io.vertx.mqtt.MqttClientOptions;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.paho.mqttv5.client.MqttClient;
import org.eclipse.paho.mqttv5.client.MqttConnectionOptions;
import org.eclipse.paho.mqttv5.client.persist.MemoryPersistence;
import org.eclipse.paho.mqttv5.common.MqttException;
import org.springframework.util.PropertyPlaceholderHelper;

import java.nio.charset.StandardCharsets;
import java.util.Objects;
import java.util.Properties;
import java.util.Set;
import java.util.UUID;


/**
 * 类说明：
 *
 * @ClassName MQTTRedirectFn
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/9/6 10:49
 * @Version 1.0
 */
@Data
@Slf4j
public class MQTTRedirectFn implements RedirectFn {

    private String ip;

    private Integer port;

    private String username;

    private String password;

    private boolean needAuth = false;

    private boolean needTLS = false;

    private String keyPath;

    private String certPath;

    private int keepAlive;

    private long messageResendInterval;

    private String topic;

    private MqttQoS mqttQoS;
    // default AT_LEAST_ONCE
    private int qos = 1;

    private Object payload;

    private Long redirectId;

    private boolean testConnect = false;

    private final static Long TEST_REDIRECT_ID = 1L;

    private String clientId;

    /**
     * 重连间隔 默认30s
     */
    private Long reconnectGapTime = 60000L;

    private static final PropertyPlaceholderHelper propertyPlaceholderHelper = new PropertyPlaceholderHelper("${", "}");

    public static MQTTRedirectFn getFunctionInstance(InstanceRedirectEntity instanceRedirectEntity, Object payload) {
        try {
            MQTTRedirectFn mqttRedirectFn = JSONUtil.toBean(instanceRedirectEntity.getRedirectFn(), MQTTRedirectFn.class);
            mqttRedirectFn.setMqttQoS(MqttQoS.valueOf(mqttRedirectFn.getQos()));
            mqttRedirectFn.setRedirectId(instanceRedirectEntity.getId());
            if (!Objects.isNull(payload)) {
                //处理占位符
                JSONObject message = JSONObject.parseObject(JSONUtil.toJsonStr(payload));
                if (StrUtil.isNotBlank(message.getString("payload"))) {
                    mqttRedirectFn.setPayload(message.get("payload"));
                }
                JSONObject topicJSON = message.getJSONObject("topic");
                if (!Objects.isNull(topicJSON)) {
                    Set<String> keys = topicJSON.keySet();
                    Properties properties = new Properties();
                    for (String key : keys) {
                        if (StrUtil.isBlank(key) || Objects.isNull(topicJSON.getString(key))) {
                            log.error("topic params error.topic:{},redirect id:{},param:{}", mqttRedirectFn.getTopic(), mqttRedirectFn.getRedirectId(), JSONUtil.toJsonStr(payload));
                            return null;
                        }
                        properties.put(key, topicJSON.getString(key));
                    }
                    mqttRedirectFn.setTopic(propertyPlaceholderHelper.replacePlaceholders(mqttRedirectFn.getTopic(), properties));
                }
            }
            ;
            return mqttRedirectFn;
        } catch (Exception e) {
            log.error("phrase mqtt redirect function error,can't phrase to Bean errorMsg:{}",e.getMessage());
        }
        return null;
    }

    @Override
    public Result<Object> execFn() {
        if (testConnect && Objects.isNull(redirectId)) {
            redirectId = TEST_REDIRECT_ID;
        }
        if (Objects.isNull(redirectId)) {
            log.error("redirect id null,exec abort");
            return Result.error("redirect id null,exec abort");
        }
        MqttClient client = MQTTClientPool.getClient(redirectId);
        if (Objects.isNull(client)) {
            if(MQTTClientPool.cannotConnect(ip,port,redirectId)){
                return null;
            }
            //创建client
            client = createMqttClient();
        }
        if(Objects.isNull(client)){
            return Result.error("mqtt连接失败");
        }
        //用client发送消息
        publishMsg(client);
        return Result.ok();
    }

    @Override
    public Result<Object> execFnSync() {
        Result<Boolean> connectResult = testConnect();
        if (connectResult.getSignal() && connectResult.getResult()) {
            return Result.ok();
        }
        return Result.error("mqtt连接失败");
    }


    private Result<Boolean> testConnect() {
        MqttConnectionOptions options = new MqttConnectionOptions();
        options.setKeepAliveInterval(keepAlive);
        options.setUserName(username);
        options.setPassword(password.getBytes());
        PahoMqttClientFn pahoMqttClientFn = new PahoMqttClientFn();
        String url = String.format("tcp://%s:%d", ip, port);
        try {
            return pahoMqttClientFn.connect(url, clientId, options);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return Result.error("mqtt连接失败");

    }

    private MqttClient createMqttClient(){
        MqttConnectionOptions options = new MqttConnectionOptions();
        options.setKeepAliveInterval(keepAlive);
        options.setMaxReconnectDelay(10000);

        if (needAuth || (StringUtils.isNotBlank(username) && StringUtils.isNotBlank(password))) {
            options.setUserName(username);
            options.setPassword(password.getBytes());
        }
       if (StrUtil.isEmpty(clientId)) {
            clientId= UUID.randomUUID().toString();
       }else{
           clientId = String.format("%s::%s", clientId, UUID.randomUUID().toString());
       }

        String url = String.format("tcp://%s:%d", ip, port);
        try {
            // 使用MemoryPersistence来避免创建临时目录
            MemoryPersistence persistence = new MemoryPersistence();
            MqttClient client = new MqttClient(url, clientId,persistence);
            client.connect(options);
            MQTTClientPool.putRecentConnectTime(redirectId);
            MQTTClientPool.register(redirectId,client);
            return client;
        } catch (MqttException e) {
            MQTTClientPool.putRedirectConnectFailedTime(redirectId);
            log.warn("mqtt连接失败  clientId:{},ip:{},port:{},topic:{},redirectId:{}",clientId,ip,port,topic,redirectId);
            log.error(e.getMessage(),e);
        }
        return null;
    }


    private void publishMsg(MqttClient client) {
        if (client.isConnected()) {
            try {
                client.publish(topic, JSONUtil.toJsonStr(payload).getBytes(StandardCharsets.UTF_8), qos, false);
            } catch (MqttException e) {
                log.error(e.getMessage());
            }
        } else {
            MQTTClientPool.removeClient(redirectId);
        }
    }

    @Override
    public String getTargetUrl() {
        return this.topic;
    }


}
