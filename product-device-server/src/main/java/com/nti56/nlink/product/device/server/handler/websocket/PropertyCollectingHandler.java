package com.nti56.nlink.product.device.server.handler.websocket;

import cn.hutool.core.collection.ConcurrentHashSet;
import com.alibaba.fastjson.JSON;
import com.nti56.nlink.common.util.R;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.nlink.product.device.server.config.websocket.WsSessionManager;
import com.nti56.nlink.product.device.server.entity.ChannelEntity;
import com.nti56.nlink.product.device.server.exception.BizException;
import com.nti56.nlink.product.device.server.service.IChannelService;
import com.nti56.nlink.product.device.server.service.IEdgeGatewayService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.ConcurrentWebSocketSessionDecorator;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import java.io.IOException;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023-09-01 14:34:40
 * @since JDK 1.8
 */
@Slf4j
@Component
public class PropertyCollectingHandler extends TextWebSocketHandler {



    /**
     * socket 建立成功事件
     *
     * @param session
     * @throws Exception
     */
    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        Long edgeGatewayId = (Long) session.getAttributes().get("edgeGatewayId");
        Long tenantId = (Long) session.getAttributes().get("tenantId");
        String sessionId = (String) session.getAttributes().get("sessionId");
        String key = WsSessionManager.PROPERTY_SESSION_KEY_PREFIX + edgeGatewayId.toString() + "#" + sessionId;
        ConcurrentWebSocketSessionDecorator decorator = new ConcurrentWebSocketSessionDecorator(session,500,20 * 1024 * 1024);
        if (tenantId != null) {
            WsSessionManager.add(key, decorator);
            log.info("websocket客户端创建连接成功，edgeGatewayId：{}，tenantId：{}，sessionId：{}",edgeGatewayId,tenantId,sessionId);
        } else {
            throw new BizException("websocket无效的会话！租户ID为空！");
        }
    }

    /**
     * 接收消息事件
     *
     * @param session
     * @param message
     * @throws Exception
     */
    @Override
    protected void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {
        String payload = message.getPayload();
        log.debug("websocket接收消息：{}",payload);
        if ("test".equals(payload)) {
            session.sendMessage(new TextMessage(payload));
        }
        String sessionId = (String) session.getAttributes().get("sessionId");
        WsSessionManager.removeSessionInit(sessionId);
    }



    /**
     * socket 断开连接时
     *
     * @param session
     * @param status
     * @throws Exception
     */
    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) throws Exception {
        Long edgeGatewayId = (Long) session.getAttributes().get("edgeGatewayId");
        log.info("websocket客户端已关闭：{}",session.getAttributes().get("sessionId"));
        if (edgeGatewayId != null) {
            WsSessionManager.removeAndClose(edgeGatewayId.toString());
        }
    }
}
