package com.nti56.nlink.product.device.server.domain.thing.enumerate;

import lombok.Getter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 类说明: 设备状态枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023-02-1:33:25
 * @since JDK 1.8
 */
public enum DeviceStatusEnum {
    INACTIVATED(0, "inactivated", "未激活"),
    DEACTIVATED(1, "deactivate", "停用"),
    ONLINE(2, "online", "上线"),
    OFFLINE(3, "offline", "离线")
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    DeviceStatusEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static DeviceStatusEnum typeOfValue(Integer value){
        DeviceStatusEnum[] values = DeviceStatusEnum.values();
        for (DeviceStatusEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static DeviceStatusEnum typeOfName(String name){
        DeviceStatusEnum[] values = DeviceStatusEnum.values();
        for (DeviceStatusEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static DeviceStatusEnum typeOfNameDesc(String nameDesc){
        DeviceStatusEnum[] values = DeviceStatusEnum.values();
        for (DeviceStatusEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }


    public static List toList(){
        List<Map> result = new ArrayList<>();
        DeviceStatusEnum[] values = DeviceStatusEnum.values();
        Map<String,Object> map ;
        for (DeviceStatusEnum v : values) {
            map = new HashMap<>();
            map.put("name",v.name);
            map.put("value",v.value);
            map.put("nameDesc",v.nameDesc);
            result.add(map);
        }
        return result;
    }
}
