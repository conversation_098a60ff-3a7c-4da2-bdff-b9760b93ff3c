package com.nti56.nlink.product.device.server.annotation.aspect;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.nti56.nlink.common.tenant.TenantIsolationThreadLocal;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.annotation.AuditLog;
import com.nti56.nlink.product.device.server.entity.AuditLogEntity;
import com.nti56.nlink.product.device.server.service.AuditLogService;
import com.nti56.nlink.common.util.JwtUserInfoUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;

@Aspect
@Component
@Slf4j
public class AuditLogAspect {


    private final AuditLogService auditLogService;

    @Autowired
    public AuditLogAspect(AuditLogService auditLogService) {
        this.auditLogService = auditLogService;
    }

    @AfterReturning(value = "@annotation(auditLog)", returning = "result")
    public void logAudit(JoinPoint joinPoint, AuditLog auditLog, Object result) {
        // 获取方法参数或其他审计信息
        String action = auditLog.action().getDesc();
        String target = auditLog.target().getDesc();
        String details = auditLog.details();
        String userJson = JwtUserInfoUtils.userJson();
        // 记录审计日志
        AuditLogEntity log = new AuditLogEntity();
        if(StrUtil.isNotBlank(userJson)){
            JSONObject jsonObject = JSON.parseObject(userJson);
            String userInfo = jsonObject.getString("userInfo");
            JSONObject userInfoObject = JSON.parseObject(userInfo);
            Long userId =  userInfoObject.getLong("id");
            log.setTenantId(TenantIsolationThreadLocal.getTenantId());
            String userName =  userInfoObject.getString("empName");
            log.setUserId(userId); // 设置当前用户的ID
            log.setUserName(userName);
        }
        if(!Objects.isNull(result)){
            if(result instanceof Result){
                log.setActionStatus(((Result)result).getSignal()?1:0);
            }
        }
        log.setActionInfo(action);
        log.setTarget(target);
        log.setActionTimestamp(new Date());
        log.setDetails(details);
        auditLogService.save(log);
    }
}
