package com.nti56.nlink.product.device.server.domain.thing.dpo;

import cn.hutool.core.collection.CollectionUtil;
import io.swagger.models.auth.In;
import com.nti56.nlink.product.device.client.model.dto.json.dpo.EventDpo;
import com.nti56.nlink.product.device.client.model.dto.json.dpo.SubscriptionDpo;
import lombok.Data;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-05-26 08:58:42
 * @since JDK 1.8
 */
@Data
public class ModelDpo {
    private Long id;
    private String name;
    private String descript;
    private List<PropertyDpo> properties;
    private List<EventDpo> events;
    private List<ServiceDpo> services;
    private List<SubscriptionDpo> subscriptions;
    private Integer modelType;

    public ModelDpo merge(ModelDpo modelDpo){
        if (CollectionUtil.isNotEmpty(modelDpo.properties)) {
            if (properties == null) {
                properties = new ArrayList<>();
            }
            properties.addAll(modelDpo.properties);
        }
        if (CollectionUtil.isNotEmpty(modelDpo.events)) {
            if (events == null) {
                events = new ArrayList<>();
            }
            events.addAll(modelDpo.events);
        }
        if (CollectionUtil.isNotEmpty(modelDpo.services)) {
            if (services == null) {
                services = new ArrayList<>();
            }
            services.addAll(modelDpo.services);
        }
        if (CollectionUtil.isNotEmpty(modelDpo.subscriptions)) {
            if (subscriptions == null) {
                subscriptions = new ArrayList<>();
            }
            subscriptions.addAll(modelDpo.subscriptions);
        }
        return this;
    }
}
