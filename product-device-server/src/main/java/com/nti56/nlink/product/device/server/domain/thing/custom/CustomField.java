package com.nti56.nlink.product.device.server.domain.thing.custom;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import cn.hutool.core.net.Ipv4Util;
import org.springframework.beans.BeanUtils;

import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.client.model.dto.json.CustomFieldDynamicLengthConfigField;
import com.nti56.nlink.product.device.client.model.dto.json.CustomFieldTransformField;
import com.nti56.nlink.product.device.client.model.dto.json.custom.ConfigDynamicLengthConditionElm;
import com.nti56.nlink.product.device.client.model.dto.json.custom.DynamicLengthConditionElm;
import com.nti56.nlink.product.device.client.model.dto.json.custom.MessageItemElm;
import com.nti56.nlink.product.device.client.model.dto.json.enumerate.ThingDataTypeEnum;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.CustomFieldPartTypeEnum;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.CustomFieldTargetTypeEnum;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.DriverCheckTypeEnum;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.DriverFieldTypeEnum;
import com.nti56.nlink.product.device.server.entity.CustomFieldEntity;
import com.nti56.nlink.product.device.server.model.custom.CustomFieldBo;
import com.nti56.nlink.product.device.server.util.RegexUtil;

import lombok.Getter;

/**
 * 类说明: 自定义协议字段领域对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-11-18 14:38:53
 * @since JDK 1.8
 */
public class CustomField {
    
    @Getter
    private Long id;

    @Getter
    private String fieldName;

    @Getter
    private CustomFieldTargetTypeEnum targetType;

    @Getter
    private Long targetId;

    @Getter
    private Integer sortNo;

    @Getter
    private Boolean isBody;

    @Getter
    private CustomFieldPartTypeEnum partType;

    @Getter
    private String fieldDescript;

    @Getter
    private Integer startByte;

    @Getter
    private Integer startBit;

    @Getter
    private Integer endByte;

    @Getter
    private Integer endBit;

    @Getter
    private ThingDataTypeEnum dataType;

    @Getter
    private Integer bytes;

    @Getter
    private DriverFieldTypeEnum fieldType; 

    @Getter
    private String constValue;

    @Getter
    private List<String> byteLengthFieldList;

    @Getter
    private List<String> countFieldList;

    @Getter
    private DriverCheckTypeEnum checkType;
    
    @Getter
    private List<String> checkFieldList;

    @Getter
    private List<String> repeatFieldList;

    @Getter
    private String ipValue;

    private CustomFieldDynamicLengthConfigField dynamicLengthConfig;

    @Getter
    private CustomFieldTransformField fieldTransform;

    private CustomField() {}

    private static Result<CustomField> checkBase(Integer sortNo, CustomFieldEntity entity){
        
        if(sortNo == null){
            return Result.error("字段排序号不能为空");
        }
        CustomField field = new CustomField();
        field.sortNo = sortNo;
        field.id = entity.getId();

        String fieldName = entity.getFieldName();
        if(fieldName == null || "".equals(fieldName)){
            return Result.error("字段名不能为空");
        }
        
        if(!RegexUtil.checkName(fieldName)){
            return Result.error("字段名只能是英文、字母、下划线，且英文开头");
        }
        if(fieldName.length() > 255){
            return Result.error("字段名长度不能超过255");
        }
        field.fieldName = fieldName;

        field.fieldDescript = entity.getFieldDescript();

        //开始位置
        Integer startByte = entity.getStartByte();
        if(startByte == null){
            return Result.error("字段开始位置不能为空：fieldName:" + fieldName);
        }
        field.startByte = startByte;

        //数据类型
        ThingDataTypeEnum dataType = ThingDataTypeEnum.typeOfValue(entity.getDataType());
        if(dataType == null){
            return Result.error("字段数据类型错误：dataType:" + entity.getDataType());
        }
        field.dataType = dataType;

        //字段类型
        DriverFieldTypeEnum fieldType = DriverFieldTypeEnum.typeOfValue(entity.getFieldType());
        if(fieldType == null){
            return Result.error("字段类型错误：fieldType:" + entity.getFieldType());
        }
        field.fieldType = fieldType;

        //开始位索引
        Integer startBit = entity.getStartBit();
        if(DriverFieldTypeEnum.DYNAMIC_POSITION.equals(fieldType)){
            if(startBit == null){
                field.startBit = 0;
            }else if(startBit < 0 || startBit > 7){
                return Result.error("字段开始位索引只能在[0-7]范围内：startBit:" + startBit);
            }else{
                field.startBit = startBit;
            }
        }else{
            if(ThingDataTypeEnum.BOOLEAN.equals(dataType)){
                if(startBit == null){
                    field.startBit = 0;
                }else if(startBit < 0 || startBit > 7){
                    return Result.error("字段开始位索引只能在[0-7]范围内：startBit:" + startBit);
                }else{
                    field.startBit = startBit;
                }
            }else{
                if(startBit == null || startBit == 0){
                    field.startBit = 0;
                }else{
                    return Result.error("字段数据类型" + dataType.getName() + "的开始位索引只能是0或null，startBit:" + startBit);
                }
            }
        }

        //动态变量
        if(DriverFieldTypeEnum.DYNAMIC.equals(fieldType)){
            if(!ThingDataTypeEnum.STRING.equals(dataType) 
                && !ThingDataTypeEnum.BYTE.equals(dataType)
            ){
                return Result.error("动态变量的数据类型只能是String或Byte：dataType:" + entity.getDataType());
            }
        }

        //动长变量
        if(DriverFieldTypeEnum.DYNAMIC_LENGTH.equals(fieldType)){

            CustomFieldDynamicLengthConfigField dynamicLengthConfig = entity.getDynamicLengthConfig();
            if(dynamicLengthConfig == null){
                return Result.error("动态长度需要配置");
            }
            List<ConfigDynamicLengthConditionElm> dynamicLengthConditions = dynamicLengthConfig.getDynamicLengthConditions();
            if(dynamicLengthConditions == null || dynamicLengthConditions.size() <= 0){
                return Result.error("动态长度需要配置");
            }
            for(ConfigDynamicLengthConditionElm elm:dynamicLengthConditions){
                if(elm.getConditionField() == null || "".equals(elm.getConditionField()) 
                    || elm.getConditionValue() == null || "".equals(elm.getConditionValue()) 
                    || elm.getResultDataType() == null
        ){
                    return Result.error("动态长度需要配置具体参数");
                }
            }

            field.dynamicLengthConfig = dynamicLengthConfig;
        }

        field.fieldTransform = entity.getFieldTransform();

        //结束位置
        field.endByte = entity.getEndByte();

        //结束位索引
        Integer endBit = entity.getEndBit();
        if(DriverFieldTypeEnum.DYNAMIC_POSITION.equals(fieldType)){
            if(endBit == null){
                field.endBit = 0;
            }else if(endBit < 0 || endBit > 7){
                return Result.error("字段结束位索引只能在[0-7]范围内：endBit:" + endBit);
            }else{
                field.endBit = endBit;
            }
        }
        
        //bytes
        if(!DriverFieldTypeEnum.DYNAMIC.equals(fieldType)){
            if(ThingDataTypeEnum.STRING.equals(dataType) 
                || ThingDataTypeEnum.BYTE.equals(dataType)
            ){
                Integer bytes = entity.getBytes();
                if(bytes == null){
                    return Result.error("字段字节长度不能为空");
                }
                field.bytes = bytes;
            }else if(ThingDataTypeEnum.BOOLEAN.equals(dataType) ){
                field.bytes = 1;
            }else{
                field.bytes = dataType.getBytes();
            }
        }else{
            if(ThingDataTypeEnum.BYTE.equals(dataType)
            ){
                //动态长度，byte默认是2，才能识别为byte数组
                field.bytes = 2;
            }
        }

        //检查类型
        if(DriverFieldTypeEnum.CHECK.equals(fieldType)){
            DriverCheckTypeEnum checkType = DriverCheckTypeEnum.typeOfValue(entity.getCheckType());
            field.checkType = checkType;
        }

        return Result.ok(field);
    }
    
    
    public static Result<CustomField> checkFixTailField(Integer sortNo, CustomFieldEntity entity){

        Result<CustomField> baseResult = checkBase(sortNo, entity);
        if(!baseResult.getSignal()){
            return Result.error(baseResult.getMessage());
        }
        CustomField field = baseResult.getResult();

        DriverFieldTypeEnum fieldType = field.getFieldType();

        String fieldValue = entity.getFieldValue();

        //常量
        if(fieldValue != null && !"".equals(fieldValue)){
            switch (fieldType) {
                case CONST: {
                    field.constValue = fieldValue;
                    break;
                }
                case BYTE_LENGTH: {
                    field.byteLengthFieldList = Arrays.asList(fieldValue.split(","));
                    break;
                }
                case COUNT: {
                    field.countFieldList = Arrays.asList(fieldValue.split(","));
                    break;
                }
                case IP: {
                    if(!RegexUtil.checkIpv4(fieldValue)){
                        return Result.error("ip格式错误，ip:" + fieldValue);
                    }
                    field.ipValue = fieldValue;
                    break;
                }
                case CHECK: {
                    field.checkFieldList = Arrays.asList(fieldValue.split(","));
                    break;
                }
                case REPEAT: {
                    field.repeatFieldList = Arrays.asList(fieldValue.split(","));
                    break;
                }
                default: {
                    break;
                }
            }
        }

        field.targetType = CustomFieldTargetTypeEnum.DRIVER_FIELD;
        field.isBody = false;
        field.partType = CustomFieldPartTypeEnum.TAIL;

        return Result.ok(field);
    }

    public static Result<CustomField> checkFixHeaderField(Integer sortNo, CustomFieldEntity entity){

        Result<CustomField> baseResult = checkBase(sortNo, entity);
        if(!baseResult.getSignal()){
            return Result.error(baseResult.getMessage());
        }
        CustomField field = baseResult.getResult();

        DriverFieldTypeEnum fieldType = field.getFieldType();

        String fieldValue = entity.getFieldValue();

        //常量
        if(fieldValue != null && !"".equals(fieldValue)){
            switch (fieldType) {
                case CONST: {
                    field.constValue = fieldValue;
                    break;
                }
                case BYTE_LENGTH: {
                    field.byteLengthFieldList = Arrays.asList(fieldValue.split(","));
                    break;
                }
                case COUNT: {
                    field.countFieldList = Arrays.asList(fieldValue.split(","));
                    break;
                }
                case IP: {
                    if(!RegexUtil.checkIpv4(fieldValue)){
                        return Result.error("ip格式错误，ip:" + fieldValue);
                    }
                    field.ipValue = fieldValue;
                    break;
                }
                case CHECK: {
                    field.checkFieldList = Arrays.asList(fieldValue.split(","));
                    break;
                }
                case REPEAT: {
                    field.repeatFieldList = Arrays.asList(fieldValue.split(","));
                    break;
                }
                default: {
                    break;
                }
            }
        }

        field.targetType = CustomFieldTargetTypeEnum.DRIVER_FIELD;
        field.isBody = false;
        field.partType = CustomFieldPartTypeEnum.HEADER;

        return Result.ok(field);
    }

    /**
     * 根据新头部字段定义，和旧协议包字段值，构建新协议包字段
     * @param messageTargetId
     * @param oldCustomField
     * @return
     */
    public Result<CustomField> buildMessageField(Long messageTargetId, CustomField oldCustomField) {
        
        //检查this必须是固定头字段才能导出协议包字段
        if(!CustomFieldTargetTypeEnum.DRIVER_FIELD.equals(targetType)){
            return Result.error("只有协议字段才能导出消息字段");
        }

        CustomField field = new CustomField();
        if(oldCustomField != null){
            field.id = oldCustomField.getId();
        }
        field.targetType = CustomFieldTargetTypeEnum.MESSAGE_FIELD;

        field.fieldName = fieldName;
        field.sortNo = sortNo;
        
        if(messageTargetId == null){
            return Result.error("协议包字段所属协议包id不能为空，fieldName:" + fieldName);
        }
        field.targetId = messageTargetId;

        field.fieldDescript = fieldDescript;
        if(oldCustomField != null && oldCustomField.getStartByte() != null){
            field.startByte = oldCustomField.getStartByte();
        }else{
            field.startByte = startByte;
        }
        if(oldCustomField != null && oldCustomField.getStartBit() != null){
            field.startBit = oldCustomField.getStartBit();
        }else{
            field.startByte = startByte;
        }
        if(oldCustomField != null && oldCustomField.getDataType() != null){
            field.dataType = oldCustomField.getDataType();
        }else{
            field.dataType = dataType;
        }
        if(oldCustomField != null && oldCustomField.getBytes() != null){
            field.bytes = oldCustomField.getBytes();
        }else{
            field.bytes = bytes;
        }
        if(oldCustomField != null && oldCustomField.getEndByte() != null){
            field.endByte = oldCustomField.getEndByte();
        }else{
            field.endByte = endByte;
        }
        if(oldCustomField != null && oldCustomField.getEndBit() != null){
            field.endBit = oldCustomField.getEndBit();
        }else{
            field.endBit = endBit;
        }
        if(oldCustomField != null && oldCustomField.getFieldTransform() != null){
            field.fieldTransform = oldCustomField.getFieldTransform();
        }else{
            field.fieldTransform = fieldTransform;
        }
        
        
        field.fieldType = fieldType;
        field.isBody = false;
        field.partType = partType;

        switch (fieldType) {
            case CONST: {
                if((constValue == null || "".equals(constValue)) 
                    && (oldCustomField != null && oldCustomField.getConstValue() != null)
                ){
                    field.constValue = oldCustomField.getConstValue();
                }else{
                    field.constValue = constValue;
                }
                break;
            }
            case BYTE_LENGTH: {
                if((byteLengthFieldList == null || byteLengthFieldList.size() <= 0)
                    && (oldCustomField != null && oldCustomField.getByteLengthFieldList() != null && oldCustomField.getByteLengthFieldList().size() > 0)
                ){
                    field.byteLengthFieldList = oldCustomField.getByteLengthFieldList();
                }else{
                    field.byteLengthFieldList = byteLengthFieldList;
                }
                break;
            }
            case COUNT: {
                if((countFieldList == null || countFieldList.size() <= 0)
                    && (oldCustomField != null && oldCustomField.getCountFieldList() != null && oldCustomField.getCountFieldList().size() > 0)
                ){
                    field.countFieldList = oldCustomField.getCountFieldList();
                }else{
                    field.countFieldList = countFieldList;
                }
                break;
            }
            case CHECK: {
                if((checkFieldList == null || checkFieldList.size() <= 0)
                    && (oldCustomField != null && oldCustomField.getCheckFieldList() != null && oldCustomField.getCheckFieldList().size() > 0)
                ){
                    field.checkFieldList = oldCustomField.getCheckFieldList();
                }else{
                    field.checkFieldList = checkFieldList;
                }
                if(checkType == null && oldCustomField != null && oldCustomField.getCheckType() != null){
                    field.checkType = oldCustomField.getCheckType();
                }else{
                    field.checkType = checkType;
                }
                break;
            }
            case REPEAT: {
                if((repeatFieldList == null || repeatFieldList.size() <= 0)
                    && (oldCustomField != null && oldCustomField.getRepeatFieldList() != null && oldCustomField.getRepeatFieldList().size() > 0)
                ){
                    field.repeatFieldList = oldCustomField.getRepeatFieldList();
                }else{
                    field.repeatFieldList = repeatFieldList;
                }
                break;
            }
            case VARIABLE: {

                break;
            }
            case DYNAMIC_LENGTH: {
                field.dynamicLengthConfig = dynamicLengthConfig;
                break;
            }
            default: {

                break;
            }
        }

        return Result.ok(field);
    }

    /**
     * 固定头字段复制并合协议包字段，然后导出
     * @param messageFieldEntity
     * @return
     */
    public Result<CustomField> buildMessageField(CustomFieldEntity messageFieldEntity) {

        //检查this必须是固定头字段才能导出协议包字段
        if(!CustomFieldTargetTypeEnum.DRIVER_FIELD.equals(targetType)){
            return Result.error("只有协议字段才能导出消息字段");
        }

        //检查名称一致
        String messageFieldName = messageFieldEntity.getFieldName();
        if(!fieldName.equals(messageFieldName)){
            return Result.error("协议包字段名称和固定头字段名称不一致，fieldName:" + fieldName + ", messageFieldName:" + messageFieldName);
        }

        //检查排序号一致
        Integer messageSortNo = messageFieldEntity.getSortNo();

        CustomField field = new CustomField();
        field.id = messageFieldEntity.getId();
        field.targetType = CustomFieldTargetTypeEnum.MESSAGE_FIELD;
        field.fieldName = fieldName;
        field.sortNo = messageSortNo;
        
        // Long messageTargetId = messageFieldEntity.getTargetId();
        // if(messageTargetId == null){
        //     return Result.error("协议包字段所属协议包id不能为空，fieldName:" + fieldName);
        // }
        // field.targetId = messageTargetId;
        field.targetId = messageFieldEntity.getTargetId();

        field.fieldDescript = fieldDescript;

        Integer messageStartByte = messageFieldEntity.getStartByte();
        if(messageStartByte == null){
            field.startByte = startByte;
        }else{
            field.startByte = messageStartByte;
        }
        
        Integer messageStartBit = messageFieldEntity.getStartBit();
        if(messageStartBit == null){
            field.startBit = startBit;
        }else{
            field.startBit = messageStartBit;
        }
        
        ThingDataTypeEnum messageDataType = ThingDataTypeEnum.typeOfValue(messageFieldEntity.getDataType());
        if(messageDataType == null){
            field.dataType = dataType;
        }else{
            field.dataType = messageDataType;
        }

        Integer messageBytes = messageFieldEntity.getBytes();
        if(messageBytes == null){
            field.bytes = bytes;
        }else{
            field.bytes = messageBytes;
        }
        
        field.fieldType = fieldType;
        field.isBody = false;
        field.partType = partType;

        switch (fieldType) {
            case CONST: {
                String messageConstValue = messageFieldEntity.getFieldValue();
                if(messageConstValue == null || "".equals(messageConstValue)){
                    field.constValue = constValue;
                }else{
                    field.constValue = messageConstValue;
                }
                break;
            }
            case BYTE_LENGTH: {
                String messageByteLengthFields = messageFieldEntity.getFieldValue();
                if(messageByteLengthFields == null || "".equals(messageByteLengthFields)){
                    field.byteLengthFieldList = byteLengthFieldList;
                }else{
                    field.byteLengthFieldList = Arrays.asList(messageByteLengthFields.split(","));
                }
                break;
            }
            case COUNT: {
                String messageCountFields = messageFieldEntity.getFieldValue();
                if(messageCountFields == null || "".equals(messageCountFields)){
                    field.countFieldList = countFieldList;
                }else{
                    field.countFieldList = Arrays.asList(messageCountFields.split(","));
                }
                break;
            }
            case IP: {
                String messageIpValue = messageFieldEntity.getFieldValue();
                if(messageIpValue == null || "".equals(messageIpValue)){
                    field.ipValue = ipValue;
                }else{
                    field.ipValue = messageIpValue;
                }
                break;
            }
            case CHECK: {
                DriverCheckTypeEnum messageCheckType = DriverCheckTypeEnum.typeOfValue(messageFieldEntity.getCheckType());
                if(messageCheckType == null){
                    field.checkType = checkType;
                }else{
                    field.checkType = messageCheckType;
                }
                String messageCheckFields = messageFieldEntity.getFieldValue();
                if(messageCheckFields == null || "".equals(messageCheckFields)){
                    field.checkFieldList = checkFieldList;
                }else{
                    field.checkFieldList = Arrays.asList(messageCheckFields.split(","));
                }
                break;
            }
            case REPEAT: {
                String messageRepeatFields = messageFieldEntity.getFieldValue();
                if(messageRepeatFields == null || "".equals(messageRepeatFields)){
                    field.repeatFieldList = repeatFieldList;
                }else{
                    field.repeatFieldList = Arrays.asList(messageRepeatFields.split(","));
                }
                break;
            }
            case DYNAMIC_LENGTH: {
                CustomFieldDynamicLengthConfigField  messageDynamicLengthConfig = messageFieldEntity.getDynamicLengthConfig();
                if(messageDynamicLengthConfig == null){
                    field.dynamicLengthConfig = dynamicLengthConfig;
                }else{
                    field.dynamicLengthConfig = messageDynamicLengthConfig;
                }
                break;
            }
            default: {

                break;
            }
        }

        return Result.ok(field);
    }

    /**
     * 构建消息体字段
     */
    public static Result<CustomField> checkMessageFieldInfo(CustomFieldEntity entity) {

        Integer sortNo = entity.getSortNo();

        Result<CustomField> baseResult = checkBase(sortNo, entity);
        if(!baseResult.getSignal()){
            return Result.error(baseResult.getMessage());
        }
        CustomField field = baseResult.getResult();

        field.isBody = true;
        field.targetType = CustomFieldTargetTypeEnum.MESSAGE_FIELD;
        field.targetId = entity.getTargetId();
        field.partType = CustomFieldPartTypeEnum.BODY;

        DriverFieldTypeEnum fieldType = field.getFieldType();
        String fieldName = field.getFieldName();
        
        switch (fieldType) {
            case CONST: {
                String constValue = entity.getFieldValue();
                if(constValue == null || "".equals(constValue)){
                    return Result.error("常量字段，constValue不能为空，fieldName:" + fieldName);
                }else{
                    field.constValue = constValue;
                }
                break;
            }
            case BYTE_LENGTH: {
                String byteLengthFields = entity.getFieldValue();
                if(byteLengthFields == null || "".equals(byteLengthFields)){
                    return Result.error("字节长度字段，byteLengthFields不能为空，fieldName:" + fieldName);
                }else{
                    field.byteLengthFieldList = Arrays.asList(byteLengthFields.split(","));
                }
                break;
            }
            case COUNT: {
                String countFields = entity.getFieldValue();
                if(countFields == null || "".equals(countFields)){
                    return Result.error("数量字段，countFields不能为空，fieldName:" + fieldName);
                }else{
                    field.countFieldList = Arrays.asList(countFields.split(","));
                }
                break;
            }
            case VARIABLE: {

                break;
            }
            case CHECK: {
                String checkFields = entity.getFieldValue();
                if(checkFields == null || "".equals(checkFields)){
                    return Result.error("校验字段，checkFields不能为空，fieldName:" + fieldName);
                }else{
                    field.checkFieldList = Arrays.asList(checkFields.split(","));
                }
                break;
            }
            case REPEAT: {
                String repeatFields = entity.getFieldValue();
                if(repeatFields == null || "".equals(repeatFields)){
                    return Result.error("重复数组字段，repeatFields不能为空，fieldName:" + fieldName);
                }else{
                    field.repeatFieldList = Arrays.asList(repeatFields.split(","));
                }
                break;
            }
            case IP: {
                String ipValue = entity.getFieldValue();
                if(ipValue == null || "".equals(ipValue)){
                    return Result.error("IP字段，ipValue不能为空，fieldName:" + fieldName);
                }else{
                    field.ipValue = ipValue;
                }
                break;
            }
            case DYNAMIC_LENGTH: {
                field.dynamicLengthConfig = entity.getDynamicLengthConfig();
                break;
            }
            default: {

                break;
            }
        }

        return Result.ok(field);
    }

    public CustomFieldBo toFullBo() {
        CustomFieldBo bo = new CustomFieldBo();
        BeanUtils.copyProperties(toEntity(), bo);
        bo.setIsBody(isBody);
        return bo;
    }
    public CustomFieldEntity toEntity() {
        return toEntity(true);
    }
    public CustomFieldEntity toEntity(Boolean withId) {
        CustomFieldEntity entity = new CustomFieldEntity();
        if(withId != null && withId){
            entity.setId(id);
        }
        entity.setSortNo(sortNo);
        entity.setTargetType(targetType.getValue());
        entity.setTargetId(targetId);
        entity.setPartType(partType.getValue());
        entity.setFieldName(fieldName);
        entity.setFieldDescript(fieldDescript);
        entity.setStartByte(startByte);
        entity.setStartBit(startBit);
        entity.setEndByte(endByte);
        entity.setEndBit(endBit);
        entity.setDataType(dataType.getValue());
        entity.setBytes(bytes);
        entity.setFieldType(fieldType.getValue());
        entity.setDynamicLengthConfig(dynamicLengthConfig);
        entity.setFieldTransform(fieldTransform);

        switch (fieldType) {
            case CONST: {
                entity.setFieldValue(constValue);
                break;
            }
            case BYTE_LENGTH: {
                if(byteLengthFieldList != null && byteLengthFieldList.size() > 0){
                    entity.setFieldValue(byteLengthFieldList.stream()
                        .collect(Collectors.joining(","))
                    );
                }
                break;
            }
            case COUNT: {
                if(countFieldList != null && countFieldList.size() > 0){
                    entity.setFieldValue(countFieldList.stream()
                        .collect(Collectors.joining(","))
                    );
                }
                break;
            }
            case IP: {
                entity.setFieldValue(ipValue);
                break;
            }
            case CHECK: {
                if(checkType != null){
                    entity.setCheckType(checkType.getValue());
                }
                if(checkFieldList != null && checkFieldList.size() > 0){
                    entity.setFieldValue(checkFieldList.stream()
                            .collect(Collectors.joining(","))
                    );
                }
                break;
            }
            case REPEAT: {
                if(repeatFieldList != null && repeatFieldList.size() > 0){
                    entity.setFieldValue(repeatFieldList.stream()
                            .collect(Collectors.joining(","))
                    );
                }
                break;
            }
            default:
                break;
        }
        
        return entity;
    }

    public void updateSortNo(Integer sortNo) {
        this.sortNo = sortNo;
    }

    public MessageItemElm toRuntimeInfo() {
        MessageItemElm define = new MessageItemElm();
        define.setFieldName(fieldName);
        define.setFieldDescript(fieldDescript);
        define.setStartByte(startByte);
        define.setStartBit(startBit);
        define.setEndByte(endByte);
        define.setEndBit(endBit);
        define.setDataType(dataType.getName());
        define.setBytes(bytes);
        define.setFieldType(fieldType.getName());
        if(checkType != null){
            define.setCheckType(checkType.getName());
        }
        define.setConstValue(constValue);
        define.setByteLengthFields(byteLengthFieldList);
        define.setCountFields(countFieldList);
        define.setIpValue(ipValue);
        define.setCheckFields(checkFieldList);
        define.setRepeatFields(repeatFieldList);
        define.setFieldTransform(fieldTransform);
        if(dynamicLengthConfig != null && dynamicLengthConfig.getDynamicLengthConditions().size() > 0){
            List<DynamicLengthConditionElm> collect = dynamicLengthConfig.getDynamicLengthConditions().stream().map(t -> {
                return new DynamicLengthConditionElm(
                    t.getConditionField(),
                    t.getConditionValue(),
                    ThingDataTypeEnum.typeOfValue(t.getResultDataType()).getName(),
                    t.getResultBytes()
                );
            }).collect(Collectors.toList());
            define.setDynamicLengthConditions(collect);
        }
        return define;
    }


}
