package com.nti56.nlink.product.device.server.domain.thing.channel;

import cn.hutool.core.util.ObjectUtil;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.client.model.dto.json.ChannelRuntimeInfoField;
import com.nti56.nlink.product.device.client.model.dto.json.device.ChannelElm;
import com.nti56.nlink.product.device.server.util.RegexUtil;
import lombok.Getter;

import java.util.Map;
import java.util.HashMap;
import java.util.List;
import com.nti56.nlink.product.device.server.entity.ChannelParamEntity;


/**
 * 类说明: opcua驱动通道参数领域对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-24 14:07:25
 * @since JDK 1.8
 */
@Getter
public class OpcUaChannelParam extends ChannelParam{
    private String ip;
    private Integer port;
    private String userName;
    private String password;

    public static final String[] requiredParam = new String[]{
        "ip::true", 
        "port::true",
        "userName::false",
        "password::false",
        "reconnectGapMs:3000:true::断线重连间隔（毫秒）",
        "maxConnection:1:true::通道最多连接数",
        "delayIdleMs:0:true::延迟空闲时间（毫秒）"
    };

    
    public static Result<ChannelParam> checkParam(List<ChannelParamEntity> channelParamList){

        OpcUaChannelParam param = new OpcUaChannelParam();

        Result<Map<String, String>> baseResult = ChannelParam.checkBase(
            param, requiredParam, channelParamList
        );
        if(!baseResult.getSignal()){
            return Result.error(baseResult.getMessage());
        }
        Map<String, String> paramMap = baseResult.getResult();


        //ip
        String ipStr = paramMap.get("ip");
        if(ipStr == null){
            return Result.error("通道参数缺少ip");
        }
        if(!RegexUtil.checkIpv4(ipStr)){
            return Result.error("通道ip格式错误，ip:" + ipStr);
        }
        param.ip = ipStr;

        //port
        String portStr = paramMap.get("port");
        if(portStr == null){
            return Result.error("通道参数缺少port");
        }
        if(!RegexUtil.checkPort(portStr)){
            return Result.error("通道port格式错误，port:" + portStr);
        }
        param.port = Integer.parseInt(portStr);

        if(ObjectUtil.isNotNull(paramMap.get("userName"))){
            param.userName = paramMap.get("userName");
        }
        if(ObjectUtil.isNotNull(paramMap.get("password"))){
            param.password = paramMap.get("password");
        }
        
        //channelKey
        param.channelKey = param.ip + param.port;

        return Result.ok(param);

    }

    @Override
    public void processRuntimeInfo(ChannelRuntimeInfoField info) {
        processBaseRuntimeInfo(info);
       
        info.setIp(ip);
        info.setPort(port);
        info.setUserName(userName);
        info.setPassword(password);
    }


    @Override
    public void processChannelElm(ChannelElm channelElm) {
        processBaseChannelElm(channelElm);
      
        channelElm.setIp(ip);
        channelElm.setPort(port);
        channelElm.setUserName(userName);
        channelElm.setPassword(password);
    }
    
}
