package com.nti56.nlink.product.device.server.domain.thing.channel;

import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.client.model.dto.json.ChannelRuntimeInfoField;
import com.nti56.nlink.product.device.client.model.dto.json.device.ChannelElm;
import com.nti56.nlink.product.device.server.util.RegexUtil;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.HashMap;
import java.util.List;
import com.nti56.nlink.product.device.server.entity.ChannelParamEntity;


/**
 * 类说明: AB plc/EtherIp驱动通道参数领域对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-08-10 14:29:25
 * @since JDK 1.8
 */
@Getter
public class BacnetIpChannelParam extends ChannelParam{
    private String ip;
    private Integer port;

    private Integer deviceId;

    public static final String[] requiredParam = new String[]{
        "port::true",
        "deviceId::true",
        "reconnectGapMs:3000:true::断线重连间隔（毫秒）",
        "maxConnection:1:true::通道最多连接数",
        "delayIdleMs:0:true::延迟空闲时间（毫秒）"
    };

    
    public static Result<ChannelParam> checkParam(List<ChannelParamEntity> channelParamList){
        
        BacnetIpChannelParam param = new BacnetIpChannelParam();

        Result<Map<String, String>> baseResult = ChannelParam.checkBase(
            param, requiredParam, channelParamList
        );
        if(!baseResult.getSignal()){
            return Result.error(baseResult.getMessage());
        }
        Map<String, String> paramMap = baseResult.getResult();

        //port
        String portStr = paramMap.get("port");
        if(portStr == null){
            return Result.error("通道参数缺少port");
        }
        if(!RegexUtil.checkPort(portStr)){
            return Result.error("通道port格式错误，port:" + portStr);
        }
        param.port = Integer.parseInt(portStr);

        String deviceIdStr = paramMap.get("deviceId");
        if (StringUtils.isBlank(deviceIdStr)){
            return Result.error("通道参数缺少deviceId");
        }
        try{
            param.deviceId = Integer.parseInt(deviceIdStr);
            if(param.deviceId <0 || param.deviceId > 4194303){
                return Result.error("通道deviceId【0-4194303】格式错误，deviceId:" + deviceIdStr);
            }

        }catch (NumberFormatException e) {
            return Result.error("通道deviceId格式错误，deviceId:" + deviceIdStr);
        }

        param.channelKey = deviceIdStr;

        return Result.ok(param);
    }

    @Override
    public void processRuntimeInfo(ChannelRuntimeInfoField info) {
        processBaseRuntimeInfo(info);

        info.setIp(ip);
        info.setPort(port);
        info.setDeviceId(deviceId);
    }

    @Override
    public void processChannelElm(ChannelElm channelElm) {
        processBaseChannelElm(channelElm);
        
        channelElm.setIp(ip);
        channelElm.setPort(port);
        channelElm.setDeviceId(deviceId);
    }

    
}
