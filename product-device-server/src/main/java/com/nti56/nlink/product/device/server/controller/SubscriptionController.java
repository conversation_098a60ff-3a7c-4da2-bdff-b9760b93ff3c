package com.nti56.nlink.product.device.server.controller;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.*;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.ThingServiceCallTypeEnum;
import com.nti56.nlink.product.device.server.entity.DeviceServiceLogEntity;
import com.nti56.nlink.product.device.server.entity.SubscriptionEntity;
import com.nti56.nlink.product.device.server.service.ISubscriptionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.function.Function;


/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2022-09-22 15:17:38
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/")
@Tag(name = "订阅模块")
public class SubscriptionController {

    @Autowired
    private ISubscriptionService service;

    @GetMapping("subscription/page")
    @Operation(summary = "获取分页")
    public R page(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                  PageParam pageParam,SubscriptionEntity entity){
        Page<SubscriptionEntity> page = pageParam.toPage(SubscriptionEntity.class);
        entity.setTenantId(tenantIsolation.getTenantId());
        Result<Page<SubscriptionEntity>> result = service.getPage(entity,page);
        return R.result(result);
    }
    

    @GetMapping("subscription/list")
    @Operation(summary = "获取列表" )
    public R list(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                  SubscriptionEntity entity){
        entity.setTenantId(tenantIsolation.getTenantId());
        Result<List<SubscriptionEntity>> result = service.list(entity);
        return R.result(result);
    }

    @PostMapping("subscription")
    @Operation(summary = "创建对象")
    public R create(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                    @Parameter(description = "对象") @RequestBody SubscriptionEntity entity){
        if (BeanUtilsIntensifier.checkBeanAndProperties(entity, SubscriptionEntity::getName, SubscriptionEntity::getProperties,
                SubscriptionEntity::getSendOneByOne, SubscriptionEntity::getOutType,
                SubscriptionEntity::getEventType, SubscriptionEntity::getDirectlyModelId, SubscriptionEntity::getModelType)) {
            return R.error(ServiceCodeEnum.CODE_PARAM_ERROR);
        }
        if(!service.checkSubscription(entity)){
            return R.error(ServiceCodeEnum.CODE_PARAM_ERROR);
        }
        Result<SubscriptionEntity> result = service.save(tenantIsolation, entity);
        return R.result(result);
    }

    @PutMapping("subscription")
    @Operation(summary = "更新")
    public R update(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                    @Parameter(description = "对象") @RequestBody SubscriptionEntity entity){
        if (BeanUtilsIntensifier.checkBeanAndProperties(entity, SubscriptionEntity::getId, SubscriptionEntity::getSendOneByOne,
                SubscriptionEntity::getOutType, SubscriptionEntity::getEnable)) {
            return R.error(ServiceCodeEnum.CODE_PARAM_ERROR);
        }
        if(!service.checkSubscription(entity)){
            return R.error(ServiceCodeEnum.CODE_PARAM_ERROR);
        }
        Result result = service.update(tenantIsolation, entity);
        if(result.getSignal()){
            service.deleteNotifyIfNeed(entity);
        }
        return R.result(result);
    }

    @DeleteMapping("subscription/{entityId}")
    @Operation(summary = "删除对象")
    public R delete(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                    @Parameter(description = "目标ID") @PathVariable Long entityId){
        Result<Void> result = service.deleteById(tenantIsolation, entityId);
        if(result.getSignal()) {
            service.deleteNotify(entityId);
        }
        return R.result(result);
    }

    @DeleteMapping("subscription")
    @Operation(summary = "批量删除订阅")
    public R deleteBatchByIds(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                    @Parameter(description = "目标ID") @RequestBody List<Long> ids){
        Result<Void> result = service.deleteBatchByIds(tenantIsolation, ids);
        if(result.getSignal()){
            ids.forEach(id->{
                service.deleteNotify(id);
            });
        }
        return R.result(result);
    }

    @GetMapping("subscription/{entityId}")
    @Operation(summary = "获取对象")
    public R get(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                 @Parameter(description = "目标ID") @PathVariable Long entityId){
        Result<SubscriptionEntity> result = service.getByIdAndTenantIsolation(entityId, tenantIsolation);
        return R.result(result);
    }

    private R checkParamAndDoSomething(SubscriptionEntity entity, Function<SubscriptionEntity,Result> func){
        if (BeanUtilsIntensifier.checkBeanAndProperties(entity, SubscriptionEntity::getName, SubscriptionEntity::getProperties,
            SubscriptionEntity::getEventType, SubscriptionEntity::getCallbackId, SubscriptionEntity::getDirectlyModelId, SubscriptionEntity::getModelType)) {
            return R.error(ServiceCodeEnum.CODE_PARAM_ERROR);
        }
        Result result = func.apply(entity);
        return R.result(result);
    }
    
    @GetMapping("subscription/edgeGateway/page")
    @Operation(summary = "获取分页")
    public R subscriptionGatewayPage(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,PageParam pageParam,Long edgeGatewayId,SubscriptionEntity entity){
        Page<SubscriptionEntity> page = pageParam.toPage(SubscriptionEntity.class);
        Result<Page<SubscriptionEntity>> result = service.subscriptionGatewayPage(edgeGatewayId,entity,page,tenantIsolation);
        return R.result(result);
    }

}
