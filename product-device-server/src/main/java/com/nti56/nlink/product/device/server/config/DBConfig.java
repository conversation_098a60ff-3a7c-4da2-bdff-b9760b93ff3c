package com.nti56.nlink.product.device.server.config;

import org.apache.ibatis.mapping.DatabaseIdProvider;
import org.apache.ibatis.mapping.VendorDatabaseIdProvider;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceUtils;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.SQLException;
import java.util.Properties;

@Configuration
public class DBConfig {

    @Bean
    public DatabaseIdProvider databaseIdProvider() {
        return new DatabaseIdProvider() {
            @Override
            public void setProperties(Properties p) {
                // 在这里可以设置一些属性，如果没有，可以留空
                // 例如: properties.setProperty("someProperty", "value");
                p.setProperty("DM DBMS", "dm");
                p.setProperty("MySQL", "mysql");
                p.setProperty("Oracle", "oracle");
                p.setProperty("Microsoft SQL Server", "sqlserver");
            }

            @Override
            public String getDatabaseId(DataSource dataSource) throws SQLException {
                try (Connection connection = DataSourceUtils.getConnection(dataSource)) {
                    DatabaseMetaData metaData = connection.getMetaData();
                    String url = metaData.getURL();
                    if (url.contains("mysql")) {
                        return "mysql";
                    } else if (url.contains("dm")) {
                        return "dm";
                    } else if (url.contains("oracle")) {
                        return "oracle";
                    } else if (url.contains("sqlserver")) {
                        return "sqlserver";
                    } else {
                        return "unknown";
                    }
                }
            }
        };
    }
}