package com.nti56.nlink.product.device.server.domain.thing.enumerate;

import lombok.Getter;


/**
 * 类说明: 物服务调用类型枚举 1-用户调用，2-场景联动调用，3-接口调用，4-服务调用 5-fault事件调用
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-24 14:07:13
 * @since JDK 1.8
 */
public enum ThingServiceCallTypeEnum {
    USER_INVOKE(1, "user", "用户调用"),
    SCENE_INVOKE(2, "scene", "场景联动调用"),
    INTERFACE_INVOKE(3, "interface", "接口调用"),
    SERVICE_INVOKE(4, "service", "服务调用"),
    FAULT_INVOKE(5, "fault", "fault事件调用"),
    SUBSCRIPTION_INVOKE(6, "subscription", "订阅调用")
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    ThingServiceCallTypeEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static ThingServiceCallTypeEnum typeOfValue(Integer value){
        ThingServiceCallTypeEnum[] values = ThingServiceCallTypeEnum.values();
        for (ThingServiceCallTypeEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static ThingServiceCallTypeEnum typeOfName(String name){
        ThingServiceCallTypeEnum[] values = ThingServiceCallTypeEnum.values();
        for (ThingServiceCallTypeEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    @Override
    public String toString(){
        return this.name;
    }
}
