package com.nti56.nlink.product.device.server.config;

import com.nti56.nlink.product.device.server.listener.NoChangeKeyExpireListener;
import io.vertx.core.Vertx;
import io.vertx.redis.client.Redis;
import io.vertx.redis.client.RedisOptions;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName RedisConfig
 * @date 2022/7/25 15:47
 * @Version 1.0
 */
@Slf4j
@Configuration
public class VertxRedisConfig {

    @Autowired
    private Vertx vertx;

    @Value("${spring.redis.database}")
    private Integer database;
    @Value("${spring.redis.host}")
    private String host;

    @Value("${spring.redis.port}")
    private Integer port;

    @Value("${spring.redis.password}")
    private String  password;


    @Bean
    public Redis vertxRedisClient(Vertx vertx) {
        try {
            if (StringUtils.isNotEmpty(password)) {
                password = URLEncoder.encode(password, "UTF-8");
            }

        } catch (UnsupportedEncodingException e) {
           log.error(e.getMessage());
        }
        String url = String.format("redis://:%s@%s:%d/%d", password, host, port, database);
        RedisOptions options = new RedisOptions()
                .setConnectionString(url)
                .setMaxPoolSize(200) // 每个连接池的最大连接数
                .setMaxPoolWaiting(50000) // 每个连接池最大等待连接数
                .setPoolRecycleTimeout(60000) // 连接回收超时时间（毫秒）
                .setPoolCleanerInterval(10000); // 连接池清理间隔（毫秒）

        return Redis.createClient(vertx, options);
    }

}
