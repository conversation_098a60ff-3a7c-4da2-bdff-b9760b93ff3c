package com.nti56.nlink.product.device.server.domain.thing.enumerate;

import lombok.Getter;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-05-26 09:00:34
 * @since JDK 1.8
 */
public enum TriggerConditionTypeEnum {
    BRACKET(1, "bracket", "括号"),
    COMPARE(2, "compare", "对比"),
    SINGLE(3, "single", "单个")
    ;
    
    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    TriggerConditionTypeEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static TriggerConditionTypeEnum typeOfValue(Integer value){
        TriggerConditionTypeEnum[] values = TriggerConditionTypeEnum.values();
        for (TriggerConditionTypeEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static TriggerConditionTypeEnum typeOfName(String name){
        TriggerConditionTypeEnum[] values = TriggerConditionTypeEnum.values();
        for (TriggerConditionTypeEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static TriggerConditionTypeEnum typeOfNameDesc(String nameDesc){
        TriggerConditionTypeEnum[] values = TriggerConditionTypeEnum.values();
        for (TriggerConditionTypeEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
