package com.nti56.nlink.product.device.server.domain.thing.channel;

import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.client.model.dto.json.ChannelRuntimeInfoField;
import com.nti56.nlink.product.device.client.model.dto.json.device.ChannelElm;
import com.nti56.nlink.product.device.server.util.RegexUtil;
import lombok.Getter;

import java.util.Map;
import java.util.HashMap;
import java.util.List;
import com.nti56.nlink.product.device.server.entity.ChannelParamEntity;


/**
 * 类说明: modbusRtu驱动通道参数领域对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-9-22 15:27:25
 * @since JDK 1.8
 */
@Getter
public class ModbusRtuChannelParam extends ChannelParam{
    private Integer comId;
    private Integer baudRate;
    private Integer dataBits;
    private Integer stopBits;
    private Integer parity;
    private Integer slaveId;

    private String endianness;
    private String firstAddress;

    public static final String[] requiredParam = new String[]{
            "comId::true",
            "baudRate::true",
            "dataBits::true",
            "stopBits::true",
            "parity::true",
            "slaveId::true",
            "endianness:ABCD:true:ABCD,BADC,CDAB,DCBA:大小端配置",
            "firstAddress:是:true:是,否:首地址是否从0开始",
            "reconnectGapMs:3000:true::断线重连间隔（毫秒）",
            "maxConnection:1:true::通道最多连接数",
            "delayIdleMs:0:true::延迟空闲时间（毫秒）"
    };

    
    public static Result<ChannelParam> checkParam(List<ChannelParamEntity> channelParamList){

        ModbusRtuChannelParam param = new ModbusRtuChannelParam();

        Result<Map<String, String>> baseResult = ChannelParam.checkBase(
            param, requiredParam, channelParamList
        );
        if(!baseResult.getSignal()){
            return Result.error(baseResult.getMessage());
        }
        Map<String, String> paramMap = baseResult.getResult();

        //comId
        String comIdStr = paramMap.get("comId");
        if(comIdStr == null){
            return Result.error("通道参数缺少COM口");
        }
        if(!RegexUtil.checkInt(comIdStr)){
            return Result.error("通道Com口格式错误");
        }
        param.comId =  Integer.parseInt(comIdStr);

        //channelKey
        param.channelKey = comIdStr;

        //baudRate
        String baudRateStr = paramMap.get("baudRate");
        if(baudRateStr == null){
            return Result.error("通道参数缺少baudRate");
        }
        if(!RegexUtil.checkBaudRate(baudRateStr)){
            return Result.error("通道baudRate格式错误，baudRate 值应为(300|600|1200|2400|4800|9600|14400|19200|28800|38400|56000|57600|115200|128000|256000)");
        }
        param.baudRate = Integer.parseInt(baudRateStr);

        //dataBits
        String dataBitsStr = paramMap.get("dataBits");
        if(dataBitsStr == null){
            return Result.error("通道参数缺少dataBits");
        }
        if(!RegexUtil.checkDataBits(dataBitsStr)){
            return Result.error("通道dataBits格式错误，dataBits 值应为5-8");
        }
        param.dataBits = Integer.parseInt(dataBitsStr);

        //stopBits
        String stopBitsStr = paramMap.get("stopBits");
        if(stopBitsStr == null){
            return Result.error("通道参数缺少stopBits");
        }
        if(!RegexUtil.checkStopBits(stopBitsStr)){
            return Result.error("通道stopBits格式错误，stopBits 值应为（1，2）");
        }
        param.stopBits = Integer.parseInt(stopBitsStr);

        //parity
        String parityStr = paramMap.get("parity");
        if(parityStr == null){
            return Result.error("通道参数缺少parity");
        }
        if(!RegexUtil.checkParity(parityStr)){
            return Result.error("通道parity格式错误，parity 值应为0(无),1(奇),2(偶)）");
        }
        param.parity = Integer.parseInt(parityStr);

        //slaveId
        String slaveIdStr = paramMap.get("slaveId");
        if(slaveIdStr == null){
            return Result.error("通道参数缺少slaveId");
        }
        if(!RegexUtil.checkInt(slaveIdStr)){
            return Result.error("通道slaveId格式错误");
        }
        param.slaveId = Integer.parseInt(slaveIdStr);

        String endiannessStr = paramMap.get("endianness");
        if(endiannessStr == null){
            return Result.error("通道参数缺少endianness");
        }
        param.endianness = endiannessStr;

        String firstAddressStr = paramMap.get("firstAddress");
        if(firstAddressStr == null){
            return Result.error("通道参数缺少firstAddress");
        }
        param.firstAddress = firstAddressStr;

        return Result.ok(param);

    }

    @Override
    public void processRuntimeInfo(ChannelRuntimeInfoField info) {
        processBaseRuntimeInfo(info);
       
        info.setComId(comId);
        info.setBaudRate(baudRate);
        info.setDataBits(dataBits);
        info.setStopBits(stopBits);
        info.setParity(parity);
        info.setSlaveId(slaveId);
        info.setEndianness(endianness);
        info.setFirstAddress(firstAddress);
    }


    @Override
    public void processChannelElm(ChannelElm channelElm) {
        processBaseChannelElm(channelElm);
       
        channelElm.setComId(comId);
        channelElm.setBaudRate(baudRate);
        channelElm.setDataBits(dataBits);
        channelElm.setStopBits(stopBits);
        channelElm.setParity(parity);
        channelElm.setSlaveId(slaveId);
        channelElm.setEndianness(endianness);
        channelElm.setFirstAddress(firstAddress);
    }
    
}
