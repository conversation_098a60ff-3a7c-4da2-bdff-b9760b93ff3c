package com.nti56.nlink.product.device.server.domain.thing.enumerate;

import lombok.Getter;

/**
 * 类说明: 协议字段类型
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-11-04 11:37:05
 * @since JDK 1.8
 */
public enum DriverFieldTypeEnum {
    
    CONST(1, "const", "常量"), 
    VARIABLE(2, "variable", "变量"),
    BYTE_LENGTH(3, "byteLength", "字节长度"),
    COUNT(4, "count", "数量"),
    IP(5, "ip", "ip地址"),
    CHECK(6, "check", "校验"),
    DYNAMIC(7, "dynamic", "动态参数"),
    DYNAMIC_LENGTH(8, "dynamicLength", "动长参数"),
    DYNAMIC_POSITION(9, "dynamicPosition", "动位参数"),
    REPEAT(10, "repeat", "重复数组")
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    DriverFieldTypeEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static DriverFieldTypeEnum typeOfValue(Integer value){
        DriverFieldTypeEnum[] values = DriverFieldTypeEnum.values();
        for (DriverFieldTypeEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static DriverFieldTypeEnum typeOfName(String name){
        DriverFieldTypeEnum[] values = DriverFieldTypeEnum.values();
        for (DriverFieldTypeEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static DriverFieldTypeEnum typeOfNameDesc(String nameDesc){
        DriverFieldTypeEnum[] values = DriverFieldTypeEnum.values();
        for (DriverFieldTypeEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
