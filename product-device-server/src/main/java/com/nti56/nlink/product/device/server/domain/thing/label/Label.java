package com.nti56.nlink.product.device.server.domain.thing.label;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.TypeReference;
import com.nti56.nlink.common.base.FieldValue;
import com.nti56.nlink.common.base.UniqueConstraint;
import com.nti56.nlink.common.base.UniqueConstraint.Unique;
import com.nti56.nlink.common.fetcher.CommonFetcher;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.nlink.product.device.client.model.dto.json.GatherParamField;
import com.nti56.nlink.product.device.client.model.dto.json.device.AccessElm;
import com.nti56.nlink.product.device.client.model.dto.json.enumerate.ThingDataTypeEnum;
import com.nti56.nlink.product.device.server.domain.thing.device.Device;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.DriverEnum;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.Snap7DataTypeEnum;
import com.nti56.nlink.product.device.server.entity.ChannelEntity;
import com.nti56.nlink.product.device.server.entity.EdgeGatewayEntity;
import com.nti56.nlink.product.device.server.entity.LabelEntity;
import com.nti56.nlink.product.device.server.entity.LabelGroupEntity;
import com.nti56.nlink.product.device.server.model.device.dto.DeviceCheckInfoContext;
import com.nti56.nlink.product.device.server.util.RegexUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 类说明: 标签领域对象
 *
 * 
 * 
## 标签地址格式

DB50.INT0
{areaType}{areaNum}.{dataType}{start}

DB50.BIT18.0
{areaType}{areaNum}.{dataType}{start}.{startBit}


 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-24 15:41:41
 * @since JDK 1.8
 */
@Data
@Slf4j
public class Label {
    
    private Long id;
    private String name;
    private String address;
    private Integer length;
    private ThingDataTypeEnum dataType;
    private Boolean isArray;
    private Boolean readOnly;
    private Integer stringBytes;

    private String labelGroupName;
    private String channelName;
    private Long labelGroupId;
    private Integer intervalMs;
    private Long channelId;
    private Long edgeGatewayId;
    private LabelEntity row;

    private static final UniqueConstraint labelGroupNameUniqueConstraint = new UniqueConstraint("label_group_id", "name");

    /**
     * 检查标签自身
     */
    public static Result<Label> checkInfoToBase(LabelEntity entity, DriverEnum driver){

        if(entity == null){
            return Result.error(ServiceCodeEnum.DEVICE_LABEL_NULL);
        }

        Label label = new Label();
        label.row = entity;

        label.id = entity.getId();
        if (!Optional.ofNullable(entity.getName()).isPresent() || entity.getName().length() > 128 || !RegexUtil.checkLabelName(entity.getName()).getSignal()) {
            return Result.error(ServiceCodeEnum.LABEL_NAME_ERROR);
        }
        label.name = entity.getName();
        if(Optional.ofNullable(entity.getAlias()).isPresent() && entity.getAlias().length() > 256){
            return Result.error(ServiceCodeEnum.LABEL_ALIAS_NAME_ERROR);
        }
        //dataType
        String dataTypeStr = entity.getDataType();
        if(dataTypeStr == null){
            return Result.error(ServiceCodeEnum.DEVICE_LABEL_DATATYPE_NULL,entity.getId());
        }
        ThingDataTypeEnum dataTypeEnum = ThingDataTypeEnum.typeOfName(dataTypeStr);
        if(dataTypeEnum == null){
            return Result.error(ServiceCodeEnum.DEVICE_LABEL_DATATYPE_ERROR,entity.getId(),dataTypeStr);
        }
        label.setDataType(dataTypeEnum);

        //address
        String addressStr = entity.getAddress();
        if(addressStr == null){
            return Result.error(ServiceCodeEnum.DEVICE_LABEL_ADDRESS_NULL);
        }
       
        if(driver == null){
            return Result.error("通道协议类型错误，labelId:" + entity.getId());
        }
        if(DriverEnum.SNAP7.equals(driver)){
            Result<Snap7DataTypeEnum> addressResult = RegexUtil.checkSnap7Address(addressStr);
            if(!addressResult.getSignal()){
                return Result.error(ServiceCodeEnum.DEVICE_LABEL_ADDRESS_ERROR,entity.getId(),addressStr,"");
            }
            Snap7DataTypeEnum addressDataType = addressResult.getResult();
            
            label.setAddress(addressStr);
            
            //snap7地址数据类型和数据类型
            Result<Void> dataTypeResult = checkDataType(addressDataType, dataTypeEnum);
            if(!dataTypeResult.getSignal()){
                return Result.error("类型不匹配，address:" + addressStr + ", dataType:" + dataTypeEnum);
            }
            
        }
        else if(DriverEnum.OPCUA.equals(driver)){
            Result<Void> addressResult = RegexUtil.checkOpcuaAddress(addressStr);
            if(!addressResult.getSignal()){
                return Result.error(ServiceCodeEnum.DEVICE_LABEL_ADDRESS_ERROR,entity.getId(),addressStr,"");
            }
            
            label.setAddress(addressStr);
        }else if(DriverEnum.MODBUS.equals(driver) || DriverEnum.ModbusRtuOverTcp.equals(driver) || DriverEnum.ModbusRtu.equals(driver)){
            Result<Void> addressResult = RegexUtil.checkModbusAddress(addressStr,entity.getDataType(),entity.getIsArray());
            if(!addressResult.getSignal()){
                return Result.error(ServiceCodeEnum.MODBUS_DEVICE_LABEL_ADDRESS_ERROR,addressResult.getMessage());
            }

            label.setAddress(addressStr);
        } else if(DriverEnum.ETHER_IP.equals(driver)){
            Result<Void> addressResult = RegexUtil.checkAbPLCAddress(addressStr,entity.getDataType());
            if(!addressResult.getSignal()){
                return Result.error(ServiceCodeEnum.DEVICE_LABEL_ADDRESS_ERROR,addressResult.getMessage());
            }
            label.setAddress(addressStr);
        } else if(DriverEnum.MELSEC.equals(driver)){
            Result<Void> addressResult = RegexUtil.checkMelsecAddress(addressStr,entity.getDataType());
            if(!addressResult.getSignal()){
                return Result.error(ServiceCodeEnum.MODBUS_DEVICE_LABEL_ADDRESS_ERROR,addressResult.getMessage());
            }
            label.setAddress(addressStr);
        } else if(DriverEnum.KEYENCE_NANO.equals(driver)){
            Result<Void> addressResult = RegexUtil.checkKeyenceNanoAddress(addressStr,entity.getDataType());
            if(!addressResult.getSignal()){
                return Result.error(ServiceCodeEnum.MODBUS_DEVICE_LABEL_ADDRESS_ERROR,addressResult.getMessage());
            }
            label.setAddress(addressStr);
        }  else if (DriverEnum.BACNET_IP.equals(driver)){
            if(BooleanUtils.isTrue(entity.getIsArray())){
                return Result.error("BACnet/IP不支持数组");
            }

            List<String> supportType = Arrays.asList("Boolean","Word", "DWord", "String", "Float");
            if (!supportType.contains(entity.getDataType())){
                return Result.error("BACnet/IP只支持Boolean、Word、DWord、Float、String");
            }

            Result<Void> addressResult = RegexUtil.checkBacnetIpAddress(addressStr);
            if(!addressResult.getSignal()){
                return Result.error(ServiceCodeEnum.DEVICE_LABEL_ADDRESS_ERROR,entity.getId(),addressStr,"");
            }
            label.setAddress(addressStr);
        }  else if(DriverEnum.ADS.equals(driver)){
            //String address 无需校验
            label.setAddress(addressStr);
        }else if(DriverEnum.IEC104.equals(driver)){
            if(BooleanUtils.isTrue(entity.getIsArray())){
                return Result.error("Iec104协议不支持数组");
            }
            switch (dataTypeEnum) {
                case DWORD :case BYTE:case FLOAT:case BOOLEAN:case WORD:
                    break;
                default:
                    return Result.error("IEC104只支持Boolean、Byte、Word、DWord、Float数据类型");
            }
            Result<Void> addressResult = RegexUtil.checkIec104Address(addressStr,dataTypeEnum);
            if(!addressResult.getSignal()){
                return Result.error(ServiceCodeEnum.DEVICE_LABEL_ADDRESS_ERROR,entity.getId(),addressStr,addressResult.getMessage());
            }
            label.setAddress(addressStr);
        }else if(DriverEnum.FINS_TCP.equals(driver)){
            Result<Void> addressResult = RegexUtil.checkFinsAddress(addressStr,entity.getDataType());
            if(!addressResult.getSignal()){
                return Result.error(ServiceCodeEnum.DEVICE_LABEL_ADDRESS_ERROR,addressResult.getMessage());
            }
            label.setAddress(addressStr);
        } else if(DriverEnum.INOVANCE_TCP.equals(driver)){
            Result<Void> addressResult = RegexUtil.checkInovanceAddress(addressStr,entity.getDataType());
            if(!addressResult.getSignal()){
                return Result.error(ServiceCodeEnum.MODBUS_DEVICE_LABEL_ADDRESS_ERROR,addressResult.getMessage());
            }
            label.setAddress(addressStr);
        }
        else if (DriverEnum.Panasonic.equals(driver)){
            List<String> noSupportType = Arrays.asList("Char","Byte", "QWORD", "BCD", "LBCD" , "DATE");
            if (noSupportType.contains(entity.getDataType())){
                return Result.error("Panasonic不支持Char、Byte");
            }

            if ("String".equals(entity.getDataType()) && entity.getIsArray()){
                return Result.error("Panasonic的String数据类型不支持数组");
            }

            Result<Void> addressResult = RegexUtil.checkPanasonicAddress(addressStr);
            if(!addressResult.getSignal()){
                return Result.error(ServiceCodeEnum.DEVICE_LABEL_ADDRESS_ERROR,entity.getId(),addressStr,"");
            }
            label.setAddress(addressStr);
        }else if(DriverEnum.CVC600.equals(driver)){
           /* Result<Void> addressResult = RegexUtil.checkInovanceAddress(addressStr,entity.getDataType());
            if(!addressResult.getSignal()){
                return Result.error(ServiceCodeEnum.MODBUS_DEVICE_LABEL_ADDRESS_ERROR,addressResult.getMessage());
            }*/
            ThingDataTypeEnum thingDataTypeEnum = ThingDataTypeEnum.typeOfName(entity.getDataType());
            switch (thingDataTypeEnum){
                case SHORT:
                case WORD:
                case BYTE:
                case LONG:
                case BOOLEAN:
                case DOUBLE:
                case STRING:
                    break;
                default:
                    return Result.error("不支持的类型的数据类型："+entity.getDataType());
            }
            label.setAddress(addressStr);
        } else if (DriverEnum.DLT645.equals(driver)) {
            ThingDataTypeEnum thingDataTypeEnum = ThingDataTypeEnum.typeOfName(entity.getDataType());
            switch (thingDataTypeEnum){
                case STRING:
                case DOUBLE:
                    break;
                default:
                    return Result.error("不支持的类型的数据类型："+entity.getDataType());
            }
            label.setAddress(addressStr);
        } else if (DriverEnum.HTTP.equals(driver)) {
            try {
                if (entity.getIsArray()){
                    return Result.error("http协议不支持数组");
                }
                List<String> address = JSON.parseObject(addressStr, new TypeReference<List<String>>(){});
            } catch (Exception e) {
                return Result.error("地址错误: " + addressStr + " , " + e.getMessage());
            }
            label.setAddress(addressStr);
        }else {
            return Result.error("协议暂不支持");
        }
        if(entity.getReadOnly() == null){
            return Result.error(ServiceCodeEnum.DEVICE_LABEL_READ_ONLY_NULL);
        }
        label.setReadOnly(entity.getReadOnly());

        //isArray
        Boolean isArray = entity.getIsArray();
        if(isArray == null){
            return Result.error(ServiceCodeEnum.DEVICE_LABEL_IS_ARRAY_NULL,entity.getId());
        }
        label.setIsArray(isArray);

        //length
        Integer lengthInt = entity.getLength();
        if(lengthInt == null){
            return Result.error(ServiceCodeEnum.DEVICE_LABEL_LENGTH_NULL,entity.getId());
        }
        if(label.getIsArray()){
            if(lengthInt < 1){
                return Result.error(ServiceCodeEnum.DEVICE_LABEL_LENGTH_ERROR,entity.getId(),lengthInt);
            }
        }else{
            if(lengthInt != 1){
                return Result.error(ServiceCodeEnum.DEVICE_LABEL_LENGTH_ERROR,entity.getId(),lengthInt);
            }
        }
        label.setLength(lengthInt);

        //string_bytes
        if(ThingDataTypeEnum.STRING.equals(label.getDataType())){
            Integer stringBytesInt = entity.getStringBytes();
            if(stringBytesInt == null){
                return Result.error(ServiceCodeEnum.DEVICE_LABEL_STRING_BYTES_ERROR,entity.getId());
            }
            if(stringBytesInt <= 0){
                return Result.error(ServiceCodeEnum.DEVICE_LABEL_STRING_BYTES_ERROR,entity.getId());
            }
            label.setStringBytes(stringBytesInt);
        }

        label.labelGroupId = entity.getLabelGroupId();

        label.intervalMs = entity.getIntervalMs();

        return Result.ok(label);
    }
    
    /**
     * 检查标签、所属分组、所属通道
     */
    public static Result<Label> checkInfoToChannel(LabelEntity entity, CommonFetcher commonFetcher){

        if(entity == null){
            return Result.error(ServiceCodeEnum.DEVICE_LABEL_NULL);
        }

        LabelGroupEntity labelGroupEntity = commonFetcher.get(entity.getLabelGroupId(), LabelGroupEntity.class);
        if(labelGroupEntity == null){
            return Result.error("找不到标签分组，labelId:" + entity.getId());
        }
        ChannelEntity channelEntity = commonFetcher.get(labelGroupEntity.getChannelId(), ChannelEntity.class);
        if(channelEntity == null){
            return Result.error("找不到通道，labelId:" + entity.getId() + ", labelGroupId:" + labelGroupEntity.getId());
        }
        DriverEnum driver = DriverEnum.typeOfValue(channelEntity.getDriver());

        Result<Label> baseResult = checkInfoToBase(entity, driver);
        if(!baseResult.getSignal()){
            return Result.error(baseResult.getMessage());
        }
        Label label = baseResult.getResult();
        label.labelGroupId = labelGroupEntity.getId();
        label.channelId = channelEntity.getId();
        label.labelGroupName = labelGroupEntity.getName();
        label.channelName = channelEntity.getName();
        label.edgeGatewayId = channelEntity.getEdgeGatewayId();
        return Result.ok(label);
    }
    /**
     * 检查标签、所属分组、所属通道
     */
    public static Result<Label> checkInfoToChannel2(LabelEntity entity, DeviceCheckInfoContext context){

        if(entity == null){
            return Result.error(ServiceCodeEnum.DEVICE_LABEL_NULL);
        }

        LabelGroupEntity labelGroupEntity = context.getLabelGroupEntityById(entity.getLabelGroupId());
        if(labelGroupEntity == null){
            return Result.error("找不到标签分组，labelId:" + entity.getId());
        }
        ChannelEntity channelEntity = context.getChannelEntityById(labelGroupEntity.getChannelId());
        if(channelEntity == null){
            return Result.error("找不到通道，labelId:" + entity.getId() + ", labelGroupId:" + labelGroupEntity.getId());
        }
        DriverEnum driver = DriverEnum.typeOfValue(channelEntity.getDriver());

        Result<Label> baseResult = checkInfoToBase(entity, driver);
        if(!baseResult.getSignal()){
            return Result.error(baseResult.getMessage());
        }
        Label label = baseResult.getResult();
        label.labelGroupId = labelGroupEntity.getId();
        label.channelId = channelEntity.getId();
        label.labelGroupName = labelGroupEntity.getName();
        label.channelName = channelEntity.getName();
        label.edgeGatewayId = channelEntity.getEdgeGatewayId();
        return Result.ok(label);
    }

    /**
     * 检查标签、所属分组、所属通道、策略、网关
     */
    public static Result<Label> checkInfoToEdgeGateway(LabelEntity entity, CommonFetcher commonFetcher){
        Result<Label> labelResult = checkInfoToChannel(entity, commonFetcher);
        if(!labelResult.getSignal()){
            return Result.error(labelResult.getMessage());
        }
        Label label = labelResult.getResult();

        //网关id
        if(label.channelId == null){
            return Result.error("标签所属通道id不能为空，labelName:" + label.name + ", channelId:" + label.channelId);
        }
        ChannelEntity channelEntity = commonFetcher.get(label.channelId, ChannelEntity.class);
        if (!Optional.ofNullable(channelEntity).isPresent()) {
            return Result.error("找不到标签所属通道，labelName:" + label.name + ", channelId:" + label.channelId);
        }
        //默认走标签自己间隔，为空的话走分组的间隔
        if(label.intervalMs == null || label.intervalMs == 0){
            if (channelEntity.getIntervalMs() == null){
                label.intervalMs = 10;
            }else {
                label.intervalMs = channelEntity.getIntervalMs();
            }
        }
        if(label.intervalMs < 10){
            label.intervalMs = 10;
        }

        //网关id
        if(label.edgeGatewayId == null){
            return Result.error("标签所属网关id不能为空，labelName:" + label.name + ", edgeGatewayId:" + label.edgeGatewayId);
        }
        EdgeGatewayEntity edgeGatewayEntity = commonFetcher.get(label.edgeGatewayId, EdgeGatewayEntity.class);
        if(edgeGatewayEntity == null){
            return Result.error("找不到标签所属网关，labelName:" + label.name + ", edgeGatewayId:" + label.edgeGatewayId);
        }

        return Result.ok(label);
    }

    /**
     * 检查标签、所属分组、所属通道、策略、网关
     */
    public static Result<Label> checkInfoToEdgeGateway2(LabelEntity entity, DeviceCheckInfoContext context){
        Result<Label> labelResult = checkInfoToChannel2(entity, context);
        if(!labelResult.getSignal()){
            return Result.error(labelResult.getMessage());
        }
        Label label = labelResult.getResult();

        //网关id
        if(label.channelId == null){
            return Result.error("标签所属通道id不能为空，labelName:" + label.name + ", channelId:" + label.channelId);
        }
        ChannelEntity channelEntity = context.getChannelEntityById(label.channelId);
        if (!Optional.ofNullable(channelEntity).isPresent()) {
            return Result.error("找不到标签所属通道，labelName:" + label.name + ", channelId:" + label.channelId);
        }
        //默认走标签自己间隔，为空的话走分组的间隔
        if(label.intervalMs == null || label.intervalMs == 0){
            if (channelEntity.getIntervalMs() == null){
                label.intervalMs = 10;
            }else {
                label.intervalMs = channelEntity.getIntervalMs();
            }
        }
        if(label.intervalMs < 10){
            label.intervalMs = 10;
        }

        //网关id
        if(label.edgeGatewayId == null){
            return Result.error("标签所属网关id不能为空，labelName:" + label.name + ", edgeGatewayId:" + label.edgeGatewayId);
        }
        EdgeGatewayEntity edgeGatewayEntity = context.getEedgeGatewayMapById(label.edgeGatewayId);
        if(edgeGatewayEntity == null){
            return Result.error("找不到标签所属网关，labelName:" + label.name + ", edgeGatewayId:" + label.edgeGatewayId);
        }

        return Result.ok(label);
    }

    private static Result<Void> checkDataType(Snap7DataTypeEnum addressDataType, ThingDataTypeEnum dataType){
        if(addressDataType == null){
            return Result.error("地址类型不能为空");
        }
        if(dataType == null){
            return Result.error("数据类型不能为空");
        }
        if(!Snap7TypeMatcher.checkMatch(addressDataType, dataType)){
            return Result.error("地址与数据类型不匹配");
        }
        return Result.ok();
    }

    public static void label2Access(List<AccessElm> accessElmList, List<LabelEntity> labelList, Long channelId) {
        labelList.stream().forEach(labelEntity -> {
            AccessElm build = AccessElm.builder()
                    .channelId(channelId)
                    .labelId(labelEntity.getId())
                    .labelName(labelEntity.getName())
                    .address(labelEntity.getAddress())
                    .dataType(labelEntity.getDataType())
                    .isArray(labelEntity.getIsArray())
                    .length(labelEntity.getLength())
                    .stringBytes(labelEntity.getStringBytes())
                    .build();
            accessElmList.add(build);
        });
    }

    public static Label build(LabelEntity entity,ChannelEntity channel) {
        Label label = new Label();
        label.row = entity;
        label.id = entity.getId();
        label.name = entity.getName();
        //dataType
        String dataTypeStr = entity.getDataType();
        ThingDataTypeEnum dataTypeEnum = ThingDataTypeEnum.typeOfName(dataTypeStr);
        label.setDataType(dataTypeEnum);
        //address
        String addressStr = entity.getAddress();
        label.setAddress(addressStr);
        label.setReadOnly(entity.getReadOnly());
        //isArray
        Boolean isArray = entity.getIsArray();
        label.setIsArray(isArray);
        //length
        Integer lengthInt = entity.getLength();
        label.setLength(lengthInt);
        //string_bytes
        label.setStringBytes(entity.getStringBytes());
        label.labelGroupId = entity.getLabelGroupId();
        label.intervalMs = entity.getIntervalMs();
        label.channelId = channel.getId();
        label.edgeGatewayId = channel.getEdgeGatewayId();
        return label;
    }


    /**
     * 创建运行时采集参数
     */
    public GatherParamField createGatherParam() {
        GatherParamField param = new GatherParamField();
        param.setLabelId(id);
        param.setLabelName(name);
        param.setParamType(dataType.getName());
        param.setIsArray(isArray);
        param.setAddress(address);
        param.setLength(length);
        param.setStringBytes(stringBytes);
        param.setChannelId(channelId);
        param.setInterval(intervalMs);
        param.setEdgeGatewayId(edgeGatewayId);
        return param;
    }



    public static Result<Void> checkCreateLabelNameUnique(Long labelGroupId, String labelName, CommonFetcher commonFetcher){
        if (!Optional.ofNullable(labelGroupId).isPresent()) {
            return Result.ok();
        }
        Unique unique = labelGroupNameUniqueConstraint.buildUnique(new FieldValue(labelGroupId), new FieldValue(labelName));
        LabelEntity sameEntity = commonFetcher.get(unique, LabelEntity.class);
        if(sameEntity != null){
            return Result.error("标签名已存在");
        }
        return Result.ok();
    }

    public static Result<Void> checkUpdateLabelNameUnique(Long labelGroupId, String labelName, Long id, CommonFetcher commonFetcher){
        if (!Optional.ofNullable(labelGroupId).isPresent()) {
            return Result.ok();
        }
        Unique unique = labelGroupNameUniqueConstraint.buildUnique(new FieldValue(labelGroupId), new FieldValue(labelName));
        LabelEntity sameEntity = commonFetcher.get(unique, LabelEntity.class);
        if(sameEntity != null && !sameEntity.getId().equals(id)){
            return Result.error("标签名已存在");
        }
        return Result.ok();
    }


    /**
     * 检查标签、所属分组、所属通道、策略、网关
     */
    public static Result<Label> checkInfoToEdgeGatewayNew(LabelEntity entity,
                                                          Map<Long,ChannelEntity> channelEntityMap,
                                                          Map<Long,EdgeGatewayEntity> edgeGatewayEntityMap,
                                                          Map<Long,LabelGroupEntity> labelGroupEntityMap
    ){
        Result<Label> labelResult = checkInfoToChannelNew(entity, labelGroupEntityMap,channelEntityMap);
        if(!labelResult.getSignal()){
            return Result.error(labelResult.getMessage());
        }
        Label label = labelResult.getResult();

        //网关id
        if(label.channelId == null){
            return Result.error("标签所属通道id不能为空，labelName:" + label.name + ", channelId:" + label.channelId);
        }
        ChannelEntity channelEntity = channelEntityMap.get(label.channelId);
        if (!Optional.ofNullable(channelEntity).isPresent()) {
            return Result.error("找不到标签所属通道，labelName:" + label.name + ", channelId:" + label.channelId);
        }
        //默认走标签自己间隔，为空的话走分组的间隔
        if(label.intervalMs == null || label.intervalMs == 0){
            if (channelEntity.getIntervalMs() == null){
                label.intervalMs = 10;
            }else {
                label.intervalMs = channelEntity.getIntervalMs();
            }
        }
        if(label.intervalMs < 10){
            label.intervalMs = 10;
        }

        //网关id
        if(label.edgeGatewayId == null){
            return Result.error("标签所属网关id不能为空，labelName:" + label.name + ", edgeGatewayId:" + label.edgeGatewayId);
        }
        EdgeGatewayEntity edgeGatewayEntity = edgeGatewayEntityMap.get(label.edgeGatewayId);
        if(edgeGatewayEntity == null){
            return Result.error("找不到标签所属网关，labelName:" + label.name + ", edgeGatewayId:" + label.edgeGatewayId);
        }

        return Result.ok(label);
    }

    public static Result<Label> checkInfoToChannelNew(LabelEntity entity,
                                                      Map<Long,LabelGroupEntity> labelGroupEntityMap,
                                                      Map<Long,ChannelEntity> channelEntityMap
                                                      ){

        if(entity == null){
            return Result.error(ServiceCodeEnum.DEVICE_LABEL_NULL);
        }

        LabelGroupEntity labelGroupEntity = labelGroupEntityMap.get(entity.getLabelGroupId());
        if(labelGroupEntity == null){
            return Result.error("找不到标签分组，labelId:" + entity.getId());
        }
        ChannelEntity channelEntity = channelEntityMap.get(labelGroupEntity.getChannelId());
        if(channelEntity == null){
            return Result.error("找不到通道，labelId:" + entity.getId() + ", labelGroupId:" + labelGroupEntity.getId());
        }
        DriverEnum driver = DriverEnum.typeOfValue(channelEntity.getDriver());

        Result<Label> baseResult = checkInfoToBase(entity, driver);
        if(!baseResult.getSignal()){
            return Result.error(baseResult.getMessage());
        }
        Label label = baseResult.getResult();
        label.labelGroupId = labelGroupEntity.getId();
        label.channelId = channelEntity.getId();
        label.labelGroupName = labelGroupEntity.getName();
        label.channelName = channelEntity.getName();
        label.edgeGatewayId = channelEntity.getEdgeGatewayId();
        return Result.ok(label);
    }


    
}
