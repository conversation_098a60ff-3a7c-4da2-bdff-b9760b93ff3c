package com.nti56.nlink.product.device.server.domain.thing.custom.special;

import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.client.model.dto.json.CustomDriverSpecialConfigField;
import com.nti56.nlink.product.device.client.model.dto.json.custom.FlagCarJimiElm;
import com.nti56.nlink.product.device.client.model.dto.json.custom.SpecialFlagCarJimiElm;
import com.nti56.nlink.product.device.client.model.dto.json.enumerate.ThingDataTypeEnum;

public class FlagCarJimi {

    private Boolean enabled; //true
    private String conditionValue1; //"0x7878"
    private ThingDataTypeEnum lengthType1; //"Byte"
    private String conditionValue2; //"0x7979"
    private ThingDataTypeEnum lengthType2; //"Word"

    private SpecialFlagCarJimiElm raw;

    private FlagCarJimi(Boolean enabled, SpecialFlagCarJimiElm raw) {
        this.enabled = enabled;
        this.raw = raw;
    }

    public static Result<FlagCarJimi> checkInfo(CustomDriverSpecialConfigField specialConfig) {
        if(specialConfig == null){
            return Result.ok(new FlagCarJimi(false, null));
        }
        SpecialFlagCarJimiElm elm = specialConfig.getFlagCarJimi();
        if(elm == null){
            return Result.ok(new FlagCarJimi(false, null));
        }
        if(!elm.getEnabled()){
            return Result.ok(new FlagCarJimi(false, elm));
        }
        FlagCarJimi flagCarJimi = new FlagCarJimi(true, elm);
        
        String conditionValue1 = elm.getConditionValue1();
        if(conditionValue1 == null || "".equals(conditionValue1)){
            return Result.error("条件1不能为空");
        }
        flagCarJimi.conditionValue1 = conditionValue1;

        String conditionValue2 = elm.getConditionValue2();
        if(conditionValue2 == null || "".equals(conditionValue2)){
            return Result.error("条件2不能为空");
        }
        flagCarJimi.conditionValue2 = conditionValue2;

        Integer lengthType1Int = elm.getLengthType1();
        if(lengthType1Int == null){
            return Result.error("类型1不能为空");
        }
        ThingDataTypeEnum lengthType1 = ThingDataTypeEnum.typeOfValue(lengthType1Int);
        if(lengthType1 == null){
            return Result.error("类型1错误");
        }
        flagCarJimi.lengthType1 = lengthType1;

        Integer lengthType2Int = elm.getLengthType2();
        if(lengthType2Int == null){
            return Result.error("类型2不能为空");
        }
        ThingDataTypeEnum lengthType2 = ThingDataTypeEnum.typeOfValue(lengthType2Int);
        if(lengthType2 == null){
            return Result.error("类型2错误");
        }
        flagCarJimi.lengthType2 = lengthType2;

        return Result.ok(flagCarJimi);
    }

    public FlagCarJimiElm toRuntimeElm() {
        FlagCarJimiElm elm = new FlagCarJimiElm();
        elm.setEnabled(enabled);
        elm.setConditionValue1(conditionValue1);
        elm.setConditionValue2(conditionValue2);
        if(lengthType1 != null){
            elm.setLengthType1(lengthType1.getName());
        }
        if(lengthType2 != null){
            elm.setLengthType2(lengthType2.getName());
        }
        return elm;
    }

}
