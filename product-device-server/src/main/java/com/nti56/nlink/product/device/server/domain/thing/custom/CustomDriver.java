package com.nti56.nlink.product.device.server.domain.thing.custom;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.Comparator;


import com.nti56.nlink.common.base.FieldValue;
import com.nti56.nlink.common.base.UniqueConstraint;
import com.nti56.nlink.common.base.UniqueConstraint.Unique;
import com.nti56.nlink.common.fetcher.CommonFetcher;
import com.nti56.nlink.common.fetcher.Preloader;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.client.model.dto.json.custom.MessageElm;
import com.nti56.nlink.product.device.client.model.dto.json.custom.MessageItemElm;
import com.nti56.nlink.product.device.server.domain.thing.custom.special.FlagCarJimi;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.CustomDriverFormatTypeEnum;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.CustomDriverStatusEnum;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.CustomFieldPartTypeEnum;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.CustomFieldTargetTypeEnum;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.EndianEnum;
import com.nti56.nlink.product.device.server.entity.CustomDriverEntity;
import com.nti56.nlink.product.device.server.entity.CustomFieldEntity;
import com.nti56.nlink.product.device.server.entity.CustomMessageEntity;
import com.nti56.nlink.product.device.server.util.RegexUtil;
import com.nti56.nlink.product.device.client.model.dto.json.CustomDriverRuntimeInfoField;
import com.nti56.nlink.product.device.client.model.dto.json.CustomDriverSpecialConfigField;

import lombok.Getter;

/**
 * 类说明: 自定义协议领域对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-11-18 14:23:52
 * @since JDK 1.8
 */
public class CustomDriver {

    @Getter
    private Long id;

    @Getter
    private String driverName;

    @Getter
    private String descript;

    @Getter
    private EndianEnum endian;
    
    @Getter
    private CustomDriverFormatTypeEnum formatType;

    @Getter
    private List<CustomField> fixHeaderFieldList;

    @Getter
    private List<CustomField> fixTailFieldList;

    @Getter
    private List<String> readLengthFieldList;

    @Getter
    private Integer extraLength;

    @Getter
    private List<CustomMessage> messageList;

    private FlagCarJimi flagCarJimi;

    private CustomDriverSpecialConfigField rawSpecialConfig;

    private CustomDriverStatusEnum status;

    public static final UniqueConstraint driverNameUniqueConstraint = new UniqueConstraint("driver_name");

    private CustomDriver() {}

    private static Result<CustomDriver> checkBase(
        CustomDriverEntity entity, 
        List<CustomFieldEntity> fieldEntityList
    ){
        
        CustomDriver customDriver = new CustomDriver();

        customDriver.descript = entity.getDescript();

        String driverName = entity.getDriverName();
        if(!RegexUtil.checkName(driverName)){
            return Result.error("协议名只能是英文、字母、下划线，且英文开头");
        }
        if(driverName.length() > 255){
            return Result.error("协议名长度不能超过255");
        }

        CustomDriverFormatTypeEnum formatType = CustomDriverFormatTypeEnum.typeOfValue(entity.getFormatType());
        if(formatType == null){
            return Result.error("协议格式类型不能为空");
        }
        customDriver.formatType = formatType;

        EndianEnum endian = EndianEnum.typeOfValue(entity.getEndian());
        if(endian == null){
            return Result.error("协议格式类型不能为空");
        }
        customDriver.endian = endian;
        
        //检查fixHeaderTail名不能重复
        Set<String> fixNameSet = new HashSet<>();
        List<CustomField> fixHeaderFieldList = new ArrayList<>();
        List<CustomField> fixTailFieldList = new ArrayList<>();
        if(fieldEntityList != null && fieldEntityList.size() > 0){
            Map<Integer, List<CustomFieldEntity>> partTypeFieldMap = fieldEntityList.stream()
                .collect(Collectors.groupingBy(CustomFieldEntity::getPartType));
            
            List<CustomFieldEntity> fixHeaderEntityList = partTypeFieldMap.get(CustomFieldPartTypeEnum.HEADER.getValue());
            List<CustomFieldEntity> fixTailEntityList = partTypeFieldMap.get(CustomFieldPartTypeEnum.TAIL.getValue());
            
            if(fixHeaderEntityList != null && fixHeaderEntityList.size() > 0){
                Integer fixHeaderIdx = 0;
                for(CustomFieldEntity fieldEntity:fixHeaderEntityList){
                    Result<CustomField> fieldResult = CustomField.checkFixHeaderField(fixHeaderIdx, fieldEntity);
                    if(!fieldResult.getSignal()){
                        return Result.error(fieldResult.getMessage());
                    }
                    CustomField field = fieldResult.getResult();
                    if(fixNameSet.contains(field.getFieldName())){
                        return Result.error("固定头字段重名：" + field.getFieldName());
                    }
                    fixHeaderFieldList.add(field);
                    fixNameSet.add(field.getFieldName());
                    fixHeaderIdx++;
                }
            }
            customDriver.fixHeaderFieldList = fixHeaderFieldList;
            
            
            if(fixTailEntityList != null && fixTailEntityList.size() > 0){
                Integer fixTailIdx = 0 - fixTailEntityList.size();
                for(CustomFieldEntity fieldEntity:fixTailEntityList){
                    Result<CustomField> fieldResult = CustomField.checkFixTailField(fixTailIdx, fieldEntity);
                    if(!fieldResult.getSignal()){
                        return Result.error(fieldResult.getMessage());
                    }
                    CustomField field = fieldResult.getResult();
                    if(fixNameSet.contains(field.getFieldName())){
                        return Result.error("固定尾字段重名：" + field.getFieldName());
                    }
                    fixTailFieldList.add(field);
                    fixNameSet.add(field.getFieldName());
                    fixTailIdx++;
                }
            }
            customDriver.fixTailFieldList = fixTailFieldList;
        }

        //检查readLengthFields字段必须在fixHeader里面
        String readLengthFields = entity.getReadLengthFields();
        List<String> readLengthFieldList = new ArrayList<>();
        if(readLengthFields != null && !"".equals(readLengthFields)){
            readLengthFieldList = Arrays.asList(readLengthFields.split(","));
            for(String s:readLengthFieldList){
                if(!fixNameSet.contains(s)){
                    return Result.error("总报文长度读取字段必须是固定报文头里的字段:" + driverName + "," + s);
                }
            }
            customDriver.readLengthFieldList = readLengthFieldList;
        }else{
            return Result.error("总报文长度读取字段不能为空");
        }

        //检查messageType
        Optional<CustomField> findAny = fixHeaderFieldList.stream()
            .filter(item -> {
                return CustomMessage.MESSAGE_TYPE.equalsIgnoreCase(item.getFieldName());
            })
            .findAny();
        if(!findAny.isPresent()){
            return Result.error("必须包含messageType字段或MessageType");
        }
        //额外报文长度
        customDriver.extraLength = entity.getExtraLength();

        Result<FlagCarJimi> flagCarJimiResult = FlagCarJimi.checkInfo(entity.getSpecialConfig());
        if(!flagCarJimiResult.getSignal()){
            return Result.error(flagCarJimiResult.getMessage());
        }

        customDriver.flagCarJimi = flagCarJimiResult.getResult();

        customDriver.rawSpecialConfig = entity.getSpecialConfig();

        return Result.ok(customDriver);
    }

    public static Result<CustomDriver> checkCreate(
        CustomDriverEntity entity, 
        List<CustomFieldEntity> fieldEntityList, 
        CommonFetcher commonFetcher
    ) {
        Result<CustomDriver> result = checkBase(entity, fieldEntityList);
        if(!result.getSignal()){
            return Result.error(result.getMessage());
        }

        CustomDriver customDriver = result.getResult();

        //检查协议名不能重复
        String driverName = entity.getDriverName();
        if(driverName == null || "".equals(driverName)){
            return Result.error("协议名不能为空");
        }
        Unique unique = driverNameUniqueConstraint.buildUnique(new FieldValue(driverName));
        CustomDriverEntity sameEntity = commonFetcher.get(unique, CustomDriverEntity.class);
        if(sameEntity != null){
            return Result.error("协议名已存在");
        }

        customDriver.driverName = driverName;
        customDriver.status = CustomDriverStatusEnum.DISABLED;

        return Result.ok(customDriver);
    }

    public static Result<CustomDriver> checkUpdate(
        CustomDriverEntity entity, 
        List<CustomFieldEntity> fieldEntityList, 
        CommonFetcher commonFetcher
    ) {
        //基础检查
        Result<CustomDriver> result = checkBase(entity, fieldEntityList);
        if(!result.getSignal()){
            return Result.error(result.getMessage());
        }

        CustomDriver customDriver = result.getResult();

        //旧协议检查
        Long id = entity.getId();
        Result<CustomDriver> oldCustomDriverResult = checkInfoToMessage(id, commonFetcher);
        if(!oldCustomDriverResult.getSignal()){
            return Result.error("旧协议错误：" + oldCustomDriverResult.getMessage());
        }
        CustomDriver oldCustomDriver = oldCustomDriverResult.getResult();
        customDriver.id = id;

        //检查协议名不能重复
        String driverName = entity.getDriverName();
        if(driverName == null || "".equals(driverName)){
            return Result.error("协议名不能为空");
        }
        Unique unique = driverNameUniqueConstraint.buildUnique(new FieldValue(driverName));
        CustomDriverEntity sameEntity = commonFetcher.get(unique, CustomDriverEntity.class);
        if(sameEntity != null && !id.equals(sameEntity.getId())){
            return Result.error("协议名已存在");
        }
        customDriver.driverName = driverName;

        //更新协议包
        List<CustomMessage> oldMessageList = oldCustomDriver.getMessageList();
        if(oldMessageList == null || oldMessageList.size() <= 0){
            return Result.ok(customDriver);
        }
        List<CustomMessage> messageList = new ArrayList<>();
        List<CustomField> fixHeaderFieldList = customDriver.getFixHeaderFieldList();
        List<CustomField> fixTailFieldList = customDriver.getFixTailFieldList();
        for(CustomMessage oldMessage:oldMessageList){
            Result<Void> updateResult = oldMessage.updateFix(fixHeaderFieldList, fixTailFieldList);
            if(!updateResult.getSignal()){
                return Result.error(updateResult.getMessage());
            }
            messageList.add(oldMessage);
        }
        customDriver.messageList = messageList;

        CustomDriverStatusEnum status = CustomDriverStatusEnum.typeOfValue(entity.getStatus());
        customDriver.status = status;

        return Result.ok(customDriver);
    }

    public static Result<CustomDriver> checkInfoToHeader(CustomDriverEntity entity, CommonFetcher commonFetcher){
        
        if(entity == null){
            return Result.error("协议不能为空");
        }

        Long id = entity.getId();

        if(id == null){
            return Result.error("协议id不能为空");
        }

        //协议头尾
        Preloader<CustomFieldEntity> fieldPreloader = commonFetcher.preloader(
            "target_type", 
            CustomFieldTargetTypeEnum.DRIVER_FIELD.getValue(), 
            CustomFieldEntity.class
        ).preload("target_id", id.toString());
        List<CustomFieldEntity> fieldEntityList = fieldPreloader.list();
        if(fieldEntityList != null && fieldEntityList.size() > 0){
            fieldEntityList = fieldEntityList.stream()
                .sorted(Comparator.comparing(CustomFieldEntity::getPartType))
                .collect(Collectors.toList());
        }
        Result<CustomDriver> result = checkBase(entity, fieldEntityList);
        if(!result.getSignal()){
            return Result.error(result.getMessage());
        }

        CustomDriver customDriver = result.getResult();

        customDriver.id = id;

        
        //检查协议名不能重复
        String driverName = entity.getDriverName();
        if(driverName == null || "".equals(driverName)){
            return Result.error("协议名不能为空");
        }
        Unique unique = driverNameUniqueConstraint.buildUnique(new FieldValue(driverName));
        CustomDriverEntity sameEntity = commonFetcher.get(unique, CustomDriverEntity.class);
        if(sameEntity != null && !id.equals(sameEntity.getId())){
            return Result.error("协议名已存在");
        }
        customDriver.driverName = driverName;

        CustomDriverStatusEnum status = CustomDriverStatusEnum.typeOfValue(entity.getStatus());
        customDriver.status = status;
        
        return Result.ok(customDriver);
    }

    public static Result<CustomDriver> checkInfoToMessage(CustomDriverEntity entity, CommonFetcher commonFetcher) {

        Result<CustomDriver> headerResult = checkInfoToHeader(entity, commonFetcher);
        if(!headerResult.getSignal()){
            return Result.error(headerResult.getMessage());
        }
        CustomDriver customDriver = headerResult.getResult();
        Long id = customDriver.getId();

        //协议包
        List<CustomMessageEntity> messageEntityList = commonFetcher.list("custom_driver_id", id, CustomMessageEntity.class);
        if(messageEntityList == null || messageEntityList.size() <= 0){
            return Result.ok(customDriver);
        }
        
        List<CustomMessage> messageList = new ArrayList<>();
        List<CustomField> fixHeaderFieldList = customDriver.getFixHeaderFieldList();
        List<CustomField> fixTailFieldList = customDriver.getFixTailFieldList();
        for(CustomMessageEntity messageEntity:messageEntityList){
            Result<CustomMessage> messageResult = CustomMessage.checkInfo(messageEntity, fixHeaderFieldList, fixTailFieldList, commonFetcher);
            if(!messageResult.getSignal()){
                return Result.error(messageResult.getMessage());
            }
            CustomMessage customMessage = messageResult.getResult();
            messageList.add(customMessage);
        }
        customDriver.messageList = messageList;
        

        return Result.ok(customDriver);
    }

    public static Result<CustomDriver> checkInfoToMessage(Long id, CommonFetcher commonFetcher) {
        if(id == null){
            return Result.error("协议id不能为空");
        }

        CustomDriverEntity entity = commonFetcher.get(id, CustomDriverEntity.class);
        if(entity == null){
            return Result.error("找不到协议：id:" + id);
        }
        
        return checkInfoToMessage(entity, commonFetcher);
    }

    public CustomDriverEntity toEntity() {
        CustomDriverEntity entity = new CustomDriverEntity();
        entity.setId(id);
        entity.setDriverName(driverName);
        entity.setDescript(descript);
        entity.setFormatType(formatType.getValue());
        entity.setEndian(endian.getValue());
        if(readLengthFieldList != null && readLengthFieldList.size() > 0){
            entity.setReadLengthFields(readLengthFieldList.stream().collect(Collectors.joining(",")));
        }
        entity.setExtraLength(extraLength);
        entity.setSpecialConfig(rawSpecialConfig);
        if(status != null){
            entity.setStatus(status.getValue());
        }
        return entity;
    }

    public CustomDriverRuntimeInfoField toRuntimeInfo(){
        CustomDriverRuntimeInfoField define = new CustomDriverRuntimeInfoField();
        define.setDriverName(driverName);
        define.setDescript(descript);
        define.setFormatType(formatType.getName());
        define.setEndian(endian.getName());
        define.setReadLengthFields(readLengthFieldList);
        define.setExtraLength(extraLength);
        List<MessageItemElm> fixHeader = new ArrayList<>();
        if(fixHeaderFieldList != null && fixHeaderFieldList.size() > 0){
            for(CustomField field:fixHeaderFieldList){
                fixHeader.add(field.toRuntimeInfo());
            }
        }
        define.setFixHeader(fixHeader);
        List<MessageItemElm> fixTail = new ArrayList<>();
        if(fixTailFieldList != null && fixTailFieldList.size() > 0){
            for(CustomField field:fixTailFieldList){
                fixTail.add(field.toRuntimeInfo());
            }
        }
        define.setFixTail(fixTail);

        List<MessageElm> messages = new ArrayList<>();
        if(messageList != null && messageList.size() > 0){
            for(CustomMessage message:messageList){
                messages.add(message.toRuntimeInfo());
            }
        }
        define.setMessages(messages);

        define.setFlagCarJimi(flagCarJimi.toRuntimeElm());
        define.setExtraLength(extraLength);
        return define;
    }

    public List<CustomMessageEntity> toMessageEntityList() {
        List<CustomMessageEntity> messages = new ArrayList<>();
        if(messageList != null && messageList.size() > 0){
            for(CustomMessage message:messageList){
                messages.add(message.toEntity());
            }
        }
        return messages;
    }
    
}
