package com.nti56.nlink.product.device.server.domain.thing.custom;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.Comparator;

import org.springframework.beans.BeanUtils;

import com.nti56.nlink.common.base.FieldValue;
import com.nti56.nlink.common.base.UniqueConstraint;
import com.nti56.nlink.common.base.UniqueConstraint.Unique;
import com.nti56.nlink.common.fetcher.CommonFetcher;
import com.nti56.nlink.common.fetcher.Preloader;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.client.model.dto.json.AutoResponseConfigField;
import com.nti56.nlink.product.device.client.model.dto.json.custom.CopyFieldElm;
import com.nti56.nlink.product.device.client.model.dto.json.custom.MessageElm;
import com.nti56.nlink.product.device.client.model.dto.json.custom.MessageItemElm;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.CustomFieldPartTypeEnum;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.CustomFieldTargetTypeEnum;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.DirectionEnum;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.DriverFieldTypeEnum;
import com.nti56.nlink.product.device.server.entity.CustomDriverEntity;
import com.nti56.nlink.product.device.server.entity.CustomFieldEntity;
import com.nti56.nlink.product.device.server.entity.CustomMessageEntity;
import com.nti56.nlink.product.device.server.model.custom.CustomFieldBo;
import com.nti56.nlink.product.device.server.model.custom.CustomMessageBo;
import com.nti56.nlink.product.device.server.model.custom.CustomMessageFullBo;
import com.nti56.nlink.product.device.server.util.RegexUtil;

import lombok.Getter;

/**
 * 类说明: 自定义协议包领域对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-11-18 14:38:58
 * @since JDK 1.8
 */
public class CustomMessage {

    private static final UniqueConstraint messageNameUniqueConstraint = new UniqueConstraint("custom_driver_id", "message_name");
    
    public static final String MESSAGE_TYPE = "messageType";

    @Getter
    public Long id;

    @Getter
    public String messageName;
    
    @Getter
    private String descript;

    @Getter
    private DirectionEnum direction;

    @Getter
    private Boolean autoResponse;

    @Getter
    private String autoResponseMessageName;
    
    @Getter
    private AutoResponseConfigField autoResponseConfig;

    @Getter
    private String clientKeyFieldName;

    @Getter
    private Long customDriverId;

    @Getter
    private String customDriverName;

    @Getter
    private List<CustomField> messageFieldList;

    @Getter
    private Boolean autoSend;
    @Getter
    private Long autoSendInterval;
    @Getter
    private String autoSendVarStr;
    
    private CustomMessage() {}

    private static Result<CustomMessage> checkBase(
        CustomMessageEntity entity,
        List<CustomField> fixHeaderFieldList,
        List<CustomField> fixTailFieldList,
        List<CustomFieldEntity> messageFieldEntityList
    ) {
        String messageName = entity.getMessageName();
        if(messageName == null || "".equals(messageName)){
            return Result.error("协议包名不能为空");
        }
        if(!RegexUtil.checkName(messageName)){
            return Result.error("协议包名只能是英文、字母、下划线，且英文开头");
        }
        if(messageName.length() > 255){
            return Result.error("协议包名长度不能超过255");
        }

        CustomMessage customMessage = new CustomMessage();

        customMessage.messageName = messageName;
        customMessage.descript = entity.getDescript();
        customMessage.clientKeyFieldName = entity.getClientKeyFieldName();

        //检查收发类型
        Integer directionInt = entity.getDirection();
        DirectionEnum direction = DirectionEnum.typeOfValue(directionInt);
        if(direction == null){
            direction = DirectionEnum.SEND_AND_RECEIVE;
        }
        customMessage.direction = direction;

        //校验字段
        Result<List<CustomField>> messageFieldListResult = checkField(fixHeaderFieldList, fixTailFieldList, messageFieldEntityList);
        if(!messageFieldListResult.getSignal()){
            return Result.error(messageFieldListResult.getMessage());
        }
        List<CustomField> messageFieldList = messageFieldListResult.getResult();

        customMessage.messageFieldList = messageFieldList;

        //校验关联
        Result<Void> fieldReferenceResult = checkFieldReference(messageFieldList, customMessage.clientKeyFieldName);
        if(!fieldReferenceResult.getSignal()){
            return Result.error(fieldReferenceResult.getMessage());
        }

        //检查自动发送
        Result<Void> autoSendResult = checkAutoSend(entity, customMessage);
        if(!autoSendResult.getSignal()){
            return Result.error(autoSendResult.getMessage());
        }
        
        return Result.ok(customMessage);
    }

    public static Result<CustomMessage> checkCreate(
        CustomMessageEntity entity,
        List<CustomFieldEntity> messageFieldEntityList, 
        CommonFetcher commonFetcher
    ) {
        if(entity == null){
            return Result.error("协议包不能为空");
        }

        Long customDriverId = entity.getCustomDriverId();
        if(customDriverId == null){
            return Result.error("协议包所属协议id不能为空");
        }

        //检查协议包名不能重复
        String messageName = entity.getMessageName();
        if(messageName == null || "".equals(messageName)){
            return Result.error("协议包名不能为空");
        }
        Unique unique = messageNameUniqueConstraint.buildUnique(
            new FieldValue(customDriverId), 
            new FieldValue(messageName)
        );
        CustomMessageEntity sameEntity = commonFetcher.get(unique, CustomMessageEntity.class);
        if(sameEntity != null){
            return Result.error("协议包名已存在");
        }

        //设置默认sortNo为idx
        if(messageFieldEntityList != null && messageFieldEntityList.size() > 0){
            Integer idx = 0;
            for(CustomFieldEntity f:messageFieldEntityList){
                f.setSortNo(idx++);
            }
        }

        //检查所属协议
        CustomDriverEntity customDriverEntity = commonFetcher.get(customDriverId, CustomDriverEntity.class);
        if(customDriverEntity == null){
            return Result.error("找不到协议：id:" + customDriverId);
        }
        Result<CustomDriver> customDriverResult = CustomDriver.checkInfoToHeader(customDriverEntity, commonFetcher);
        if(!customDriverResult.getSignal()){
            return Result.error(customDriverResult.getMessage());
        }
        CustomDriver customDriver = customDriverResult.getResult();
        List<CustomField> fixHeaderFieldList = customDriver.getFixHeaderFieldList();
        List<CustomField> fixTailFieldList = customDriver.getFixTailFieldList();

        Result<CustomMessage> result = checkBase(entity, fixHeaderFieldList, fixTailFieldList, messageFieldEntityList);
        if(!result.getSignal()){
            return Result.error(result.getMessage());
        }
        CustomMessage customMessage = result.getResult();

        customMessage.customDriverId = customDriverId;
        customMessage.customDriverName = customDriver.getDriverName();

        
        //检查自动响应
        Result<Void> autoResponseResult = checkAutoResponse(entity, customMessage, commonFetcher);
        if(!autoResponseResult.getSignal()){
            return Result.error(autoResponseResult.getMessage());
        }
        
        return Result.ok(customMessage);
    }

    public static Result<CustomMessage> checkUpdate(
        CustomMessageEntity entity,
        List<CustomFieldEntity> messageFieldEntityList, 
        CommonFetcher commonFetcher
    ) {
        if(entity == null){
            return Result.error("协议包不能为空");
        }

        Long id = entity.getId();
        if(id == null){
            return Result.error("协议包id不能为空");
        }

        Long customDriverId = entity.getCustomDriverId();
        if(customDriverId == null){
            return Result.error("协议包所属协议id不能为空");
        }

        //检查协议包名不能重复
        String messageName = entity.getMessageName();
        if(messageName == null || "".equals(messageName)){
            return Result.error("协议包名不能为空");
        }
        Unique unique = messageNameUniqueConstraint.buildUnique(
            new FieldValue(customDriverId), 
            new FieldValue(messageName)
        );
        CustomMessageEntity sameEntity = commonFetcher.get(unique, CustomMessageEntity.class);
        if(sameEntity != null && !id.equals(sameEntity.getId())){
            return Result.error("协议包名已存在");
        }

        //设置默认sortNo为idx、默认所属消息
        if(messageFieldEntityList != null && messageFieldEntityList.size() > 0){
            Integer idx = 0;
            for(CustomFieldEntity f:messageFieldEntityList){
                f.setSortNo(idx++);
                f.setTargetId(id);
            }
        }

        //检查所属协议
        CustomDriverEntity customDriverEntity = commonFetcher.get(customDriverId, CustomDriverEntity.class);
        if(customDriverEntity == null){
            return Result.error("找不到协议：id:" + customDriverId);
        }
        Result<CustomDriver> customDriverResult = CustomDriver.checkInfoToHeader(customDriverEntity, commonFetcher);
        if(!customDriverResult.getSignal()){
            return Result.error(customDriverResult.getMessage());
        }
        CustomDriver customDriver = customDriverResult.getResult();
        List<CustomField> fixHeaderFieldList = customDriver.getFixHeaderFieldList();
        List<CustomField> fixTailFieldList = customDriver.getFixTailFieldList();

        Result<CustomMessage> result = checkBase(entity, fixHeaderFieldList, fixTailFieldList, messageFieldEntityList);
        if(!result.getSignal()){
            return Result.error(result.getMessage());
        }
        CustomMessage customMessage = result.getResult();

        customMessage.id = id;
        customMessage.customDriverId = customDriverId;
        customMessage.customDriverName = customDriver.getDriverName();

        //检查自动响应
        Result<Void> autoResponseResult = checkAutoResponse(entity, customMessage, commonFetcher);
        if(!autoResponseResult.getSignal()){
            return Result.error(autoResponseResult.getMessage());
        }
        
        return Result.ok(customMessage);
    }

    /**
     * 更新协议头部引发更新消息的头部
     * @param newFixHeaderFieldList
     * @return
     */
    public Result<Void> updateFix(List<CustomField> newFixHeaderFieldList, List<CustomField> newFixTailFieldList) {
        
        Map<CustomFieldPartTypeEnum, List<CustomField>> group = messageFieldList.stream().collect(Collectors.groupingBy(CustomField::getPartType));
        //头
        List<CustomField> oldHeaderMessageFieldList = group.get(CustomFieldPartTypeEnum.HEADER);
        //体
        List<CustomField> oldBodyMessageFieldList = group.get(CustomFieldPartTypeEnum.BODY);
        //尾
        List<CustomField> oldTailMessageFieldList = group.get(CustomFieldPartTypeEnum.TAIL);


        //构建新头部
        List<CustomField> newMessageFieldList = new ArrayList<>();
        Integer idx = 0;
        if(newFixHeaderFieldList != null && newFixHeaderFieldList.size() > 0){
            //fieldName -> CustomField
            Map<String, CustomField> fieldNameMap = new HashMap<>();
            if(oldHeaderMessageFieldList != null){
                for(CustomField f:oldHeaderMessageFieldList){
                    fieldNameMap.put(f.getFieldName(), f);
                }
            }
            for(CustomField headerField:newFixHeaderFieldList){
                Result<CustomField> newFieldResult = headerField.buildMessageField(id, fieldNameMap.get(headerField.getFieldName()));
                if(!newFieldResult.getSignal()){
                    return Result.error("更新头部字段失败，fieldName:" + headerField.getFieldName());
                }
                CustomField field = newFieldResult.getResult();
                newMessageFieldList.add(field);
                idx++;
            }
        }

        //更新体部sortNo
        if(oldBodyMessageFieldList != null && oldBodyMessageFieldList.size() > 0){
            for(CustomField field:oldBodyMessageFieldList){
                field.updateSortNo(idx);
                newMessageFieldList.add(field);
                idx++;
            }
        }

        //构建新尾部
        if(newFixTailFieldList != null && newFixTailFieldList.size() > 0){
            //fieldName -> CustomField
            Map<String, CustomField> fieldNameMap = new HashMap<>();
            if(oldTailMessageFieldList != null){
                for(CustomField f:oldTailMessageFieldList){
                    fieldNameMap.put(f.getFieldName(), f);
                }
            }
            for(CustomField tailField:newFixTailFieldList){
                Result<CustomField> newFieldResult = tailField.buildMessageField(id, fieldNameMap.get(tailField.getFieldName()));
                if(!newFieldResult.getSignal()){
                    return Result.error("更新头部字段失败，fieldName:" + tailField.getFieldName());
                }
                CustomField field = newFieldResult.getResult();
                newMessageFieldList.add(field);
                idx++;
            }
        }

        //检查
        Result<Void> checkResult = checkFieldReference(messageFieldList, clientKeyFieldName);
        if(!checkResult.getSignal()){
            return Result.error(checkResult.getMessage());
        }

        this.messageFieldList = newMessageFieldList;
        return Result.ok();
    }

    public CustomMessageEntity toEntity() {
        CustomMessageEntity entity = new CustomMessageEntity();
        entity.setId(id);
        entity.setMessageName(messageName);
        entity.setDescript(descript);
        entity.setDirection(direction.getValue());
        
        entity.setAutoResponse(autoResponse);
        entity.setAutoResponseMessageName(autoResponseMessageName);
        entity.setAutoResponseConfig(autoResponseConfig);
        
        entity.setAutoSend(autoSend);
        entity.setAutoSendInterval(autoSendInterval);
        entity.setAutoSendVarStr(autoSendVarStr);

        entity.setClientKeyFieldName(clientKeyFieldName);
        entity.setCustomDriverId(customDriverId);
        return entity;
    }

    public CustomMessageFullBo toFullBo() {
        CustomMessageFullBo bo = new CustomMessageFullBo();
        CustomMessageBo m = new CustomMessageBo();
        BeanUtils.copyProperties(toEntity(), m);
        m.setCustomDriverName(customDriverName);
        bo.setCustomMessageEntity(m);

        if(messageFieldList != null && messageFieldList.size() > 0){
            List<CustomFieldBo> l = new ArrayList<>();
            for(CustomField field:messageFieldList){
                l.add(field.toFullBo());
            }
            bo.setMessageFieldEntityList(l);
        }

        return bo;
    }

	public static Result<CustomMessage> checkInfo(
        CustomMessageEntity entity,
        CommonFetcher commonFetcher
    ) {
        Result<CustomMessage> result = checkInfoWithoutAutoResponse(entity, commonFetcher);
        if(!result.getSignal()){
            return Result.error(result.getMessage());
        }
        CustomMessage customMessage = result.getResult();

        //检查自动响应
        Result<Void> autoResponseResult = checkAutoResponse(entity, customMessage, commonFetcher);
        if(!autoResponseResult.getSignal()){
            return Result.error(autoResponseResult.getMessage());
        }
        
        return Result.ok(customMessage);
    }

	private static Result<CustomMessage> checkInfoWithoutAutoResponse(
        CustomMessageEntity entity,
        CommonFetcher commonFetcher
    ) {
        if(entity == null){
            return Result.error("协议包不能为空");
        }

        Long id = entity.getId();
        if(id == null){
            return Result.error("协议包id不能为空");
        }

        Long customDriverId = entity.getCustomDriverId();
        if(customDriverId == null){
            return Result.error("协议包所属协议id不能为空");
        }

        //检查所属协议
        CustomDriverEntity customDriverEntity = commonFetcher.get(customDriverId, CustomDriverEntity.class);
        if(customDriverEntity == null){
            return Result.error("找不到协议：id:" + customDriverId);
        }
        Result<CustomDriver> customDriverResult = CustomDriver.checkInfoToHeader(customDriverEntity, commonFetcher);
        if(!customDriverResult.getSignal()){
            return Result.error(customDriverResult.getMessage());
        }
        CustomDriver customDriver = customDriverResult.getResult();
        List<CustomField> fixHeaderFieldList = customDriver.getFixHeaderFieldList();
        List<CustomField> fixTailFieldList = customDriver.getFixTailFieldList();

        //查出字段
        Preloader<CustomFieldEntity> preloader = commonFetcher.preloader(
            "target_type", 
            CustomFieldTargetTypeEnum.MESSAGE_FIELD.getValue(), 
            CustomFieldEntity.class
        ).preload("target_id", id.toString());
        List<CustomFieldEntity> messageFieldEntityList = preloader.list();
        if(messageFieldEntityList != null && messageFieldEntityList.size() > 0){
            messageFieldEntityList = messageFieldEntityList.stream()
                .sorted(Comparator.comparing(CustomFieldEntity::getPartType))
                .collect(Collectors.toList());
        }
        Result<CustomMessage> baseResult = checkBase(entity, fixHeaderFieldList, fixTailFieldList, messageFieldEntityList);
        if(!baseResult.getSignal()){
            return Result.error(baseResult.getMessage());
        }

        CustomMessage customMessage = baseResult.getResult();
        customMessage.customDriverId = customDriverId;
        customMessage.customDriverName = customDriver.getDriverName();

        //检查协议包名不能重复
        String messageName = entity.getMessageName();
        if(messageName == null || "".equals(messageName)){
            return Result.error("协议包名不能为空");
        }
        Unique unique = messageNameUniqueConstraint.buildUnique(
            new FieldValue(customDriverId), 
            new FieldValue(messageName)
        );
        CustomMessageEntity sameEntity = commonFetcher.get(unique, CustomMessageEntity.class);
        if(sameEntity != null && !id.equals(sameEntity.getId())){
            return Result.error("协议包名已存在");
        }

        if(!RegexUtil.checkName(messageName)){
            return Result.error("协议包名只能是英文、字母、下划线，且英文开头");
        }
        if(messageName.length() > 255){
            return Result.error("协议包名长度不能超过255");
        }

        customMessage.id = id;

        return Result.ok(customMessage);
    } 

	public static Result<CustomMessage> checkInfo(
        CustomMessageEntity entity, 
        List<CustomField> fixHeaderFieldList, 
        List<CustomField> fixTailFieldList, 
        CommonFetcher commonFetcher
    ) {
        
        if(entity == null){
            return Result.error("协议包不能为空");
        }

        Long id = entity.getId();
        if(id == null){
            return Result.error("协议包id不能为空");
        }

        Long customDriverId = entity.getCustomDriverId();
        if(customDriverId == null){
            return Result.error("所属协议id不能为空");
        }

        //查出字段
        Preloader<CustomFieldEntity> preloader = commonFetcher.preloader(
            "target_type", 
            CustomFieldTargetTypeEnum.MESSAGE_FIELD.getValue(), 
            CustomFieldEntity.class
        ).preload("target_id", id.toString());
        List<CustomFieldEntity> messageFieldEntityList = preloader.list();
        if(messageFieldEntityList != null && messageFieldEntityList.size() > 0){
            messageFieldEntityList = messageFieldEntityList.stream()
                .sorted(Comparator.comparing(CustomFieldEntity::getPartType))
                .collect(Collectors.toList());
        }
        Result<CustomMessage> result = checkBase(entity, fixHeaderFieldList, fixTailFieldList, messageFieldEntityList);
        if(!result.getSignal()){
            return Result.error(result.getMessage());
        }
        CustomMessage customMessage = result.getResult();

        customMessage.id = id;
        customMessage.customDriverId = customDriverId;

        //检查自动响应
        Result<Void> autoResponseResult = checkAutoResponse(entity, customMessage, commonFetcher);
        if(!autoResponseResult.getSignal()){
            return Result.error(autoResponseResult.getMessage());
        }
        
		return Result.ok(customMessage);
	}

    private static Result<List<CustomField>> checkField(
        List<CustomField> fixHeaderFieldList,
        List<CustomField> fixTailFieldList,
        List<CustomFieldEntity> messageFieldEntityList
    ){
        final Integer messageFieldSize = (messageFieldEntityList == null ? 0: messageFieldEntityList.size());
        final Integer fixTailFieldSize = (fixTailFieldList == null? 0: fixTailFieldList.size());
        
        List<CustomField> messageFieldList = new ArrayList<>();
        Integer idx = 0;
        
        //校验并构建头部字段
        if(fixHeaderFieldList != null && fixHeaderFieldList.size() > 0){
            if(messageFieldSize < fixHeaderFieldList.size()){
                return Result.error("协议包字段数量不能小于协议头字段数量");
            }
            for(CustomField fixHeaderField:fixHeaderFieldList){
                CustomFieldEntity messageFieldEntity = messageFieldEntityList.get(idx);
                Result<CustomField> messageFieldResult = fixHeaderField.buildMessageField(messageFieldEntity);
                if(!messageFieldResult.getSignal()){
                    return Result.error(messageFieldResult.getMessage());
                }
                CustomField messageField = messageFieldResult.getResult();
                messageFieldList.add(messageField);
                idx++;
            }
        }

        //校验并构建消息体字段
        if(idx < messageFieldSize){
            Integer m = messageFieldSize - fixTailFieldSize;
            for(int i=idx;i<m;i++){
                CustomFieldEntity messageFieldEntity = messageFieldEntityList.get(i);
                Result<CustomField> messageFieldResult = CustomField.checkMessageFieldInfo(messageFieldEntity);
                if(!messageFieldResult.getSignal()){
                    return Result.error(messageFieldResult.getMessage());
                }
                CustomField messageField = messageFieldResult.getResult();
                messageFieldList.add(messageField);
                idx++;
            }
        }
        
        //校验并构建尾部字段
        if(fixTailFieldList != null && fixTailFieldSize > 0){
            if(messageFieldSize < fixTailFieldSize){
                return Result.error("协议包字段数量不能小于协议尾字段数量");
            }
            for(CustomField fixTailField:fixTailFieldList){
                CustomFieldEntity messageFieldEntity = messageFieldEntityList.get(idx);
                Result<CustomField> messageFieldResult = fixTailField.buildMessageField(messageFieldEntity);
                if(!messageFieldResult.getSignal()){
                    return Result.error(messageFieldResult.getMessage());
                }
                CustomField messageField = messageFieldResult.getResult();
                messageFieldList.add(messageField);
                idx++;
            }
        }

        //检查messageField名不能重复
        Set<String> fieldNameSet = new HashSet<>();
        if(messageFieldList != null && messageFieldList.size() > 0){
            for(CustomField field:messageFieldList){
                if(fieldNameSet.contains(field.getFieldName())){
                    return Result.error("固定头字段重名：" + field.getFieldName());
                }
                fieldNameSet.add(field.getFieldName());
            }
        }

        return Result.ok(messageFieldList);
    }
    /**
     * 校验字节长度字段、数量字段
     * @param messageFieldList
     * @return
     */
    private static Result<Void> checkFieldReference(List<CustomField> messageFieldList, String clientKeyFieldName){
        if(messageFieldList == null || messageFieldList.size() <= 0){
            return Result.ok();
        }
        Set<String> fieldNameSet = messageFieldList.stream().map(CustomField::getFieldName).collect(Collectors.toSet());
        if(clientKeyFieldName != null && !"".equals(clientKeyFieldName)){
            if(!fieldNameSet.contains(clientKeyFieldName)){
                return Result.error("客户端标识字段不在字段列表内，clientKeyFieldName:" + clientKeyFieldName);
            }
        }
        for(CustomField field:messageFieldList){
            DriverFieldTypeEnum fieldType = field.getFieldType();
            if(DriverFieldTypeEnum.BYTE_LENGTH.equals(fieldType)){
                List<String> fieldNameList = field.getByteLengthFieldList();
                if(fieldNameList != null && fieldNameList.size() > 0){
                    for(String fieldName:fieldNameList){
                        if(!fieldNameSet.contains(fieldName)){
                            return Result.error("字节长度字段不在字段列表内，fieldName:" + field.getFieldName() + ", missFieldName:" + fieldName);
                        }
                    }
                }
            }
            else if(DriverFieldTypeEnum.COUNT.equals(fieldType)){
                List<String> fieldNameList = field.getCountFieldList();
                if(fieldNameList != null && fieldNameList.size() > 0){
                    for(String fieldName:fieldNameList){
                        if(!fieldNameSet.contains(fieldName)){
                            return Result.error("数量字段不在字段列表内，fieldName:" + field.getFieldName() + ", missFieldName:" + fieldName);
                        }
                    }
                }
            }
            else if(DriverFieldTypeEnum.CHECK.equals(fieldType)){
                List<String> fieldNameList = field.getCheckFieldList();
                if(fieldNameList != null && fieldNameList.size() > 0){
                    for(String fieldName:fieldNameList){
                        if(!fieldNameSet.contains(fieldName)){
                            return Result.error("校验字段不在字段列表内，fieldName:" + field.getFieldName() + ", missFieldName:" + fieldName);
                        }
                    }
                }
            }
        }

        return Result.ok();
    }
    
    private static Result<Void> checkAutoSend(CustomMessageEntity entity, CustomMessage customMessage){
        customMessage.autoSend = entity.getAutoSend();
        customMessage.autoSendInterval = entity.getAutoSendInterval();
        customMessage.autoSendVarStr = entity.getAutoSendVarStr();
        return Result.ok();
    }

    private static Result<Void> checkAutoResponse(CustomMessageEntity entity, CustomMessage customMessage, CommonFetcher commonFetcher){
        Boolean autoResponse = entity.getAutoResponse();
        if(autoResponse == null){
            autoResponse = false;
        }
        customMessage.autoResponse = autoResponse;
        if(autoResponse){
            String autoResponseMessageName = entity.getAutoResponseMessageName();
            if(autoResponseMessageName == null || "".equals(autoResponseMessageName)){
                return Result.error("自动响应消息名不能为空");
            }
            Long customDriverId = entity.getCustomDriverId();
            Preloader<CustomMessageEntity> preloader = commonFetcher.preloader(
                "custom_driver_id", 
                customDriverId, 
                CustomMessageEntity.class
            ).preload("message_name", autoResponseMessageName);
            CustomMessageEntity autoResponseMessageEntity = preloader.getFirst("message_name", autoResponseMessageName);
            if(autoResponseMessageEntity == null){
                return Result.error("找不到自动响应消息：" + autoResponseMessageName);
            }

            Result<CustomMessage> autoResponseMessageResult = checkInfoWithoutAutoResponse(autoResponseMessageEntity, commonFetcher);
            if(!autoResponseMessageResult.getSignal()){
                return Result.error(autoResponseMessageResult.getMessage());
            }
            customMessage.autoResponseMessageName = autoResponseMessageName;
            AutoResponseConfigField autoResponseConfig = entity.getAutoResponseConfig();
            if(autoResponseConfig != null ){
                List<CopyFieldElm> copyFieldList = autoResponseConfig.getCopyFieldList();
                if(copyFieldList != null){
                    CustomMessage autoResponseMessage = autoResponseMessageResult.getResult();
                    for(CopyFieldElm elm:copyFieldList){
                        String sourceField = elm.getSourceField();
                        if(sourceField == null || "".equals(sourceField)){
                            continue;
                        }
                        {
                            if(customMessage.getMessageFieldList() == null){
                                return Result.error("自动响应消息找不到来源字段：" + sourceField);
                            }
                            Optional<CustomField> fieldOptional = customMessage.getMessageFieldList().stream().filter(t -> {
                                return t.getFieldName().equals(sourceField);
                            }).findAny();
                            if(!fieldOptional.isPresent()){
                                return Result.error("自动响应消息找不到来源字段：" + sourceField);
                            }
                        }

                        String targetField = elm.getTargetField();
                        if(targetField == null || "".equals(targetField)){
                            continue;
                        }
                        {
                            if(autoResponseMessage.getMessageFieldList() == null){
                                return Result.error("自动响应消息找不到目标字段：" + targetField);
                            }
                            Optional<CustomField> fieldOptional = autoResponseMessage.getMessageFieldList().stream().filter(t -> {
                                return t.getFieldName().equals(targetField);
                            }).findAny();
                            if(!fieldOptional.isPresent()){
                                return Result.error("自动响应消息找不到目标字段：" + targetField);
                            }
                        }
                    }
                }
                
            }
            customMessage.autoResponseConfig = autoResponseConfig;
        }
        return Result.ok();
    }
    

    public MessageElm toRuntimeInfo() {
        MessageElm define = new MessageElm();
        define.setMessageName(messageName);
        define.setDirection(direction.getValue());

        define.setAutoResponse(autoResponse);
        define.setAutoResponseMessageName(autoResponseMessageName);
        define.setAutoResponseConfig(autoResponseConfig);

        define.setAutoSend(autoSend);
        define.setAutoSendInterval(autoSendInterval);
        define.setAutoSendVarStr(autoSendVarStr);

        define.setClientKeyFieldName(clientKeyFieldName);

        List<MessageItemElm> list = new ArrayList<>();
        if(messageFieldList != null && messageFieldList.size() > 0){
            for(CustomField field:messageFieldList){
                list.add(field.toRuntimeInfo());
            }
        }
        define.setFieldList(list);

        return define;
    }

    
}
