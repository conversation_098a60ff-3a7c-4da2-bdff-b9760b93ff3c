package com.nti56.nlink.product.device.server.service.impl;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletResponse;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.fetcher.CommonFetcher;
import com.nti56.nlink.common.fetcher.CommonFetcherFactory;
import com.nti56.nlink.common.mybatis.BaseServiceImpl;
import com.nti56.nlink.common.thing.ThingDataTypeEnum;
import com.nti56.nlink.common.util.BeanUtilsIntensifier;
import com.nti56.nlink.common.util.IdGenerator;
import com.nti56.nlink.common.util.R;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.nlink.product.device.client.model.dto.json.ChannelRuntimeInfoField;
import com.nti56.nlink.product.device.client.model.dto.json.DeviceRuntimeMetadataField;
import com.nti56.nlink.product.device.client.model.dto.json.ModelField;
import com.nti56.nlink.product.device.client.model.dto.json.device.AccessElm;
import com.nti56.nlink.product.device.client.model.dto.json.device.PropertyMetadataItem;
import com.nti56.nlink.product.device.client.model.dto.json.modelfield.EventElm;
import com.nti56.nlink.product.device.client.model.dto.json.modelfield.PropertyElm;
import com.nti56.nlink.product.device.client.model.rsp.TagRsp;
import com.nti56.nlink.product.device.server.constant.RedisConstant;
import com.nti56.nlink.product.device.server.domain.thing.device.Device;
import com.nti56.nlink.product.device.server.domain.thing.device.DeviceDataResource;
import com.nti56.nlink.product.device.server.domain.thing.device.DeviceTwin;
import com.nti56.nlink.product.device.server.domain.thing.devicemodel.DeviceModel;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.ChangeSubjectEnum;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.ChangeTypeEnum;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.DeviceOptionEnum;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.DeviceStatusEnum;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.EdgeGatewayTypeEnum;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.ModelTypeEnum;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.ResourceTypeEnum;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.StatusEnum;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.SyncStatusEnum;
import com.nti56.nlink.product.device.server.domain.thing.label.LabelBindRelation;
import com.nti56.nlink.product.device.server.domain.thing.modelbase.Property;
import com.nti56.nlink.product.device.server.domain.thing.thingmodel.ThingModel;
import com.nti56.nlink.product.device.server.domain.thing.thingmodel.ThingModelInherit;
import com.nti56.nlink.product.device.server.domain.thing.up.UpData;
import com.nti56.nlink.product.device.server.domain.thing.up.UpProp;
import com.nti56.nlink.product.device.server.entity.AbsolutePathLabelEntity;
import com.nti56.nlink.product.device.server.entity.ChangeNoticeEntity;
import com.nti56.nlink.product.device.server.entity.ChannelEntity;
import com.nti56.nlink.product.device.server.entity.DeviceEntity;
import com.nti56.nlink.product.device.server.entity.DeviceModelInheritEntity;
import com.nti56.nlink.product.device.server.entity.DeviceServiceEntity;
import com.nti56.nlink.product.device.server.entity.DeviceServiceLogEntity;
import com.nti56.nlink.product.device.server.entity.EdgeGatewayEntity;
import com.nti56.nlink.product.device.server.entity.LabelBindRelationEntity;
import com.nti56.nlink.product.device.server.entity.LabelEntity;
import com.nti56.nlink.product.device.server.entity.LabelGroupEntity;
import com.nti56.nlink.product.device.server.entity.ResourceRelationEntity;
import com.nti56.nlink.product.device.server.entity.SubscriptionEntity;
import com.nti56.nlink.product.device.server.entity.TagBindRelationEntity;
import com.nti56.nlink.product.device.server.entity.ThingModelEntity;
import com.nti56.nlink.product.device.server.entity.ThingModelInheritEntity;
import com.nti56.nlink.product.device.server.entity.ThingServiceEntity;
import com.nti56.nlink.product.device.server.entity.json.InputValueField;
import com.nti56.nlink.product.device.server.exception.BizException;
import com.nti56.nlink.product.device.server.feign.IEdgeGatewayCacheProxy;
import com.nti56.nlink.product.device.server.feign.IEdgeGatewaySpiProxy;
import com.nti56.nlink.product.device.server.mapper.AbsolutePathLabelMapper;
import com.nti56.nlink.product.device.server.mapper.DeviceMapper;
import com.nti56.nlink.product.device.server.mapper.DeviceModelInheritMapper;
import com.nti56.nlink.product.device.server.mapper.DeviceModelMapper;
import com.nti56.nlink.product.device.server.mapper.DeviceServiceLogMapper;
import com.nti56.nlink.product.device.server.mapper.EdgeGatewayMapper;
import com.nti56.nlink.product.device.server.mapper.LabelBindRelationMapper;
import com.nti56.nlink.product.device.server.mapper.LabelMapper;
import com.nti56.nlink.product.device.server.mapper.ResourceRelationMapper;
import com.nti56.nlink.product.device.server.model.BatchDeviceTagDto;
import com.nti56.nlink.product.device.server.model.BatchLabelCreateDeviceParam;
import com.nti56.nlink.product.device.server.model.ConnectResult;
import com.nti56.nlink.product.device.server.model.DeviceBo;
import com.nti56.nlink.product.device.server.model.DeviceChannelBo;
import com.nti56.nlink.product.device.server.model.DeviceDto;
import com.nti56.nlink.product.device.server.model.DeviceExportDto;
import com.nti56.nlink.product.device.server.model.DeviceNamesToIdsDto;
import com.nti56.nlink.product.device.server.model.DeviceRespondBo;
import com.nti56.nlink.product.device.server.model.DeviceTwinRequestBo;
import com.nti56.nlink.product.device.server.model.EventVo;
import com.nti56.nlink.product.device.server.model.GateWayDeviceDTO;
import com.nti56.nlink.product.device.server.model.GateWayDeviceListDTO;
import com.nti56.nlink.product.device.server.model.WritePropertyValueDTO;
import com.nti56.nlink.product.device.server.model.changeNotice.SendMessage;
import com.nti56.nlink.product.device.server.model.device.dto.CreateByAssembleInfoDTO;
import com.nti56.nlink.product.device.server.model.device.dto.DeviceCheckInfoContext;
import com.nti56.nlink.product.device.server.model.device.dto.DeviceFaultInstance;
import com.nti56.nlink.product.device.server.model.device.dto.DeviceFaultStatistic;
import com.nti56.nlink.product.device.server.model.device.dto.FaultEventInstance;
import com.nti56.nlink.product.device.server.model.device.vo.AssembleByLabelIdGroupIdVO;
import com.nti56.nlink.product.device.server.model.device.vo.AssembleByLabelIdGroupIdsVO;
import com.nti56.nlink.product.device.server.model.device.vo.DcmpDevicePropertyVO;
import com.nti56.nlink.product.device.server.model.device.vo.DcmpDeviceVO;
import com.nti56.nlink.product.device.server.model.device.vo.DeviceOnlineStatusVO;
import com.nti56.nlink.product.device.server.model.deviceModel.DeviceModelBo;
import com.nti56.nlink.product.device.server.model.deviceModel.dto.AssembleByLabelGroupIdsDTO;
import com.nti56.nlink.product.device.server.model.label.vo.PropertiesBindLabelVO;
import com.nti56.nlink.product.device.server.model.redirect.RedirectReferenceDto;
import com.nti56.nlink.product.device.server.model.thingModel.vo.ThingModelVO;
import com.nti56.nlink.product.device.server.openapi.domain.request.DoServiceTaskRequest;
import com.nti56.nlink.product.device.server.openapi.domain.request.GetDeviceRequest;
import com.nti56.nlink.product.device.server.openapi.domain.request.ListDcmpDeviceRequest;
import com.nti56.nlink.product.device.server.openapi.domain.request.ListDevicesRequest;
import com.nti56.nlink.product.device.server.openapi.domain.request.ListTagRequest;
import com.nti56.nlink.product.device.server.scriptApi.Engineering;
import com.nti56.nlink.product.device.server.scriptApi.EngineeringFactory;
import com.nti56.nlink.product.device.server.scriptApi.SpiUtil;
import com.nti56.nlink.product.device.server.scriptApi.Thing;
import com.nti56.nlink.product.device.server.service.IChangeNoticeService;
import com.nti56.nlink.product.device.server.service.IChannelService;
import com.nti56.nlink.product.device.server.service.IDeviceLogService;
import com.nti56.nlink.product.device.server.service.IDeviceModelInheritService;
import com.nti56.nlink.product.device.server.service.IDeviceService;
import com.nti56.nlink.product.device.server.service.IDeviceServiceService;
import com.nti56.nlink.product.device.server.service.IDeviceStatusManagementService;
import com.nti56.nlink.product.device.server.service.IEdgeGatewayService;
import com.nti56.nlink.product.device.server.service.IInstanceRedirectService;
import com.nti56.nlink.product.device.server.service.ILabelBindRelationService;
import com.nti56.nlink.product.device.server.service.ILabelGroupService;
import com.nti56.nlink.product.device.server.service.ILabelService;
import com.nti56.nlink.product.device.server.service.ISubscriptionService;
import com.nti56.nlink.product.device.server.service.ITagBindRelationService;
import com.nti56.nlink.product.device.server.service.ITagService;
import com.nti56.nlink.product.device.server.service.IThingModelService;
import com.nti56.nlink.product.device.server.service.IThingResourceService;
import com.nti56.nlink.product.device.server.service.IThingServiceService;
import com.nti56.nlink.product.device.server.service.cache.redis2Mem.MemoryCache;
import com.nti56.nlink.product.device.server.util.RegexUtil;
import com.nti56.nlink.product.device.server.util.redis.RedisUtil;
import com.nti56.nlink.product.device.server.verticle.post.processor.label.Mapping2DeviceHandler;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 类说明: 设备服务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 14:10:11
 * @since JDK 1.8
 */
@Service
@Slf4j
public class DeviceServiceImpl extends BaseServiceImpl<DeviceMapper, DeviceEntity> implements IDeviceService {

    @Autowired @Lazy
    DeviceMapper deviceMapper;

    @Autowired @Lazy
    DeviceModelMapper deviceModelMapper;

    @Autowired @Lazy
    EdgeGatewayMapper edgeGatewayMapper;

    @Autowired @Lazy
    ITagBindRelationService tagBindRelationService;

    @Autowired @Lazy
    ILabelBindRelationService labelBindRelationService;

    @Autowired @Lazy
    ResourceRelationMapper resourceRelationMapper;

    @Autowired @Lazy
    DeviceModelInheritMapper deviceModelInheritMapper;

    @Autowired @Lazy
    LabelMapper labelMapper;

    @Autowired @Lazy
    IDeviceModelInheritService deviceModelInheritService;

    @Autowired
    @Lazy
    IDeviceServiceService deviceServiceService;

    @Autowired @Lazy
    LabelBindRelationMapper labelBindRelationMapper;

    @Autowired @Lazy
    CommonFetcherFactory commonFetcherFactory;

    @Autowired @Lazy
    StringRedisTemplate stringRedisTemplate;

    @Autowired @Lazy
    IThingResourceService thingResourceService;

    @Autowired @Lazy
    DeviceServiceLogMapper deviceServiceLogMapper;

    @Autowired @Lazy
    private ILabelService labelService;

    @Autowired @Lazy
    private ILabelGroupService labelGroupService;

    @Lazy
    @Autowired
    private IChannelService channelService;

    @Autowired @Lazy
    private RedisTemplate<String, Object> redisTemplate;

    //@Autowired @Lazy
    //private IModelGraphService modelGraphService;

    @Autowired @Lazy
    private IEdgeGatewayService edgeGatewayService;

    @Autowired @Lazy
    private IThingModelService thingModelService;

    @Autowired @Lazy
    private IChangeNoticeService changeNoticeService;

    @Autowired
    @Lazy
    private ISubscriptionService subscriptionService;

    @Autowired
    private IInstanceRedirectService instanceRedirectService;

    //todo 删掉
    @Lazy
    @Autowired
    Mapping2DeviceHandler mapping2DeviceHandler;

    @Autowired @Lazy
    private IEdgeGatewayCacheProxy edgeGatewayCacheProxy;

    @Autowired
    private EngineeringFactory engineeringFactory;

    @Autowired @Lazy
    private IEdgeGatewaySpiProxy edgeGatewaySpiProxy;
    //@Autowired @Lazy
    //private Neo4jDeviceRepository neo4jDeviceRepository;
    @Autowired @Lazy
    private IThingServiceService thingServiceService;

    @Autowired @Lazy
    private IDeviceLogService deviceLogService;

    @Autowired @Lazy
    private ITagService tagService;


    @Transactional
    @Override
    public Result<DeviceDto> createDevice(DeviceDto device) {
        checkParam(device);
        this.uniqueName(device.getName(), device.getTenantId());
        String resourceId = this.uniqueResourceId(device.getResourceId(), device.getTenantId());
        device.setStatus(StatusEnum.INACTIVATED.getValue());
        device.setResourceId(resourceId);
        if (deviceMapper.insert(device) == 1) {
            tagBindRelationService.saveList(device.getTenantId(), device.getId(),
                    BeanUtilsIntensifier.getIds(device.getTags(), TagRsp::getId), ResourceTypeEnum.DEVICE);
            deviceModelInheritService.resetInheritRelation(device);
            ResourceRelationEntity build = ResourceRelationEntity.builder().deviceId(device.getId()).engineeringId(device.getEngineeringId()).spaceId(device.getSpaceId())
                    .moduleId(device.getModuleId()).tenantId(device.getTenantId()).build();
            resourceRelationMapper.insert(build);

            CommonFetcher commonFetcher = commonFetcherFactory.buildCommonFetcher(device.getTenantId());
            Result<DeviceModel> deviceModelResult = DeviceModel.checkInfo(
                    device,
                    commonFetcher
            );
            if (!deviceModelResult.getSignal()) {
                throw new BizException(deviceModelResult.getMessage());
            }
           
            changeNotice(device.getTenantId(),ChangeTypeEnum.ADD,device,deviceModelResult.getResult().getInherit().getThingModelList().get(0).getName());
            return Result.ok(device);
        } else {
            throw new BizException(ServiceCodeEnum.CODE_CREATE_FAIL);
        }

    }

    private String uniqueResourceId(String resourceId, Long tenantId) {
        //判断是否有设置值，没有设置标识则系统生成
        return this.uniqueResourceId(null,resourceId,tenantId);
    }

    private String uniqueResourceId(Long id, String resourceId, Long tenantId) {
        //判断是否有设置值，没有设置标识则系统生成
        String identify = resourceId;

        if(StrUtil.isBlank(identify)){
            identify = IdUtil.getSnowflake().nextIdStr();
        }else{
            if(!RegexUtil.checkName2(identify)){
                throw new BizException("设备标识只支持英文字母、数字和下划线");
            }
            if(identify.length()>256){
                throw new BizException("设备标识不能超过256字符");

            }
        }
        LambdaQueryWrapper<DeviceEntity> lqw = new LambdaQueryWrapper<>();
        lqw
                .ne(id != null, DeviceEntity::getId, id)
                .eq(DeviceEntity::getResourceId, identify)
                .eq(DeviceEntity::getTenantId, tenantId);
        if (deviceMapper.selectCount(lqw) > 0) {
            throw new BizException("已经存在该设备标识,标识：" + resourceId);
        }
        return identify;
    }


    private <T extends DeviceEntity> void changeNotice(Long tenantId, ChangeTypeEnum changeTypeEnum, T device,String thingModelName) {
        // device.setRuntimeMetadata(null);
        device.setModel(null);
        DeviceDto deviceDto = BeanUtilsIntensifier.copyBean(device, DeviceDto.class);
        deviceDto.setFastThingModelName(thingModelName);
        changeNoticeService.changeNotice(tenantId,deviceDto,changeTypeEnum,ChangeSubjectEnum.DEVICE);
    }

    @Override
    public Result<Page<DeviceDto>> getDevicePage(DeviceDto device, Page<DeviceDto> page) {
        if (device.getStatus() != null && DeviceStatusEnum.ONLINE.getValue() == device.getStatus() || DeviceStatusEnum.OFFLINE.getValue() == device.getStatus()) {
            List<Long> ids = getPagedDeviceIds(device);
            device.setIdList(ids);
        }
        QueryWrapper<DeviceDto> wrapper = buildDevicePageCondition(DeviceDto.class, device, "d.");
        Page<DeviceDto> list = deviceMapper.pageDevice(page, wrapper);
        List<DeviceDto> deviceDtos = Optional.ofNullable(list.getRecords()).orElse(new ArrayList<>());
        List<Long> ids = BeanUtilsIntensifier.getIds(deviceDtos, DeviceDto::getId);
        Map<Long, List<TagRsp>> tagMap = tagBindRelationService.getTagListGroupByTargetId(device.getTenantId(), ResourceTypeEnum.DEVICE, ids);
        deviceDtos.forEach(deviceDto -> {
            deviceDto.setTags(tagMap.get(deviceDto.getId()));
            if(DeviceStatusEnum.ONLINE.getValue().equals(deviceDto.getStatus())){
                //内存设备状态 页面会调用onlineStatus接口获取在线状态，这里不获取
//                Map<Object, Object> deviceStatusMap = getDeviceStatusMap(deviceDto.getTenantId(),deviceDto.getEdgeGatewayId());
//                if(deviceStatusMap.get(String.valueOf(deviceDto.getId()))!=null && String.valueOf(DeviceStatusEnum.ONLINE.getValue()).equals(deviceStatusMap.get(String.valueOf(deviceDto.getId())))){
                    deviceDto.setStatus(DeviceStatusEnum.ONLINE.getValue());
//                }else {
//                    deviceDto.setStatus(DeviceStatusEnum.OFFLINE.getValue());
//                }
            }
        });
        return Result.ok(list);
    }

    public void exportDevice(DeviceDto device, HttpServletResponse response) {
        if (device.getStatus() != null && DeviceStatusEnum.ONLINE.getValue() == device.getStatus() || DeviceStatusEnum.OFFLINE.getValue() == device.getStatus()) {
            List<Long> ids = getPagedDeviceIds(device);
            device.setIdList(ids);
        }
        QueryWrapper<DeviceDto> wrapper = buildDevicePageCondition(DeviceDto.class, device, "d.");
        List<DeviceDto> deviceDtos = deviceMapper.listExportDevice(wrapper);
        List<Long> ids = BeanUtilsIntensifier.getIds(deviceDtos, DeviceDto::getId);
        List<DeviceExportDto> exportDtoList = new ArrayList<>();
        Map<Long, List<TagRsp>> tagMap = tagBindRelationService.getTagListGroupByTargetId(device.getTenantId(), ResourceTypeEnum.DEVICE, ids);
        deviceDtos.forEach(deviceDto -> {
            DeviceExportDto exportDto = new DeviceExportDto();
            String tagName = null;
            List<TagRsp> tagList = tagMap.getOrDefault(deviceDto.getId(), new ArrayList<>());
            for (TagRsp tagRsp : tagList) {
                String tagTemp = tagRsp.getTagKey() + ":" + tagRsp.getTagValue();
                tagName = tagName == null ? tagTemp : tagName + "," +tagTemp;
            }
            exportDto.setName(deviceDto.getName());
            exportDto.setEdgeGatewayName(deviceDto.getEdgeGatewayName());
            exportDto.setTagName(tagName);
            exportDtoList.add(exportDto);
        });
        this.export(response,"设备导出.xlsx",exportDtoList);
    }
    private void export(HttpServletResponse response, String fileName, List<DeviceExportDto> templateDtoList) {
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontName("Calibri");
        headWriteFont.setColor(IndexedColors.WHITE.getIndex());
        headWriteFont.setBold(true);
        headWriteFont.setFontHeightInPoints((short) 10);
        headWriteCellStyle.setWriteFont(headWriteFont);
        HorizontalCellStyleStrategy horizontalCellStyleStrategy =
                new HorizontalCellStyleStrategy(headWriteCellStyle, new ArrayList<>());
        ExcelWriterBuilder excelWriter = null;
        ExcelWriter build = null;
        try {
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            response.setContentType("application/octet-stream; charset=utf-8");
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-Disposition", "attachment; filename=" + fileName);
            excelWriter = EasyExcel.write(response.getOutputStream(), DeviceExportDto.class);
            excelWriter
                    .sheet("Sheet1")
                    .registerWriteHandler(horizontalCellStyleStrategy)
                    .doWrite(templateDtoList);
            build = excelWriter.build();
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (build != null) {
                build.finish();
            }
        }
    }
    private List<Long> getPagedDeviceIds(DeviceDto device) {
        String key = RedisConstant.DEVICE_STATUS + device.getTenantId();
        Map<Object, Object> dataMap = stringRedisTemplate.opsForHash().entries(key);
        if(CollectionUtil.isEmpty(dataMap)){
            return new ArrayList<>();
        }
        List<Long> deviceIdIds = new ArrayList<>();
        String status = String.valueOf(device.getStatus());
        for (Map.Entry<Object, Object> entry : dataMap.entrySet()) {
            if(status.equals(String.valueOf(entry.getValue()))){
                deviceIdIds.add(Long.parseLong(String.valueOf(entry.getKey())));
            }
        }
        if(CollectionUtil.isEmpty(deviceIdIds)){
            deviceIdIds.add(-1L);
        }
        return deviceIdIds;
    }

    @Override
    public Result<List<DeviceEntity>> listDevice(DeviceEntity device) {
        LambdaQueryChainWrapper<DeviceEntity> wrapper = new LambdaQueryChainWrapper<>(deviceMapper);
        wrapper.eq(DeviceEntity::getTenantId, device.getTenantId());
        if (Optional.ofNullable(device).isPresent() && Optional.ofNullable(device.getEdgeGatewayId()).isPresent()) {
            wrapper.eq(DeviceEntity::getEdgeGatewayId, device.getEdgeGatewayId());
        }
        wrapper.select(DeviceEntity.class, info -> !info.getColumn().equals("runtime_metadata"));
        List<DeviceEntity> list = wrapper.list();
        list.forEach(deviceEntity->{
            if(DeviceStatusEnum.ONLINE.getValue().equals(deviceEntity.getStatus())){
                //内存设备状态
                Map<Object, Object> deviceStatusMap = getDeviceStatusMap(deviceEntity.getTenantId(),deviceEntity.getEdgeGatewayId());
                if(deviceStatusMap.get(String.valueOf(deviceEntity.getId()))!=null && String.valueOf(DeviceStatusEnum.ONLINE.getValue()).equals(deviceStatusMap.get(String.valueOf(deviceEntity.getId())))){
                    deviceEntity.setStatus(DeviceStatusEnum.ONLINE.getValue());
                }else {
                    deviceEntity.setStatus(DeviceStatusEnum.OFFLINE.getValue());
                }
            }
        });
        return Result.ok(list);
    }

    @Override
    public Result<List<DeviceEntity>> listDeviceByName(TenantIsolation tenantIsolation, ListDevicesRequest request) {
        LambdaQueryChainWrapper<DeviceEntity> wrapper = new LambdaQueryChainWrapper<>(deviceMapper);
        List<DeviceEntity> list = wrapper.eq(DeviceEntity::getTenantId, tenantIsolation.getTenantId())
                .eq(DeviceEntity::getDeleted , 0)
                .in(CollectionUtil.isNotEmpty(request.getNameList()), DeviceEntity::getName, request.getNameList())
                .list();
        return Result.ok(list);
    }

    @Transactional
    @Override
    public Result<Void> updateDevice(DeviceDto device) {
        checkParam(device);
        this.uniqueName(device.getId(), device.getName(), device.getTenantId());
        String resourceId = this.uniqueResourceId(device.getId(), device.getResourceId(), device.getTenantId());
        device.setResourceId(resourceId);
        DeviceEntity deviceEntity = deviceMapper.selectById(device.getId());
        if (!deviceEntity.getEdgeGatewayId().equals(device.getEdgeGatewayId())) {
            throw new BizException(ServiceCodeEnum.DEVICE_EDGE_GATEWAY_UPDATE_ERROR);
        }
        if (deviceMapper.updateById(device) == 1) {
            tagBindRelationService.saveList(device.getTenantId(), device.getId(),
                    BeanUtilsIntensifier.getIds(device.getTags(), TagRsp::getId), ResourceTypeEnum.DEVICE);
            deviceModelInheritService.resetInheritRelation(device);

            CommonFetcher commonFetcher = commonFetcherFactory.buildCommonFetcher(device.getTenantId());
            Result<Device> deviceModelResult = Device.checkInfoToModel(
                    device,
                    commonFetcher
            );
            if (!deviceModelResult.getSignal()) {
                throw new BizException(deviceModelResult.getMessage());
            }
            //更新设备图节点
          /*  Result<Void> result = modelGraphService.updateDeviceNode(device, device.getThingModelIds());
            if(!result.getSignal()){
                throw new BizException(result.getMessage());
            }*/
            device.setBeforeUpdateName(deviceEntity.getName());
            changeNotice(device.getTenantId(),ChangeTypeEnum.UPDATE,device,deviceModelResult.getResult().getDeviceModel().getInherit().getThingModelList().get(0).getName());
            return Result.ok();
        } else {
            throw new BizException(ServiceCodeEnum.CODE_UPDATE_FAIL);
        }

    }

    @Override
    public Result<Void> deleteDeviceById(TenantIsolation tenantIsolation, Long deviceId) {
        DeviceEntity entity = deviceMapper.selectById(deviceId);
        if (!entity.getTenantId().equals(tenantIsolation.getTenantId())) {
            throw new BizException(ServiceCodeEnum.TENANT_ERROR);
        }
        return deleteDevice(tenantIsolation, entity);
    }

    @Override
    @Transactional
    public Result<Void> deleteDevice(TenantIsolation tenantIsolation, DeviceEntity entity) {
        if (entity.getStatus().equals(StatusEnum.ONLINE.getValue())) {
            throw new BizException(ServiceCodeEnum.DEVICE_ONLINE);
        }
        if (deviceMapper.deleteById(entity.getId()) == 1) {
            tagBindRelationService.saveList(tenantIsolation.getTenantId(), entity.getId(), null, ResourceTypeEnum.DEVICE);
            labelBindRelationService.saveList(tenantIsolation.getTenantId(), entity.getId(), null);
            deviceModelInheritService.resetInheritRelation(tenantIsolation, entity, null);
            deviceServiceService.deleteByDeviceId(tenantIsolation, entity.getId());
            resourceRelationMapper.deleteByDeviceId(tenantIsolation.getTenantId(), entity.getId());
            RedisUtil redisUtil = new RedisUtil(redisTemplate, null);

            MemoryCache.deleteSubscriptionListByDeviceId(entity.getId());
            // Set<String> subscriptionKeys = RedisSubscriptionIndexUtil.getDeviceSubscriptionKeys(redisTemplate, entity.getId());

            // if(!CollectionUtils.isEmpty(subscriptionKeys)){
            //     redisTemplate.delete(subscriptionKeys);
            // }
            //删除设备故障
            /*Result<Void> result = modelGraphService.deleteDeviceNode(entity.getId());
            if(!result.getSignal()){
                throw new BizException(result.getMessage());
            }*/
            String tenantOnFaultDeviceKey = String.format(RedisConstant.TENANT_DEVICE_ON_FAULT,tenantIsolation.getTenantId());
            redisTemplate.opsForSet().remove(tenantOnFaultDeviceKey,entity.getId());
            //回调调用详情
            String faultInstanceKey = String.format(RedisConstant.DEVICE_FAULT_INSTANCE_CACHE,tenantIsolation.getTenantId(),entity.getId());
            String faultEventInstanceKey = String.format(RedisConstant.DEVICE_FAULT_EVENT_INSTANCE,tenantIsolation.getTenantId(), entity.getId());
            redisTemplate.delete(faultEventInstanceKey);
            redisTemplate.delete(faultInstanceKey);

            QueryWrapper<SubscriptionEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("directly_model_id",entity.getId());
            List<SubscriptionEntity> subscriptionEntities = subscriptionService.list(queryWrapper);
            RedirectReferenceDto delRef = RedirectReferenceDto.builder()
                    .updateType(0)
                    .build();
            if(CollectionUtil.isNotEmpty(subscriptionEntities)){
                for (SubscriptionEntity subscriptionEntity : subscriptionEntities) {
                    delRef.setRefId(subscriptionEntity.getDirectlyModelId());
                    delRef.setRedirectId(subscriptionEntity.getCallbackId());
                    instanceRedirectService.updateReference(tenantIsolation, delRef);
                }
            }
            changeNotice(tenantIsolation.getTenantId(),ChangeTypeEnum.DELETE,DeviceEntity.builder().id(entity.getId()).name(entity.getName()).build(),null);
            return Result.ok();
        } else {
            throw new BizException(ServiceCodeEnum.CODE_DELETE_FAIL);
        }
    }

    @Override
    public Result<DeviceDto> getDevice(DeviceEntity entity) {
        log.info("获取设备：{}，tenant:{}",entity.getId(),entity.getTenantId());
        QueryWrapper<DeviceEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("d.id", entity.getId());
        wrapper.eq("d.tenant_id", entity.getTenantId());
        DeviceDto device = deviceMapper.getDevice(wrapper);
        if (Objects.isNull(device)) {
            return Result.error("设备不存在！");
        }
        List<ThingModelVO> thingModelVOS = deviceModelInheritService.listByDeviceId(entity.getId());
        device.setThingModelIds(BeanUtilsIntensifier.getIds(thingModelVOS, ThingModelVO::getId));
        device.setThingModels(thingModelVOS);
        List<LabelBindRelationEntity> list = new LambdaQueryChainWrapper<LabelBindRelationEntity>(labelBindRelationMapper)
                .eq(LabelBindRelationEntity::getTenantId, entity.getTenantId())
                .eq(LabelBindRelationEntity::getDeviceId, entity.getId())
                .list();
        Set<Long> idSet = list.stream().map(LabelBindRelationEntity::getDirectlyModelId).collect(Collectors.toSet());
        thingModelVOS.forEach(thingModelVO -> {
            if (idSet.contains(thingModelVO.getId())) {
                thingModelVO.setIsBind(true);
            } else {
                thingModelVO.setIsBind(false);
            }
        });
        Result<List<TagBindRelationEntity>> relationList = tagBindRelationService.listByTargetId(entity.getTenantId(), device.getId());
        List<String> tagStrings = BeanUtilsIntensifier.getIds2String(relationList.getResult(), TagBindRelationEntity::getTagId);
        device.setTagIds(tagStrings);
        List<TagRsp> tagRspList = tagBindRelationService.getTags(device.getTenantId(), ResourceTypeEnum.DEVICE, device.getId());
        device.setTags(tagRspList);
        return Result.ok(device);
    }

    @Override
    public Result<List<DeviceBo>> listDeviceByTag(DeviceDto dto) {
        List<String> tagIds = dto.getTagIds();
        List<DeviceBo> deviceBos;
        if (!Optional.ofNullable(tagIds).isPresent() || tagIds.size() == 0) {
            deviceBos = deviceMapper.allList(dto.getTenantId());
            setDeviceRunStatus(deviceBos);
            setDeviceThingModelId(dto.getTenantId(), deviceBos);
            return Result.ok(deviceBos);
        }
        Result<List<TagBindRelationEntity>> listResult = tagBindRelationService.listByTagIds(dto.getTenantId(), tagIds, ResourceTypeEnum.DEVICE);
        if (listResult.getSignal() && listResult.getResult().size() > 0) {
            deviceBos = deviceMapper.listByIds(BeanUtilsIntensifier.getIds(listResult.getResult(), TagBindRelationEntity::getTargetId), dto.getTenantId());
            setDeviceRunStatus(deviceBos);
            setDeviceThingModelId(dto.getTenantId(), deviceBos);
            return Result.ok(deviceBos);
        }
        return Result.ok();
    }

    private void setDeviceThingModelId(Long tenantId, List<DeviceBo> deviceBos){
        if(deviceBos == null || deviceBos.size() <= 0){
            return;
        }
        List<Long> deviceIds = BeanUtilsIntensifier.getIds(deviceBos,DeviceBo::getId);
        List<DeviceModelInheritEntity> inheritThingModels = deviceModelInheritMapper.listInheritThingModelsByIds(tenantId, deviceIds);
        if(inheritThingModels == null || inheritThingModels.size() <= 0){
            return;
        }
        Map<Long, Long> m = new HashMap<>();
        for(DeviceModelInheritEntity entity:inheritThingModels){
            m.put(entity.getDeviceId(), entity.getInheritThingModelId());
        }
        for(DeviceBo device:deviceBos){
            device.setThingModelId(m.get(device.getId()));
        }
    }

    private void setDeviceRunStatus(List<DeviceBo> deviceBos){
        deviceBos.forEach(deviceBo -> {
            if(DeviceStatusEnum.ONLINE.getValue().equals(deviceBo.getStatus())){
                //内存设备状态
                Map<Object, Object> deviceStatusMap = getDeviceStatusMap(deviceBo.getTenantId(),deviceBo.getEdgeGatewayId());
                if(deviceStatusMap.get(String.valueOf(deviceBo.getId()))!=null && String.valueOf(DeviceStatusEnum.ONLINE.getValue()).equals(deviceStatusMap.get(String.valueOf(deviceBo.getId())))){
                    deviceBo.setStatus(DeviceStatusEnum.ONLINE.getValue());
                }else {
                    deviceBo.setStatus(DeviceStatusEnum.OFFLINE.getValue());
                }
            }
        });
    }

    @Override
    public Result<DeviceModelBo> getDeviceProperties(Long tenantId, Long deviceId) {
        DeviceEntity deviceEntity = deviceModelMapper.getByDeviceId(tenantId, deviceId);
        if (!Optional.ofNullable(deviceEntity).isPresent()) {
            return Result.error(ServiceCodeEnum.CODE_GET_FAIL);
        }
        CommonFetcher commonFetcher = commonFetcherFactory.buildCommonFetcher(tenantId);
        Result<DeviceModel> modelResult = DeviceModel.checkInfo(
                deviceEntity,
                commonFetcher
        );
        if (modelResult.getSignal()) {
            return Result.ok(modelResult.getResult().toDeviceModelBo());
        }
        return Result.error(modelResult.getServiceCode(), modelResult.getMessage());
    }

    private <D> QueryWrapper<D> buildDevicePageCondition(Class<D> dClass, DeviceDto device, String tableAlias) {
        Result<List<TagBindRelationEntity>> listResult = Result.error();
        if (Optional.ofNullable(device.getTagIds()).isPresent() && device.getTagIds().size() > 0) {
            listResult = tagBindRelationService.listByTagIds(device.getTenantId(), device.getTagIds(), ResourceTypeEnum.DEVICE);
        }
        QueryWrapper<D> wrapper = new QueryWrapper<>();
        wrapper.eq(tableAlias + "tenant_id", device.getTenantId());
        wrapper.eq(Optional.ofNullable(device.getId()).isPresent(), tableAlias + "id", device.getId());
        if(DeviceStatusEnum.INACTIVATED.getValue() == device.getStatus() || DeviceStatusEnum.DEACTIVATED.getValue() == device.getStatus()){
            wrapper.eq(Optional.ofNullable(device.getStatus()).isPresent(), tableAlias + "status", device.getStatus());
        }else {
            wrapper.eq(Optional.ofNullable(device.getStatus()).isPresent(), tableAlias + "status", DeviceStatusEnum.ONLINE.getValue());
        }
        wrapper.like(Optional.ofNullable(device.getName()).isPresent(), tableAlias + "name", device.getName());
        wrapper.eq(Optional.ofNullable(device.getEdgeGatewayId()).isPresent(), tableAlias + "edge_gateway_id", device.getEdgeGatewayId());
        if(StrUtil.isNotBlank(device.getResourceId())){
            wrapper.eq(tableAlias + "resource_id", device.getResourceId());
        }
        wrapper.in(listResult.getSignal() && listResult.getResult().size() > 0, tableAlias + "id", BeanUtilsIntensifier.getIds(listResult.getResult(), TagBindRelationEntity::getTargetId));
        if(CollectionUtil.isNotEmpty(device.getIdList())){
            wrapper.in(device.getIdList().size() > 0, tableAlias + "id", device.getIdList());
        }
        wrapper.orderByDesc("d.update_time");
        return wrapper;
    }

    @Override
    public Result<Void> setNotSyncById(Long id) {
        LambdaUpdateWrapper<DeviceEntity> luw = new LambdaUpdateWrapper<>();
        luw.set(DeviceEntity::getSyncStatus, SyncStatusEnum.NOT_SYNC.getValue())
                .eq(DeviceEntity::getId, id);
        this.update(luw);
        return Result.ok();
    }

    @Override
    public DeviceEntity getDeviceByName(Long tenantId, String name) {
        DeviceEntity build = DeviceEntity.builder().name(name).build();
        build.setTenantId(tenantId);
        return deviceMapper.selectOne(new QueryWrapper<>(build));
    }

    @Override
    public Result<Integer> countByEdgeGatewayId(Long edgeGatewayId) {
        LambdaQueryWrapper<DeviceEntity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(DeviceEntity::getEdgeGatewayId, edgeGatewayId);
        return Result.ok(deviceMapper.selectCount(lqw));
    }

    @Override
    public Result<List<EventVo>> getDeviceEventProperties(Long tenantId, List<EventVo> eventVos) {
        Map<Long, DeviceModelBo> devices = new HashMap<>();
        if (Optional.ofNullable(eventVos).isPresent() && eventVos.size() > 0) {
            Iterator<EventVo> iterator = eventVos.iterator();
            while (iterator.hasNext()) {
                EventVo eventVo = iterator.next();
                if (!devices.containsKey(eventVo.getDeviceId())) {
                    Result<DeviceModelBo> modelBoResult = getDeviceProperties(tenantId, eventVo.getDeviceId());
                    if (!modelBoResult.getSignal()) {
                        return Result.error(modelBoResult.getServiceCode(), modelBoResult.getMessage());
                    }
                    devices.put(eventVo.getDeviceId(), modelBoResult.getResult());
                }
                List<EventElm> eventElms = devices.get(eventVo.getDeviceId()).getEvents();
                if (Optional.ofNullable(eventElms).isPresent() && eventElms.size() > 0) {
                    for (EventElm eventElm : eventElms) {
                        if (!eventElm.getName().equals(eventVo.getEventName())) {
                            continue;
                        }
                        List<String> properties = new ArrayList<>();
                        List<String> reportProperties = eventElm.getEventDefine().getProperties();
                        if (Optional.ofNullable(reportProperties).isPresent() && reportProperties.size() > 0) {
                            properties.addAll(reportProperties);
                        }
                        String property = eventElm.getEventDefine().getProperty();
                        if (!StringUtils.isEmpty(property)) {
                            properties.add(property);
                        }
                        eventVo.setProperties(properties);
                        break;
                    }
                }
            }
        }
        return Result.ok(eventVos);
    }


    @Override
    public List<DeviceEntity> getDeviceRuntimeList(Long tenantId) {
        if (!Optional.ofNullable(tenantId).isPresent()) {
            return null;
        }
        List<Integer> status = Arrays.asList(DeviceStatusEnum.ONLINE.getValue(), DeviceStatusEnum.OFFLINE.getValue(), DeviceStatusEnum.DEACTIVATED.getValue());
        return new LambdaQueryChainWrapper<>(deviceMapper)
                .eq(DeviceEntity::getTenantId, tenantId)
                .in(DeviceEntity::getStatus, status)
                // .isNotNull(DeviceEntity::getRuntimeMetadata)
                .list();
        //TODO:缓存后续处理
    }

    private <T extends DeviceEntity> void checkParam(T device) {
        EdgeGatewayEntity edgeGatewayEntity = edgeGatewayMapper.selectById(device.getEdgeGatewayId());
        if (!Optional.ofNullable(edgeGatewayEntity).isPresent()) {
            throw new BizException(ServiceCodeEnum.DEVICE_EDGE_GATEWAY_NO_FIND);
        }
    }

    // @Override
    // public R doServiceTask(Long tenantId, Long deviceId, String serviceName, Map<String, Object> input, DeviceServiceLogEntity logEntity) {
    //     logEntity.setInputData(InputValueField.valueOf(input));
    //     Thing thing = engineeringFactory.getEngineering(tenantId).getByDeviceId(deviceId);
    //     if (Optional.ofNullable(thing).isPresent()) {
    //         logEntity.setDeviceName(thing.getDeviceName());
    //         R r = thing.callService(serviceName, input, logEntity);
    //         if (MapUtil.getBool(r, "ok")) {
    //             stringRedisTemplate.opsForValue().increment(String.format(RedisConstant.THING_SERVICE_DAILY_CALLED_COUNT_PREFIX, tenantId,
    //                     DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN)));
    //         }else {
    //             log.error("执行服务失败，详情{}",JSONObject.toJSONString(r));
    //         }
    //         return r;
    //     }

    //     logEntity.setCallSuccess(false);
    //     logEntity.setOutputData(ServiceCodeEnum.DEVICE_NOT_ONLINE.getMessage());
    //     deviceServiceLogMapper.insert(logEntity);
    //     return R.error(ServiceCodeEnum.DEVICE_NOT_ONLINE);
    // }

    @Override
    public R doServiceTask(Long tenantId, Long deviceId, String serviceName, Map<String, Object> input, DeviceServiceLogEntity logEntity,Boolean isRecordLog) {
        logEntity.setInputData(InputValueField.valueOf(input));

        Engineering things = engineeringFactory.getEngineering(tenantId);
        if (things == null) {
            return R.error(String.format("获取things为null tenantId:{} deviceId:{} serviceName:{}", tenantId, deviceId, serviceName));
        }

        CompletableFuture<R> resultFuture = new CompletableFuture<>();
        Thing thing = things.getByDeviceId(deviceId);

        if (Optional.ofNullable(thing).isPresent()) {
            if(isRecordLog){
                logEntity.setDeviceName(thing.getDeviceName());
                things.getScriptRuntimeContext().runOnContext((c) -> {
                    R r = thing.callService(serviceName, input, logEntity, isRecordLog);
                    resultFuture.complete(r);
                });
            }else{
                R r = thing.callService(serviceName, input, logEntity, isRecordLog);
                resultFuture.complete(r);
            }
        }else{
            resultFuture.completeExceptionally(new RuntimeException("找不到设备thing"));
        }

        if(isRecordLog){
            try {
                R r = resultFuture.get(30, TimeUnit.SECONDS);
                if (MapUtil.getBool(r, "ok")) {
                    stringRedisTemplate.opsForValue().increment(String.format(RedisConstant.THING_SERVICE_DAILY_CALLED_COUNT_PREFIX, tenantId,
                            DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN)));
                }
                return r;
            } catch (Exception e) {
                log.error("调用物服务异常:");
                log.error(e.getMessage(), e);
            }
        }


        logEntity.setCallSuccess(false);
        logEntity.setOutputData(ServiceCodeEnum.DEVICE_NOT_ONLINE.getMessage());
        if(!isRecordLog){
            log.trace("device service add log call return false and  logEntity is{}",JSONUtil.toJsonStr(logEntity));
        }else {
            deviceServiceLogMapper.insert(logEntity);
        }
        return R.error("服务调用失败或超时:" + serviceName + ", deviceId:" + deviceId);
    }

    @Override
    public R doServiceTaskWithoutContext(Long tenantId, Long deviceId, String serviceName, Map<String, Object> input, DeviceServiceLogEntity logEntity) {
        logEntity.setInputData(InputValueField.valueOf(input));

        Engineering things = engineeringFactory.getEngineering(tenantId);
        if (things == null) {
            String errInfo = String.format("tenantId:%s engineering is null", tenantId);
            log.warn(errInfo);
            return R.error(errInfo);
        }

        Thing thing = things.getByDeviceId(deviceId);
        if (Optional.ofNullable(thing).isPresent()) {
            logEntity.setDeviceName(thing.getDeviceName());
            try {
                R r = thing.callService(serviceName, input, logEntity);
                if (MapUtil.getBool(r, "ok")) {
                    deviceLogService.addThingServiceDailyColled(tenantId);
                }
                return r;
            } catch (Exception e) {
                log.error("调用物服务异常: {}", e);
                logEntity.setCallSuccess(false);
                logEntity.setOutputData("调用物服务异常");
                deviceLogService.insertLog(logEntity);
                return R.error("调用物服务异常：" + serviceName + ", deviceId:" + deviceId + "," + e.getMessage());
            }
        }else{
            logEntity.setCallSuccess(false);
            logEntity.setOutputData(ServiceCodeEnum.DEVICE_NOT_ONLINE.getMessage());
            deviceLogService.insertLog(logEntity);
            return R.error("找不到设备，设备id:" + deviceId + ", 服务名：" + serviceName);
        }
    }

    /**
     * 通过物模型通知设备
     *
     * @param thingModelId
     * @param tenantId
     */
    @Override
    public void notifyDeviceSyncByThingModelId(Long thingModelId, Long tenantId,CommonFetcher commonFetcher) {
        if (commonFetcher == null) {
            commonFetcher = commonFetcherFactory.buildCacheFetcher(tenantId);
        }
        List<Long> deviceIds = ThingModel.listAllBeInheritDeviceIdById(thingModelId, commonFetcher);
        if (Optional.ofNullable(deviceIds).isPresent() && deviceIds.size() > 0) {
            new LambdaUpdateChainWrapper<>(deviceMapper)
                    .set(
                            DeviceEntity::getSyncStatus, SyncStatusEnum.NOT_SYNC.getValue()
                    )
                    .eq(DeviceEntity::getTenantId, tenantId)
                    .in(DeviceEntity::getId, deviceIds)
                    .update();
        }
    }

    @Override
    public void notifyDeviceSyncByThingModelIds(Set<Long> thingModelIds, Long tenantId,CommonFetcher commonFetcher) {
        if (commonFetcher == null) {
            commonFetcher = commonFetcherFactory.buildCacheFetcher(tenantId);
        }
        Set<Long> deviceIds = new HashSet<>();
        if (!thingModelIds.isEmpty()) {
            CommonFetcher finalCommonFetcher = commonFetcher;
            thingModelIds.forEach(thingModelId -> {
                List<Long> ids = ThingModel.listAllBeInheritDeviceIdById(thingModelId, finalCommonFetcher);
                deviceIds.addAll(ids);
            });
        }
        if (!deviceIds.isEmpty()) {
            new LambdaUpdateChainWrapper<>(deviceMapper)
                    .set(
                            DeviceEntity::getSyncStatus, SyncStatusEnum.NOT_SYNC.getValue()
                    )
                    .eq(DeviceEntity::getTenantId, tenantId)
                    .in(DeviceEntity::getId, deviceIds)
                    .update();
        }
    }

    @Override
    public Result<List<DeviceEntity>> listByInheritModelId(Long modelId,Long tenantId) {

//        org.neo4j.ogm.model.Result maps = neo4jDeviceRepository.devicesByInheritModelId(modelId);
        List<DeviceEntity> result = Lists.newArrayList();
//        if(!Objects.isNull(maps)){
//            Iterator<Map<String, Object>> iterator = maps.iterator();
//            while (iterator.hasNext()){
//                Map<String, Object> next = iterator.next();
//                Neo4jDeviceDTO deviceDto = (Neo4jDeviceDTO)next.get("n");
//                DeviceEntity entity = DeviceEntity.builder()
//                        .id(deviceDto.getDeviceId())
//                        .name(deviceDto.getName())
//                        .tenantId(deviceDto.getTenantId())
//                        .build();
//                result.add(entity);
//            }
//        }
        CommonFetcher commonFetcher = commonFetcherFactory.buildCommonFetcher(tenantId);
        List<DeviceModelInheritEntity> deviceInheritEntityList = commonFetcher.list("inherit_thing_model_id", modelId, DeviceModelInheritEntity.class);
        if(CollectionUtil.isNotEmpty(deviceInheritEntityList)){
            Set<Long> deviceIdList = deviceInheritEntityList.stream().map(DeviceModelInheritEntity::getDeviceId).collect(Collectors.toSet());
            if(CollectionUtil.isNotEmpty(deviceIdList)){
                LambdaQueryChainWrapper<DeviceEntity> wrapper = new LambdaQueryChainWrapper<>(deviceMapper);
                result = wrapper.eq(DeviceEntity::getTenantId, tenantId)
                        .eq(DeviceEntity::getDeleted , 0)
                        .in(DeviceEntity::getId, deviceIdList)
                        .list();
            }
        }
        return Result.ok(result);
    }

    @Override
    public Result<List<DeviceServiceEntity>> listDeviceService(TenantIsolation tenantIsolation, Long deviceId) {

//        Result<Object> inheritByDeviceId = modelGraphService.queryInheritByDeviceId(deviceId);
        List<Long> modelIds = Lists.newArrayList();
//        if(inheritByDeviceId.getSignal()){
//            cn.hutool.json.JSONObject inheritIds = JSONUtil.parseObj(inheritByDeviceId.getResult());
//            JSONArray inherit = inheritIds.getJSONArray("inherit");
//            if(!Objects.isNull(inherit)) {
//                inherit.forEach(item -> {
//                    modelIds.add(JSONUtil.parseObj(item).getLong("modelId"));
//                });
//            }
//        }

        Result<Set<Long>> inheritIdsResult = deviceModelInherits(deviceId,tenantIsolation.getTenantId());
        if(inheritIdsResult.getSignal()){
            modelIds.addAll(inheritIdsResult.getResult());
        }
        List<ThingServiceEntity> thingServiceEntities = null;
        if(CollectionUtil.isNotEmpty(modelIds)){
            QueryWrapper<ThingServiceEntity> wrapper = new QueryWrapper<>();
            wrapper.in("thing_model_id", modelIds);
            thingServiceEntities = thingServiceService.list(wrapper);
        }

        Result<List<DeviceServiceEntity>> listResult = deviceServiceService.listByDeviceId(tenantIsolation,deviceId);
        List<DeviceServiceEntity> result = listResult.getResult();
        if(CollectionUtil.isNotEmpty(thingServiceEntities)) {
            thingServiceEntities.forEach(ts -> {
                DeviceServiceEntity deviceServiceEntity = BeanUtilsIntensifier.copyBean(ts, DeviceServiceEntity.class);
                deviceServiceEntity.setDeviceId(deviceId);
                result.add(deviceServiceEntity);
            });
        }
        return Result.ok(result);
    }

    private void uniqueName(String name, Long tenantId) {
        this.uniqueName(null, name, tenantId);
    }

    /**
     * 判断名称是否唯一
     *
     * @param id
     * @param name
     * @param tenantId
     * @return
     */
    private void uniqueName(Long id, String name, Long tenantId) {
        LambdaQueryWrapper<DeviceEntity> lqw = new LambdaQueryWrapper<>();
        lqw.ne(id != null, DeviceEntity::getId, id)
                .eq(DeviceEntity::getName, name)
                .eq(DeviceEntity::getTenantId, tenantId);
        if (deviceMapper.selectCount(lqw) > 0) {
            throw new BizException("已经存在该名称的设备,名称：" + name);
        }
    }

    @Autowired
    AbsolutePathLabelMapper absolutePathLabelMapper;

    @Override
    @Transactional
    public Result<Long> createByLabelIds(TenantIsolation tenant, BatchLabelCreateDeviceParam param) {
        this.uniqueName(param.getName(), tenant.getTenantId());
        String resourceId = this.uniqueResourceId(null, tenant.getTenantId());
        //创建设备
        DeviceEntity deviceEntity = new DeviceEntity();
        deviceEntity.setEdgeGatewayId(param.getEdgeGatewayId());
        deviceEntity.setName(param.getName());
        deviceEntity.setDescript(param.getDescript());
        deviceEntity.setTenantId(tenant.getTenantId());
        deviceEntity.setStatus(StatusEnum.INACTIVATED.getValue());
        deviceEntity.setSyncStatus(SyncStatusEnum.NOT_SYNC.getValue());
        deviceEntity.setResourceId(resourceId);
        deviceMapper.insert(deviceEntity);
        log.debug("createByLabels 创建设备");

        Long deviceId = deviceEntity.getId();

        //创建资源
        ResourceRelationEntity build = ResourceRelationEntity.builder()
                .deviceId(deviceEntity.getId())
                .tenantId(tenant.getTenantId())
                .build();
        resourceRelationMapper.insert(build);

        //创建设备继承物模型关系
        List<Long> inheritThingModelIds = param.getInheritThingModelIds();
        if (inheritThingModelIds != null && inheritThingModelIds.size() > 0) {
            int sortNo = 0;
            for (Long thingModelId : inheritThingModelIds) {
                DeviceModelInheritEntity inheritEntity = new DeviceModelInheritEntity();
                inheritEntity.setDeviceId(deviceId);
                inheritEntity.setInheritThingModelId(thingModelId);
                inheritEntity.setTenantId(tenant.getTenantId());
                inheritEntity.setSortNo(sortNo++);
                deviceModelInheritMapper.insert(inheritEntity);
            }
        }
        log.debug("createByLabels 创建设备继承物模型关系");


        CommonFetcher commonFetcher = commonFetcherFactory.buildCacheFetcher(tenant.getTenantId());

        Result<ThingModelInherit> inheritResult = ThingModelInherit.checkInfo(
                param.getInheritThingModelIds(),
                commonFetcher,
                ThingModel.THING_MODEL_ID_PATH_SPLITER
        );

        if (!inheritResult.getSignal()) {
            log.error("createByLabels {}", inheritResult.getMessage());
            throw new BizException(inheritResult.getMessage());
        }


        List<Long> labelIds = param.getLabelIds();
        LambdaQueryChainWrapper<AbsolutePathLabelEntity> wrapper = new LambdaQueryChainWrapper<>(absolutePathLabelMapper).in(AbsolutePathLabelEntity::getId, labelIds);
        List<AbsolutePathLabelEntity> labelList = absolutePathLabelMapper.list(wrapper.getWrapper());

        //检查入参标签跟数据库中的数据是否匹配
//        List<LabelEntity> labelList = labelService.listByIds(labelIds, tenant);
        if (labelList == null || labelList.size() <= 0) {
            log.error("createByLabels 查不到标签");
            throw new BizException("查不到标签");
        }

        HashSet<String> nameSet = new HashSet<>();
        List<String> baseNames = DeviceModel.getBaseModel().getProperties().stream().map(Property::getName).collect(Collectors.toList());
        for (AbsolutePathLabelEntity labelEntity : labelList) {
            if(!nameSet.add(labelEntity.getName())){
                throw new BizException("存在名称重复的标签名称：" + labelEntity.getName());
            }
            if(baseNames.contains(labelEntity.getName())){
                throw new BizException("标签名与属性保留字冲突");
            }
            labelEntity.setAbsoluteName(labelEntity.getChannelName() + "." + labelEntity.getLabelGroupName() + "." + labelEntity.getName());
        }

        //查询结果比入参少
        if (labelList.size() < labelIds.size()) {
            Set<Long> idSet = labelList.stream()
                    .map(AbsolutePathLabelEntity::getId)
                    .collect(Collectors.toSet());
            for (Long id : labelIds) {
                if (!idSet.contains(id)) {
                    log.error("createByLabels 查不到标签，labelId:{}", id);
                    throw new BizException("查不到标签，labelId:" + id);
                }
            }
        }

        ThingModelInherit thingModelInherit = inheritResult.getResult();
        List<Property> properties = thingModelInherit.getProperties();
        Map<String, Property> propertiesMap = properties.stream().collect(Collectors.toMap(Property::getName, Function.identity()));
        PropertiesBindLabelVO propertiesBindLabelVO = this.propertiesBindLabel(deviceId, param.getEdgeGatewayId() , propertiesMap, labelList);
        labelBindRelationService.saveBatch(propertiesBindLabelVO.getLabelBindRelationEntities());
        log.info("createByLabels 绑定完成");
        deviceEntity.setModel(propertiesBindLabelVO.getModelField());
        deviceMapper.updateById(deviceEntity);

        //校验绑定后的设备模型
        Result<Device> deviceResult2 = Device.checkInfoToBindRelation(
                deviceEntity,
                commonFetcher
        );
        if (!deviceResult2.getSignal()) {
            log.error("createByLabels {}", deviceResult2.getMessage());
            throw new BizException(deviceResult2.getMessage());
        }

        //创建标记
        tagBindRelationService.saveList(
                tenant.getTenantId(),
                deviceEntity.getId(),
                param.getTagIds(),
                ResourceTypeEnum.DEVICE
        );
        //设备 图节点
        /*Result<Void> result = modelGraphService.saveDeviceNode(BeanUtilsIntensifier.copyBean(deviceEntity,DeviceDto.class), param.getInheritThingModelIds());
        if(!result.getSignal()){
            throw new BizException(result.getMessage());
        }*/

        log.info("createByLabels 成功");
        changeNotice(tenant.getTenantId(),ChangeTypeEnum.ADD,deviceEntity,inheritResult.getResult().getThingModelList().get(0).getName());
        return Result.ok(deviceEntity.getId());
    }

    @Override
    public Result<Void> setNotSyncByLabelIdList(List<Long> labelIdList) {
        if (CollectionUtils.isEmpty(labelIdList)) {
            return Result.ok();
        }
        deviceMapper.updateSyncStatusByLabelIdList(labelIdList, SyncStatusEnum.NOT_SYNC.getValue());
        return Result.ok();
    }

    @Override
    public Result<Void> setNotSyncByIds(Set<Long> deviceIds) {
        LambdaUpdateWrapper<DeviceEntity> luw = new LambdaUpdateWrapper<>();
        luw.set(DeviceEntity::getSyncStatus, SyncStatusEnum.NOT_SYNC.getValue())
                .in(DeviceEntity::getId, deviceIds);
        this.update(luw);
        return Result.ok();
    }

    @Autowired @Lazy
    IDeviceStatusManagementService deviceStatusManagementService;

    @Override
    public Result<List<DeviceRespondBo>> deviceBatchDelete(TenantIsolation tenantIsolation, List<Long> ids) {
        return deviceStatusManagementService.deviceBatchOption(tenantIsolation, ids, DeviceOptionEnum.DELETE);
    }

    @Override
    @Transactional
    public Result<AssembleByLabelIdGroupIdsVO> assembleByLabelGroupIds(AssembleByLabelGroupIdsDTO dto, TenantIsolation tenant) {
        ArrayList<AssembleByLabelIdGroupIdVO> assembleByLabelIdGroupIdList = new ArrayList<>();
        long uuid = IdGenerator.generateId();
        redisTemplate.opsForValue().set(RedisConstant.ASSEMBLE + uuid, true, 1800L, TimeUnit.SECONDS);
        HashSet<String> nameSet = new HashSet<>();
        CommonFetcher commonFetcher = commonFetcherFactory.buildCommonFetcher(tenant.getTenantId());

        Result<ThingModelInherit> inheritResult = ThingModelInherit.checkInfo(
                dto.getInheritThingModelIds(),
                commonFetcher,
                ThingModel.THING_MODEL_ID_PATH_SPLITER
        );
        if (!inheritResult.getSignal()) {
            log.error("createByLabels {}", inheritResult.getMessage());
            throw new BizException(inheritResult.getMessage());
        }
        
        ThingModelInherit thingModelInherit = inheritResult.getResult();
        List<LabelEntity>  labelList=labelService.listByLabelGroupIds(dto.getLabelGroupIds(),tenant);
        Map<Long,List<LabelEntity>> labelGroupMap = labelList.stream().collect(Collectors.groupingBy(LabelEntity::getLabelGroupId));
        for (Long labelGroupId : dto.getLabelGroupIds()) {
            assembleByLabelIdGroupIdList.add(this.assembleByLabelIdGroupId(uuid, nameSet, labelGroupId, thingModelInherit, tenant,labelGroupMap));
        }
        return Result.ok(AssembleByLabelIdGroupIdsVO.builder()
                .uuid(uuid).assembleByLabelIdGroupIdList(assembleByLabelIdGroupIdList).build());
    }

    public AssembleByLabelIdGroupIdVO assembleByLabelIdGroupId(long uuid, Set<String> nameSet, Long labelGroupId, ThingModelInherit thingModelInherit, TenantIsolation tenant,Map<Long,List<LabelEntity>> labelGroupMap) {
        Result<LabelGroupEntity> labelGroupResult = labelGroupService.getByIdAndTenantIsolation(labelGroupId, tenant);
        if (!labelGroupResult.getSignal() || labelGroupResult.getResult() == null) {
            return null;
        }
        AssembleByLabelIdGroupIdVO result = new AssembleByLabelIdGroupIdVO();

        LabelGroupEntity labelGroupEntity = labelGroupResult.getResult();
        ChannelEntity channel = channelService.getByIdAndTenantIsolation(labelGroupEntity.getChannelId(), tenant).getResult();
        String deviceName = channel.getName() + "_" + StringUtils.replace(labelGroupEntity.getName(), ".", "_");
        boolean uniqueName = false;
        while (!uniqueName) {
            try {
                this.uniqueName(deviceName, tenant.getTenantId());
                uniqueName = true;
            } catch (BizException e) {
                deviceName += "副本";
            }
        }
        uniqueName = false;
        while (!uniqueName) {
            boolean add = nameSet.add(deviceName);
            if (add) {
                uniqueName = true;
                continue;
            }
            deviceName += "副本";
        }
        result.setDeviceName(deviceName);
        //检查入参标签跟数据库中的数据是否匹配
        List<LabelEntity> labelList = labelGroupMap.get(labelGroupId);
        if (!CollectionUtils.isEmpty(labelList)) {
            List<String> baseNames = DeviceModel.getBaseModel().getProperties().stream().map(Property::getName).collect(Collectors.toList());
            for (LabelEntity labelEntity : labelList) {
                if(baseNames.contains(labelEntity.getName())){
                    throw new BizException("标签名与属性保留字冲突");
                }
            }
            List<Property> properties = thingModelInherit.getProperties();
            Map<String, Property> propertiesMap = properties.stream().collect(Collectors.toMap(Property::getName, Function.identity()));
            PropertiesBindLabelVO propertiesBindLabelVO = this.propertiesBindLabel(null, null, propertiesMap, BeanUtilsIntensifier.copyBeanList(labelList,AbsolutePathLabelEntity.class));
            result.setBindNum(propertiesBindLabelVO.getBindNum());
            redisTemplate.opsForValue().set(RedisConstant.ASSEMBLE + uuid + deviceName , propertiesBindLabelVO, 1800L, TimeUnit.SECONDS);
        }
        redisTemplate.opsForValue().set(RedisConstant.ASSEMBLE + uuid + deviceName + "source", channel.getName() + "." + labelGroupEntity.getName(), 1800L, TimeUnit.SECONDS);
        return result;
    }

    public PropertiesBindLabelVO propertiesBindLabel(Long deviceId, Long edgeGatewayId, Map<String, Property> propertiesMap, List<AbsolutePathLabelEntity> labelList) {
        int bindNum = labelList.size();
        ArrayList<LabelBindRelationEntity> labelBindRelationEntities = new ArrayList<>();
        Iterator<AbsolutePathLabelEntity> iterator = labelList.iterator();
        //开始绑定
        while (iterator.hasNext()) {
            AbsolutePathLabelEntity label = iterator.next();
            Property property = propertiesMap.get(label.getName());
            if (property == null) {
                continue;
            }
            iterator.remove();

            if (!property.getDataType().getType().getName().equals(label.getDataType())
                    || !property.getDataType().getIsArray().equals(label.getIsArray())
                    || !property.getReadOnly().equals(label.getReadOnly())
            ) {
                bindNum--;
                continue;
            }
            LabelBindRelationEntity relation = new LabelBindRelationEntity();
            relation.setLabelId(label.getId());
            String propertyName = property.getName();
            Long directlyModelId = property.getDirectlyModelId();
            Integer modelType = property.getModelType().getValue();
            relation.setDeviceId(deviceId);
            relation.setEdgeGatewayId(edgeGatewayId);
            relation.setPropertyName(propertyName);
            relation.setLabelName(label.getName());
            relation.setLabelGroupName(label.getLabelGroupName());
            relation.setChannelName(label.getChannelName());
            relation.setModelType(modelType);
            relation.setDirectlyModelId(directlyModelId);
            labelBindRelationEntities.add(relation);
        }
        ModelField modelField = null;
        if (labelList.size() > 0) {
            modelField = labelService.toModelFieldByLabels(BeanUtilsIntensifier.copyBeanList(labelList,LabelEntity.class));
            for (AbsolutePathLabelEntity label : labelList) {
                LabelBindRelationEntity relation = new LabelBindRelationEntity();
                relation.setLabelId(label.getId());
                relation.setEdgeGatewayId(edgeGatewayId);
                relation.setDeviceId(deviceId);
                relation.setPropertyName(label.getName());
                relation.setLabelName(label.getName());
                relation.setLabelGroupName(label.getLabelGroupName());
                relation.setChannelName(label.getChannelName());
                relation.setModelType(ModelTypeEnum.DEVICE_MODEL.getValue());
                relation.setDirectlyModelId(deviceId);
                labelBindRelationEntities.add(relation);
            }

        }
        return PropertiesBindLabelVO.builder()
                .bindNum(bindNum)
                .modelField(modelField)
                .labelBindRelationEntities(labelBindRelationEntities).build();

    }

    @Override
    @Transactional
    public Result<Object> createByAssembleInfo(CreateByAssembleInfoDTO dto, TenantIsolation tenant) {
        Long uuid = dto.getUuid();
        Boolean exist = (Boolean) redisTemplate.opsForValue().get(RedisConstant.ASSEMBLE + uuid);
        log.info("是否存在exist：" + exist);

        if (exist == null || !exist ) {
            if(dto.getCancel()){
                return Result.ok();
            }
            throw new BizException("创建超时，请重新创建");
        }

        redisTemplate.delete(RedisConstant.ASSEMBLE + uuid);

        List<DeviceEntity> deviceEntities=Lists.newArrayList();
        List<ResourceRelationEntity> resourceRelationEntities=Lists.newArrayList();
        List<DeviceModelInheritEntity> inheritEntities=Lists.newArrayList();
        Map<Long,List<Long>> deviceTagIdMap= Maps.newHashMap();
        Set<String> redisKeySet= Sets.newHashSet();
        for (AssembleByLabelIdGroupIdVO assembleByLabelIdGroupIdVO : dto.getAssembleByLabelIdGroupIdList()) {
            String deviceName = assembleByLabelIdGroupIdVO.getDeviceName();

            if (!dto.getCancel()) {
                this.uniqueName(deviceName, tenant.getTenantId());
                String resourceId = this.uniqueResourceId(null, tenant.getTenantId());
                PropertiesBindLabelVO propertiesBindLabelVO =  (PropertiesBindLabelVO)redisTemplate.opsForValue().get(RedisConstant.ASSEMBLE + uuid + deviceName );
                if (propertiesBindLabelVO == null){
                    propertiesBindLabelVO = new PropertiesBindLabelVO();
                }
                List<String> baseNames = DeviceModel.getBaseModel().getProperties().stream().map(Property::getName).collect(Collectors.toList());
                if(ObjectUtil.isNotEmpty(propertiesBindLabelVO.getModelField()) && CollectionUtil.isNotEmpty(propertiesBindLabelVO.getModelField().getProperties())){
                    List<PropertyElm> propertyElms = propertiesBindLabelVO.getModelField().getProperties();
                    for(PropertyElm propertyElm : propertyElms){
                        if(baseNames.contains(propertyElm.getName())){
                            throw new BizException("已存在相关保留字");
                        }
                    }
                }
                //创建设备
                DeviceEntity deviceEntity = new DeviceEntity();
                deviceEntity.setResourceId(resourceId);
                deviceEntity.setEdgeGatewayId(dto.getEdgeGatewayId());
                deviceEntity.setName(deviceName);
                deviceEntity.setStatus(StatusEnum.INACTIVATED.getValue());
                deviceEntity.setSyncStatus(SyncStatusEnum.NOT_SYNC.getValue());
                deviceEntity.setModel(propertiesBindLabelVO.getModelField());
                String s = (String) redisTemplate.opsForValue().get(RedisConstant.ASSEMBLE + uuid + deviceName + "source");
                if (!StringUtils.isEmpty(s)) {
                    int i = s.indexOf(".");
                    if (i > 0) {
                        deviceEntity.setSource(s.substring(i+1));
                        deviceEntity.setChannel(s.substring(0,i));
                    }
                }
                deviceEntity.setId(IdGenerator.generateId());
                deviceEntities.add(deviceEntity);
                // deviceMapper.insert(deviceEntity);
                log.info("createByLabels 创建设备");

                Long deviceId = deviceEntity.getId();

                //创建资源
                ResourceRelationEntity relation = ResourceRelationEntity.builder()
                        .deviceId(deviceId)
                        .tenantId(tenant.getTenantId())
                        .build();
                // resourceRelationMapper.insert(build);
                resourceRelationEntities.add(relation);

                //创建设备继承物模型关系
                List<Long> inheritThingModelIds = dto.getInheritThingModelIds();
                if (inheritThingModelIds != null && inheritThingModelIds.size() > 0) {
                    int sortNo = 0;
                    for (Long thingModelId : inheritThingModelIds) {
                        DeviceModelInheritEntity inheritEntity = new DeviceModelInheritEntity();
                        inheritEntity.setDeviceId(deviceId);
                        inheritEntity.setInheritThingModelId(thingModelId);
                        inheritEntity.setSortNo(sortNo++);
                        // deviceModelInheritMapper.insert(inheritEntity);
                        inheritEntities.add(inheritEntity);
                    }
                }
                log.info("createByLabels 创建设备继承物模型关系");

                deviceTagIdMap.put(deviceId, dto.getTagIds());
               
                //废弃，此通知是给第一版的云管使用的，但第一版云管已经启用，创建了第二版的云管，也就是当前有ot团队构建的云管
                // changeNotice(tenant.getTenantId(),ChangeTypeEnum.ADD,deviceEntity,deviceResult.getResult().getDeviceModel().getInherit().getThingModelList().get(0).getName());
            }
            redisKeySet.add(RedisConstant.ASSEMBLE + uuid + deviceName );
            redisKeySet.add(RedisConstant.ASSEMBLE + uuid + deviceName+"source");
            // redisTemplate.delete(RedisConstant.ASSEMBLE + uuid + deviceName );
            // redisTemplate.delete(RedisConstant.ASSEMBLE + uuid + deviceName+"source" );

        }

        DeviceCheckInfoContext context=new DeviceCheckInfoContext(deviceEntities);
        for(DeviceEntity deviceEntity:deviceEntities){
            Result<Device> deviceResult = Device.checkInfoToBindRelation2(
                    deviceEntity, context
            );
            if (!deviceResult.getSignal()) {
                log.error("createByLabels {}", deviceResult.getMessage());
                throw new BizException(deviceResult.getMessage());
            }
        }
        deviceMapper.insertBatchSomeColumn(deviceEntities);
        resourceRelationMapper.insertBatchSomeColumn(resourceRelationEntities);
        deviceModelInheritMapper.insertBatchSomeColumn(inheritEntities);
        tagBindRelationService.batchSaveList(tenant.getTenantId(),deviceTagIdMap,ResourceTypeEnum.DEVICE);
        redisTemplate.delete(redisKeySet);

        return Result.ok();
    }

    @Override
    public Result<DeviceFaultStatistic> getDeviceFaultData(Long tenantId) {

        if(Objects.isNull(tenantId)){
            return Result.error();
        }
        QueryWrapper<DeviceEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tenant_id",tenantId);
        //获取当前租户下的设备总数(包含未启用,未激活)
        Integer total = deviceMapper.selectCount(queryWrapper);
        //当前故障设备数
//        String faultCount= stringRedisTemplate.opsForValue().get(String.format(RedisConstant.DEVICE_FAULT_COUNT, tenantId));
        //当前故障设备列表
        //获取故障中的设备id
        Set<Object> deviceIds = redisTemplate.opsForSet().members(String.format(RedisConstant.TENANT_DEVICE_ON_FAULT, tenantId));
//        Integer faultCountNum = Integer.valueOf(Objects.isNull(faultCount)?"0":faultCount);
        List<DeviceFaultInstance> faultInstanceList = null;
        if(!CollectionUtils.isEmpty(deviceIds)){
            faultInstanceList = new ArrayList<>();
            for (Object deviceId : deviceIds) {
                String instanceKey = String.format(RedisConstant.DEVICE_FAULT_INSTANCE_CACHE,tenantId, deviceId);
                DeviceFaultInstance instance = JSONUtil.toBean(JSONUtil.toJsonStr(redisTemplate.opsForValue().get(instanceKey)), DeviceFaultInstance.class);
                Map<Object, Object> entries = redisTemplate.opsForHash().entries(String.format(RedisConstant.DEVICE_FAULT_EVENT_INSTANCE,tenantId, deviceId));
                List<FaultEventInstance> faultEventInstances = Lists.newArrayList();
                entries.forEach((k,v)->{
                    faultEventInstances.add(JSONUtil.toBean(JSONUtil.toJsonStr(v),FaultEventInstance.class));
                });
                instance.setEventInstances(faultEventInstances);
                faultInstanceList.add(instance);
            }
        }
        DeviceFaultStatistic deviceFaultStatistic = new DeviceFaultStatistic();
        deviceFaultStatistic.setFaultDevice(deviceIds.size());
        deviceFaultStatistic.setDeviceTotal(total);
        deviceFaultStatistic.setNormalDevice(total - deviceIds.size());
        deviceFaultStatistic.setFaultInstanceList(faultInstanceList);
        return Result.ok(deviceFaultStatistic);
    }

    @Override
    public Result<List<DeviceEntity>> listDeviceByIds(Long tenantId, List<Long> deviceIds, SFunction<DeviceEntity,?>... functions) {
        LambdaQueryChainWrapper<DeviceEntity> in = new LambdaQueryChainWrapper<>(deviceMapper).eq(DeviceEntity::getTenantId, tenantId)
                .in(CollectionUtil.isNotEmpty(deviceIds), DeviceEntity::getId, deviceIds);
        if (ObjectUtil.isNotEmpty(functions)) {
            in = in.select(functions);
        }
        List<DeviceEntity> list = in.list();
        return Result.ok(list);
    }

    @Override
    public Result<List<DeviceEntity>> listDeviceByNames(Long tenantId, List<String> deviceNames) {
        List<DeviceEntity> list = deviceMapper.selectList(Wrappers.<DeviceEntity>lambdaQuery()
                .eq(DeviceEntity::getTenantId, tenantId)
                .in(CollectionUtil.isNotEmpty(deviceNames), DeviceEntity::getName, deviceNames));
        return Result.ok(list);
    }

   
    @Override
    public Result<List<DcmpDeviceVO>> listBasicDeviceForDcmp(TenantIsolation tenantIsolation, ListDcmpDeviceRequest request) {
        List<DeviceEntity> deviceList = deviceMapper.listDevice(tenantIsolation,request);
        if (deviceList == null || deviceList.size() == 0){
            return Result.ok();
        }
        List<Long> idList = deviceList.stream().map(i -> i.getId()).collect(Collectors.toList());
        Map<Long, List<TagRsp>> tagMap = tagBindRelationService.getTagListGroupByTargetId(tenantIsolation.getTenantId(), ResourceTypeEnum.DEVICE, idList);

        List<DcmpDeviceVO> dcmpDeviceList= BeanUtilsIntensifier.copyBeanList(deviceList,DcmpDeviceVO.class);
        dcmpDeviceList.forEach(item->{
            item.setTags(tagMap.get(item.getId()));
        });
        return Result.ok(dcmpDeviceList);
    }


    @Override
    public Result<List<DeviceChannelBo>> listGatewayDevices(Long edgeGatewayId, TenantIsolation tenantIsolation) {
        return Result.ok(deviceMapper.listGatewayDevices(edgeGatewayId,tenantIsolation.getTenantId()));
    }

    public Result<Void> deviceStatusProcess(DeviceEntity deviceEntity, List<ChangeNoticeEntity> noticeEntityList, Boolean gwOnline, Map<String,Boolean> channelStatusMap, Map<Object,Object> deviceStatusMap, Map<Object, Object> deviceOfflineTimesMap, String tenantStr, Map<Long, List<LabelBindRelationEntity>> labelBindRelationEntityMap, Integer type){
        //如果没有运行中的数据则返回
        log.debug("device-status-trace deviceStatusProcess");

        String key = RedisConstant.DEVICE_STATUS + tenantStr;
        String offlineKey = RedisConstant.DEVICE_OFFLINE_TIMES + tenantStr;
        List<LabelBindRelationEntity> labelBindRelationEntityList = labelBindRelationEntityMap.get(deviceEntity.getId());

        Map<Long,Integer> needToSendMap = needSendDeviceMap(deviceEntity,gwOnline,channelStatusMap,deviceStatusMap,deviceOfflineTimesMap,key,offlineKey,labelBindRelationEntityList,type);


        log.debug("device-status-trace needToSendMap: {}", needToSendMap);
        if(CollectionUtil.isNotEmpty(needToSendMap) && CollectionUtil.isNotEmpty(noticeEntityList)){
            log.debug("device-status-trace needToSendMap size: {}", needToSendMap.size());
            SendMessage sendMessage = new SendMessage();
            sendMessage.setPayload(needToSendMap);
            //推送
            for (ChangeNoticeEntity changeNoticeEntity:noticeEntityList){
                log.debug("device status send and redirectId {},need send str {}",changeNoticeEntity.getRedirectId(),JSONObject.toJSONString(sendMessage));
                subscriptionService.execRedirectWithPayload(deviceEntity.getTenantId(),changeNoticeEntity.getRedirectId(),JSONObject.toJSONString(sendMessage));
            }
        }
        return Result.ok();
    }

    @Autowired
    private DeviceDataResource deviceDataResource;

    public static UpData buildUpData(
        Long deviceId, Long timeMillis, String property,
        Object value
    ){
        UpData upData = new UpData();
        upData.setTimestamp(timeMillis);
        List<UpProp> propList = new ArrayList<>();
        UpProp upProp = new UpProp();
        upProp.setProperty(property);
        upProp.setDeviceId(deviceId);
        upProp.setDataType(ThingDataTypeEnum.SHORT.getName());
        upProp.setIsArray(false);
        upProp.setLength(1);
        upProp.setValue(value);
        propList.add(upProp);
        upData.setProp(propList);
        return upData;
    }

    @Override
    public List<Long> getDeviceIdsByTenantId(Long tenantId, DeviceTwinRequestBo request) {
        List<DeviceEntity> list = new LambdaQueryChainWrapper<>(deviceMapper)
                .eq(DeviceEntity::getTenantId, tenantId)
                .eq(ObjectUtil.isNotEmpty(request.getEdgeGatewayId()),DeviceEntity::getEdgeGatewayId,request.getEdgeGatewayId())
                .select(DeviceEntity::getId).list();
        if (CollectionUtil.isEmpty(list)) {
            return new ArrayList<>();
        }else {
            return list.stream().map(DeviceEntity::getId).collect(Collectors.toList());
        }
    }

    @Override
    public Result<List<DeviceOnlineStatusVO>> listDeviceOnlineStatus(GateWayDeviceListDTO dto,TenantIsolation tenantIsolation) {
        List<EdgeGatewayEntity> edgeGatewayEntityList =  edgeGatewayService.getAllEdgeGatewayByTenantId(tenantIsolation.getTenantId()).getResult();
        if(CollectionUtil.isEmpty(edgeGatewayEntityList)){
            return Result.ok();
        }
        List<DeviceOnlineStatusVO> deviceOnlineStatusVOList = new ArrayList<>();
        List<GateWayDeviceDTO> gateWayDeviceList = dto.getGateWayDeviceList();
        Map<Long, List<GateWayDeviceDTO>> groupBy = gateWayDeviceList.stream().collect(Collectors.groupingBy(GateWayDeviceDTO::getEdgeGatewayId));
        for (Map.Entry<Long, List<GateWayDeviceDTO>> entry : groupBy.entrySet()) {
            Map<Object, Object> deviceStatusMap = getDeviceStatusMap(tenantIsolation.getTenantId(),entry.getKey());
            for(GateWayDeviceDTO gateWayDeviceListDTO : entry.getValue()){
                //内存设备状态
                DeviceOnlineStatusVO deviceOnlineStatusVO = new DeviceOnlineStatusVO();
                deviceOnlineStatusVO.setId(gateWayDeviceListDTO.getDeviceId());
                if(deviceStatusMap.get(String.valueOf(gateWayDeviceListDTO.getDeviceId()))!=null && String.valueOf(DeviceStatusEnum.ONLINE.getValue()).equals(deviceStatusMap.get(String.valueOf(gateWayDeviceListDTO.getDeviceId())))){
                    deviceOnlineStatusVO.setOnline(DeviceStatusEnum.ONLINE.getValue());
                }else {
                    deviceOnlineStatusVO.setOnline(DeviceStatusEnum.OFFLINE.getValue());
                }
                deviceOnlineStatusVOList.add(deviceOnlineStatusVO);
            }
        }
        return Result.ok(deviceOnlineStatusVOList);
    }

    public Map<Object, Object> getDeviceStatusMap(Long tenantId,Long edgeGatewayId){
        Map<Object, Object> deviceStatusMap = stringRedisTemplate.opsForHash().entries(RedisConstant.DEVICE_STATUS + tenantId);
        return deviceStatusMap;
    }

    @Override
    @Transactional
    public void updateDeviceName(DeviceEntity deviceEntity) {
        deviceMapper.updateById(deviceEntity);
    }

    @Override
    @Async
    public void deviceBatchStatusChangeNotify(TenantIsolation tenant, List<Long> deviceIds, DeviceStatusEnum deviceStatusEnum) {
        List<ChangeNoticeEntity> changeNoticeEntityList = changeNoticeService.getChangeNoticeList().getResult().stream().filter(f -> tenant.getTenantId().equals(f.getTenantId())).collect(Collectors.toList());
        List<ChangeNoticeEntity> collect = changeNoticeEntityList.stream().filter(f-> ChangeSubjectEnum.DEVICE.getValue() == f.getChangeSubject() && ChangeTypeEnum.STATUS.getValue() == f.getChangeType()).collect(Collectors.toList());
        if(CollectionUtil.isNotEmpty(collect) && CollectionUtil.isNotEmpty(deviceIds)){
            deviceIds.forEach(id -> {
                Map<Long,Integer> needToSendDeviceMap = new HashMap<>();
                needToSendDeviceMap.put(id,deviceStatusEnum.getValue());
                SendMessage sendMessage = new SendMessage();
                sendMessage.setPayload(needToSendDeviceMap);
                for (ChangeNoticeEntity changeNoticeEntity : collect){
                    log.info("send offline device status and content is {}",JSONObject.toJSONString(sendMessage));
                    subscriptionService.execRedirectWithPayload(tenant.getTenantId(),changeNoticeEntity.getRedirectId(),JSONObject.toJSONString(sendMessage));
                }
            });

        }
    }

    @Override
    @Transactional
    public Result<List<DeviceRespondBo>> deleteDeviceBatch(TenantIsolation tenantIsolation, List<DeviceEntity> deviceEntities, Map<Long, String> gwNameMap) {
        Iterator<DeviceEntity> iterator = deviceEntities.iterator();
        List<DeviceRespondBo> deviceBos = new ArrayList<>();
        while (iterator.hasNext()) {
            DeviceEntity next = iterator.next();
            if (next.getStatus().equals(StatusEnum.ONLINE.getValue())) {
                DeviceRespondBo build = DeviceRespondBo.builder()
                        .result(Result.error(ServiceCodeEnum.DEVICE_ONLINE))
                        .edgeGatewayName(gwNameMap.get(next.getEdgeGatewayId()))
                        .build();
                BeanUtilsIntensifier.propertyInjection(next, build);
                deviceBos.add(build);
                iterator.remove();
            }
        }
        if (deviceEntities.isEmpty()) {
            return Result.ok(deviceBos);
        }
        List<Long> ids = BeanUtilsIntensifier.getIds(deviceEntities, DeviceEntity::getId);
        List<Long> edgeGatewayIds = BeanUtilsIntensifier.getIds(deviceEntities, DeviceEntity::getEdgeGatewayId);
        Map<Long,List<String>> deviceEvents = Maps.newHashMap();
        deviceEntities.forEach(de->{
            if(!Objects.isNull(de.getModel())) {
                List<EventElm> events = de.getModel().getEvents();
                if (CollectionUtil.isNotEmpty(events)) {
                    deviceEvents.put(de.getId(), events.stream().map(EventElm::getName).collect(Collectors.toList()));
                }
            }
            //删除订阅调用
            QueryWrapper<SubscriptionEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("directly_model_id",de.getId());
            List<SubscriptionEntity> subscriptionEntities = subscriptionService.list(queryWrapper);
            RedirectReferenceDto delRef = RedirectReferenceDto.builder()
                    .updateType(0)
                    .build();
            if(CollectionUtil.isNotEmpty(subscriptionEntities)){
                for (SubscriptionEntity subscriptionEntity : subscriptionEntities) {
                    delRef.setRefId(subscriptionEntity.getDirectlyModelId());
                    delRef.setRedirectId(subscriptionEntity.getCallbackId());
                    instanceRedirectService.updateReference(tenantIsolation, delRef);
                }
            }
        });
        LambdaQueryWrapper<DeviceEntity> w = new LambdaQueryWrapper<DeviceEntity>().in(DeviceEntity::getId, ids);
        try {
            deviceMapper.delete(w);
            tagBindRelationService.batchDeleteByDeviceIds(tenantIsolation.getTenantId(),new HashSet<Long>(ids));
            labelBindRelationService.batchDeleteByDeviceIds(tenantIsolation.getTenantId(), ids, deviceEntities);
            deviceModelInheritService.batchDeleteByDeviceIds(tenantIsolation.getTenantId(), ids);
            deviceServiceService.batchDeleteByDeviceIds(tenantIsolation, ids);

            subscriptionService.batchDeleteByDirectlyModelIds(tenantIsolation.getTenantId(),ids,ModelTypeEnum.DEVICE_MODEL.getValue());
            resourceRelationMapper.delete(
                    new LambdaQueryWrapper<ResourceRelationEntity>()
                            .eq(ResourceRelationEntity::getTenantId,tenantIsolation.getTenantId())
                            .in(ResourceRelationEntity::getDeviceId,ids)
            );
            Set<String> keys = new HashSet<>();
            deviceEntities.forEach(entity -> {
                MemoryCache.deleteSubscriptionListByDeviceId(entity.getId());
                changeNotice(tenantIsolation.getTenantId(),ChangeTypeEnum.DELETE,DeviceEntity.builder().id(entity.getId()).name(entity.getName()).build(),null);
                String faultInstanceKey = String.format(RedisConstant.DEVICE_FAULT_INSTANCE_CACHE,tenantIsolation.getTenantId(),entity.getId());
                redisTemplate.delete(faultInstanceKey);
                String faultEventInstanceKey = String.format(RedisConstant.DEVICE_FAULT_EVENT_INSTANCE,tenantIsolation.getTenantId(), entity.getId());
                redisTemplate.delete(faultEventInstanceKey);
            });
            if(!org.apache.commons.collections.CollectionUtils.isEmpty(keys)){
                redisTemplate.delete(keys);
            }
            deviceEvents.forEach((k,v)->{
                for (String event : v) {
                    String faultEventStatusKey = String.format(RedisConstant.DEVICE_FAULT_STATUS,tenantIsolation.getTenantId(),event,k);
                    redisTemplate.delete(faultEventStatusKey);
                }
            });
            //批量删除设备故障
            String tenantOnFaultDeviceKey = String.format(RedisConstant.TENANT_DEVICE_ON_FAULT,tenantIsolation.getTenantId());
            redisTemplate.opsForSet().remove(tenantOnFaultDeviceKey,ids.toArray(new Long[ids.size()]));
            edgeGatewayIds.stream().forEach(edgeGatewayId -> {
                Map<Object, Object> deviceStatusMap = getDeviceStatusMap(tenantIsolation.getTenantId(),edgeGatewayId);
                if (CollectionUtil.isNotEmpty(deviceStatusMap)) {
                    ids.forEach(id -> deviceStatusMap.remove(id));
                    if (deviceStatusMap.isEmpty()) {
                        stringRedisTemplate.delete(RedisConstant.DEVICE_STATUS + tenantIsolation.getTenantId());
                    }else {
                        stringRedisTemplate.opsForHash().putAll(RedisConstant.DEVICE_STATUS + tenantIsolation.getTenantId(),deviceStatusMap);
                    }
                }
            });
            return Result.ok(deviceBos);
        } catch (Exception e){
            log.error("设备删除失败,errorMsg:{}", e.getMessage());
            throw new BizException(ServiceCodeEnum.CODE_DELETE_FAIL);
        }
    }

    @Override
    public Result<List<DeviceEntity>> getAllDeviceRunList() {
        LambdaQueryWrapper<DeviceEntity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(DeviceEntity::getStatus, StatusEnum.ONLINE.getValue());
        List<DeviceEntity> deviceEntityList = deviceMapper.selectList(lqw);
        return Result.ok(deviceEntityList);
    }

    private Map<Long,Integer> needSendDeviceMap(DeviceEntity deviceEntity, Boolean gwOnline, Map<String,Boolean> channelStatusMap, Map<Object,Object> deviceStatusMap, Map<Object, Object> deviceOfflineTimesMap, String key, String offlineKey, List<LabelBindRelationEntity> labelBindRelationEntityList, Integer type){
        Integer currentStatus = DeviceStatusEnum.OFFLINE.getValue();
        log.debug("deviceStatus 1 channelStatusMapStr is{}",JSONObject.toJSONString(channelStatusMap));
        if(!StringUtils.isEmpty(deviceEntity.getChannel())){ //如果通道有值则走通道，否则走网关
            String Key =deviceEntity.getEdgeGatewayId() + "_" + deviceEntity.getChannel();
            if(channelStatusMap.get(Key) != null && channelStatusMap.get(Key)){
                currentStatus = DeviceStatusEnum.ONLINE.getValue();
            }
        }else if(type.equals(EdgeGatewayTypeEnum.CONNECTOR_GATEWAY.getValue())){
            //如果设备所属网关是直连网关（没有通道）,则根据网关的状态决定设备的状态
            if(gwOnline){
                currentStatus = DeviceStatusEnum.ONLINE.getValue();
            }
        }else{
            // 按设备 ID 分组
            if(CollectionUtil.isEmpty(labelBindRelationEntityList)){
                currentStatus = DeviceStatusEnum.OFFLINE.getValue();
            }else {
                Set<String> channelNames = labelBindRelationEntityList.stream()
                        .map(LabelBindRelationEntity::getChannelName)
                        .collect(Collectors.toSet());
                currentStatus = DeviceStatusEnum.OFFLINE.getValue();
                if(CollectionUtil.isNotEmpty(channelNames)){
                    for(String channelName : channelNames){
                        String Key =deviceEntity.getEdgeGatewayId() + "_" + channelName;
                        if(channelStatusMap.get(Key) != null && channelStatusMap.get(Key)){
                            currentStatus = DeviceStatusEnum.ONLINE.getValue();
                        }
                    }
                }
            }
        }

        log.debug("deviceStatus 2 currentStatus is{}",currentStatus);

        //需要推送的
        Map<Long,Integer> needToSendMap = new HashMap<>();

        log.debug("deviceStatus 3 is {}",JSONObject.toJSONString(deviceStatusMap));
        log.debug("deviceOfflineTimes  is {}",JSONObject.toJSONString(deviceOfflineTimesMap));

        // 从redis获取缓存的设备状态和设备离线次数
        String cacheStatus = String.valueOf(deviceStatusMap.get(String.valueOf(deviceEntity.getId())));
        Integer deviceOfflineTimes = Integer.parseInt( Optional.ofNullable(deviceOfflineTimesMap.get(String.valueOf(deviceEntity.getId()))).orElse("0").toString());
        if(!String.valueOf(currentStatus).equals(cacheStatus)){
            // 当前设备状态是从其他状态变为在线  或者  设备离线出现次数大于1次，才允许直接更新设备状态，同时将设备离线次数更新为0
            if (DeviceStatusEnum.ONLINE.getValue().equals(currentStatus) || deviceOfflineTimes>1){
                needToSendMap.put(deviceEntity.getId(),currentStatus);
                stringRedisTemplate.opsForHash().put(key,String.valueOf(deviceEntity.getId()),String.valueOf(currentStatus));
            }else {
                stringRedisTemplate.opsForHash().put(offlineKey,String.valueOf(deviceEntity.getId()),String.valueOf(deviceOfflineTimes+1));
            }
        }
        if (DeviceStatusEnum.ONLINE.getValue().equals(currentStatus) && deviceOfflineTimes>0){
            stringRedisTemplate.opsForHash().put(offlineKey,String.valueOf(deviceEntity.getId()),"0");
        }
        log.debug("deviceStatus 4");

        long timeMillis = System.currentTimeMillis();
        log.debug("needSendDevice 5 {} {}", needToSendMap.size(), needToSendMap);
        needToSendMap.forEach((k,v) ->{
            HashMap<String, Object> map = Maps.newHashMap();
            map.put("status",v);
            UpData upData = buildUpData(k, timeMillis, "status", v);
            mapping2DeviceHandler.processWriteTwin(deviceEntity.getEdgeGatewayId(), k, deviceEntity.getTenantId(), upData);
        });
        log.debug("deviceStatus 6");
        return needToSendMap;
    }


    @Async("deviceCloudAsyncExecutor")
    @Override
    public void initDeviceTwinData(Device deviceRun, DeviceEntity device, TenantIsolation tenant) {
        DeviceTwin deviceTwin = new DeviceTwin(deviceDataResource, device.getId());
        Map<String, Object> actual = deviceTwin.getActual();
        List<String> baseNames = DeviceModel.getBaseModel().getProperties().stream().map(Property::getName).collect(Collectors.toList());
        // if(actual==null){
        //     log.error("actual is null");
        //     return;
        // }
        Set<String> oldPropertyNames = new HashSet<>(actual.keySet());
        Set<String> newPropertyNames = deviceRun.getDeviceModel().getProperties().stream().map(Property::getName).collect(Collectors.toSet());
        oldPropertyNames.removeAll(newPropertyNames);
        oldPropertyNames.removeAll(baseNames);
        if (!oldPropertyNames.isEmpty()) {
            actual.keySet().removeIf(oldPropertyNames::contains);
            deviceDataResource.deleteProperties(device.getId(), oldPropertyNames);
        }
        Map<String, Object> propertyMap = deviceRun.getPropertiesDefaultValue(actual.keySet());
        for (Map.Entry<String, Object> entry : actual.entrySet()) {
            Object defaultValue = propertyMap.get(entry.getKey());
            Object actualValue = entry.getValue();
            if(defaultValue != null && actualValue == null){
                propertyMap.put(entry.getKey(),defaultValue);
                continue;
            }
            propertyMap.put(entry.getKey(),entry.getValue());
        }
        Collection<LabelBindRelation> bindRelations = deviceRun.getPropertyLabelMap().values();
        if(CollectionUtil.isNotEmpty(bindRelations)){
            List<AccessElm> accessElms = bindRelations.stream().map(t -> {
                AccessElm elm = new AccessElm();
                elm.setLabelPath(t.getChannelName() + "."+ t.getLabelGroupName() + "." + t.getLabelName());
                elm.setProperty(t.getPropertyName());
                return elm;
            }).collect(Collectors.toList());

            //标签
            Result<List<ConnectResult>> connectLabelResult = edgeGatewayCacheProxy.labelValue(
                    device.getEdgeGatewayId(),
                    tenant.getTenantId(),
                    accessElms
            );

            if(connectLabelResult.getSignal()){
                Map<String,String> map = new HashMap<>();
                bindRelations.forEach(l -> {
                    String key = l.getChannelName() + "." + l.getLabelGroupName() + "." + l.getLabelName();
                    if (map.containsKey(key)) {
                        map.put(key,map.get(key) + ":" + l.getPropertyName());
                    }else {
                        map.put(key,l.getPropertyName());
                    }
                });
//                    Map<String,String> propertyLabelMap = bindRelations.stream().collect(Collectors.toMap(l -> l.getChannelName() + "." + l.getLabelGroupName() + "." + l.getLabelName(),LabelBindRelation::getPropertyName));
                List<ConnectResult> list = connectLabelResult.getResult();
                if(list != null && list.size() > 0){
                    try{
                        for(ConnectResult item:list){
                            if (item.getOk()) {
                                String labelPath = item.getLabelPath();
                                Object value = item.getValue();
                                if (map.containsKey(labelPath)) {
                                    String[] split = map.get(labelPath).split(":");
                                    Arrays.stream(split).forEach(s -> {
                                        propertyMap.put(s, value);
                                    });
                                }
                            }
                        }
                    }catch(Exception e){
                        log.error(e.getMessage(),e);
                    }
                }
            }
        }
        propertyMap.put("id",device.getId());
        propertyMap.put("name",device.getName());
        if(device.getDescript()!=null){
            propertyMap.put("description",device.getDescript());
        }else{
            propertyMap.put("description","");
        }
//        propertyMap.put("status",DeviceStatusEnum.ONLINE.getValue());
        deviceTwin.setProperties(propertyMap,System.currentTimeMillis());

    }

    @Override
    public Result<List<Long>> getAllDeviceIdList(Long tenantId) {
        LambdaQueryWrapper<DeviceEntity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(DeviceEntity::getTenantId, tenantId);
        lqw.eq(DeviceEntity::getDeleted,0);
        List<DeviceEntity> deviceEntityList = deviceMapper.selectList(lqw);
        List<Long> ids = deviceEntityList.stream().map(DeviceEntity::getId).collect(Collectors.toList());
        if(CollectionUtil.isNotEmpty(ids)){
            return Result.ok(ids);
        }
        return Result.ok(new ArrayList<>());
    }

    private Result<DeviceEntity> getDeviceEntity(Long deviceId) {
        DeviceEntity deviceEntity = deviceMapper.selectById(deviceId);
        if(ObjectUtil.isNull(deviceEntity)){
            return Result.error("设备为空");
        }
        return Result.ok(deviceEntity);
    }

    @Override
    public Result<Boolean> writePropertyValue(WritePropertyValueDTO dto, TenantIsolation tenantIsolation) {
        return writePropertyValue(dto,tenantIsolation,false);
    }

    private Result<Boolean> writePropertyValue(WritePropertyValueDTO dto, TenantIsolation tenantIsolation,Boolean writeTwin) {
        Result<DeviceEntity> deviceEntityResult = getDeviceEntity(dto.getDeviceId());
        if(!deviceEntityResult.getSignal()){
            return Result.error(deviceEntityResult.getMessage());
        }
        DeviceEntity deviceEntity = deviceEntityResult.getResult();
        if(deviceEntity.getStatus() != 2){
            throw new BizException("请激活或启用该设备");
        }
        Result<List<ChannelEntity>> listResult = channelService.listChannelByEdgeGatewayId(deviceEntity.getEdgeGatewayId(),tenantIsolation.getTenantId(),true);
        Map<Long, ChannelRuntimeInfoField> channelRuntimeMap = new HashMap<>();
        if (listResult.getSignal()) {
            channelRuntimeMap = BeanUtilsIntensifier.collection2Map(listResult.getResult(), ChannelEntity::getId, ChannelEntity::getRuntimeInfo);
        }

        DeviceRuntimeMetadataField metadata=MemoryCache.getDeviceRuntimeMetadataField(deviceEntity.getId());
        if(metadata==null){
            log.warn("设备运行时不存在 设备id：{}",deviceEntity.getId());
            return Result.error("设备运行时不存在");
        }
        Map<String, PropertyMetadataItem> propertyMetadata =metadata.getProperties();
        if (!propertyMetadata.containsKey(dto.getPropertyName())) {
            log.warn("属性不存在：{}",dto.getPropertyName());
            return Result.error("该属性不存在");
        }
        PropertyMetadataItem propertyMetadataItem = propertyMetadata.get(dto.getPropertyName());
        if (propertyMetadataItem.getReadOnly()) {
            log.warn("设值失败，属性只读：{}",dto.getPropertyName());
            return Result.error("设值失败，属性只读");
        }

        //校验
        Result<Object> convertResult = SpiUtil.convertValue(propertyMetadataItem,dto.getWriteValue());
        if(!convertResult.getSignal()){
            return Result.error(convertResult.getMessage());
        }
        if (Optional.ofNullable(propertyMetadataItem.getLabelId()).isPresent()) {
            Boolean success = SpiUtil.writeProperty(edgeGatewaySpiProxy,propertyMetadataItem,
                channelRuntimeMap.get(propertyMetadataItem.getChannelId()),
                convertResult.getResult() ,deviceEntity.getEdgeGatewayId(), deviceEntity.getTenantId());
            if(writeTwin && success != null && success){
                UpData upData = Thing.buildUpData(deviceEntity.getId(), propertyMetadataItem, convertResult.getResult());
                mapping2DeviceHandler.processWriteTwin(deviceEntity.getEdgeGatewayId(), deviceEntity.getId(), deviceEntity.getTenantId(), upData);
            }
            return Result.ok(success);
        }
        // 直接修改内存
        UpData upData = Thing.buildUpData(deviceEntity.getId(),propertyMetadataItem,convertResult.getResult());
        mapping2DeviceHandler.processWriteTwin(deviceEntity.getEdgeGatewayId(), deviceEntity.getId(), deviceEntity.getTenantId(), upData);
        return Result.ok(true);
    }

    @Override
    public Result<List<Long>> deviceNamesToIds(Long tenantId, DeviceNamesToIdsDto dto) {
        Set<Long> tanentIds = Sets.newHashSet();
        tanentIds.add(tenantId);
        if(!StringUtils.isEmpty(dto.getCustomerIds())){
            String[] customerIds = dto.getCustomerIds().split(",");
            Arrays.stream(customerIds) .forEach(t -> tanentIds.add(Long.parseLong(t)));
        }
        List<DeviceEntity> list = deviceMapper.listDeviceByNameTanentIds(tanentIds, dto.getDeviceNames());
        if(list == null || list.size() <= 0){
            return Result.ok(new ArrayList<>());
        }
        List<Long> collect = list.stream().map(DeviceEntity::getId).collect(Collectors.toList());
        return Result.ok(collect);
    }

    @Override
    public Result<Map<Long, String>> deviceIdNameMapByNames(Long tenantId, List<String> deviceNames) {
        List<DeviceEntity> list = deviceMapper.listDeviceByNames(tenantId, deviceNames);
        if(list == null || list.size() <= 0){
            return Result.ok();
        }
        Map<Long, String> deviceIdNameMap = list.stream().collect(Collectors.toMap(DeviceEntity::getId, DeviceEntity::getName));
        return Result.ok(deviceIdNameMap);
    }

    @Override
    public Result<Void> jobExecute(List<DoServiceTaskRequest> doServiceTaskRequests, TenantIsolation tenantIsolation) {
        try{
            doServiceTaskRequests.forEach(req -> {
                DeviceServiceLogEntity logEntity = DeviceServiceLogEntity.builder()
                        .createTime(LocalDateTime.now()).callType(3).deviceId(req.getDeviceId())
                        .serviceName(req.getServiceName()).build();
                R r = doServiceTask(req.getTenantId(), req.getDeviceId(),
                        req.getServiceName(), req.getInput(), logEntity,true);
                try {
                    Thread.sleep(2000L);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
                log.debug("execute device[] service[] success",req.getDeviceId(),req.getServiceName());
            });
        }catch (Exception e){
            log.error("执行通用服务异常，异常原因：{}",e.getMessage());
        }
        return null;
    }


    @Override
    public Result<Set<Long>> deviceModelInherits(Long deviceId,Long tenantId) {
        Result<DeviceEntity> deviceEntityResult = getDeviceEntity(deviceId);
        if(!deviceEntityResult.getSignal()){
            return Result.error(deviceEntityResult.getMessage());
        }
        Set<Long> allModelId = new HashSet<>();
        CommonFetcher commonFetcher = commonFetcherFactory.buildCommonFetcher(tenantId);
        List<DeviceModelInheritEntity> deviceInheritEntityList = commonFetcher.list("device_id", deviceId, DeviceModelInheritEntity.class);
        if(CollectionUtil.isNotEmpty(deviceInheritEntityList)){
            Set<Long> modelIdList = deviceInheritEntityList.stream().map(DeviceModelInheritEntity::getInheritThingModelId).collect(Collectors.toSet());
            List<ThingModelInheritEntity> thingModelInheritEntityList = commonFetcher.list("tenant_id", tenantId, ThingModelInheritEntity.class);
            for(Long modelId :modelIdList){
                Result<Set<Long>> result = thingModelService.queryInheritByModelId(modelId,tenantId,thingModelInheritEntityList);
                if(result.getSignal() && CollectionUtil.isNotEmpty(result.getResult())){
                    allModelId.addAll(result.getResult());
                }
            }
        }
        return Result.ok(allModelId);
    }

    @Override
    public Result<List<DeviceEntity>> listDeviceByTag(Long tenantId, List<ListTagRequest> requestList) {
        Result<List<Long>> result = tagService.listTagId(tenantId,requestList);
        if(!result.getSignal()){
            return Result.error(result.getMessage());
        }
        List<Long> tagIds = result.getResult();
        List<String> tagIdList = new ArrayList<>();
        if(CollectionUtils.isEmpty(tagIds)){
            return Result.ok(new ArrayList<>());
        }
        for(Long id :tagIds){
            tagIdList.add(String.valueOf(id));
        }
        Result<List<TagBindRelationEntity>> listResult = tagBindRelationService.listByTagIds(tenantId,tagIdList,ResourceTypeEnum.DEVICE);
        if(!listResult.getSignal()){
            return Result.error(listResult.getMessage());
        }
        List<TagBindRelationEntity> tagBindRelationEntityList = listResult.getResult();
        if(CollectionUtils.isEmpty(tagBindRelationEntityList)){
            return Result.ok(new ArrayList<>());
        }
        List<Long> deviceIdList = tagBindRelationEntityList.stream().map(TagBindRelationEntity::getTargetId).collect(Collectors.toList());
        LambdaQueryChainWrapper<DeviceEntity> queryChainWrapper = new LambdaQueryChainWrapper<>(deviceMapper)
                .eq(DeviceEntity::getTenantId, tenantId)
                .in(DeviceEntity::getId, deviceIdList);
        List<DeviceEntity> list = queryChainWrapper.list();
        return Result.ok(list);
    }

    @Override
    public Result<Void> batchTags(Long tenantId, BatchDeviceTagDto batchDeviceTagDto) {
        for(Long id : batchDeviceTagDto.getIdList()){
            tagBindRelationService.saveList(tenantId, id,BeanUtilsIntensifier.getIds(batchDeviceTagDto.getTags(), TagRsp::getId), ResourceTypeEnum.DEVICE);
        }
        return Result.ok();
    }

    @Async
    @Override
    public Result<Void> ayncBatchUpdateDevice(Collection<DeviceEntity> deviceEntities,TenantIsolation tenant) {
        long start= System.currentTimeMillis();
        deviceMapper.updateSyncStatus(StatusEnum.ONLINE.getValue(),LocalDateTime.now(),SyncStatusEnum.HAVE_SYNC.getValue(),tenant.getTenantId());
        long spend=System.currentTimeMillis()-start;
        log.info("批量更新设备成功,tenantId:{},spend:{}ms", tenant.getTenantId(),spend);
        return Result.ok();
    }


    

}
