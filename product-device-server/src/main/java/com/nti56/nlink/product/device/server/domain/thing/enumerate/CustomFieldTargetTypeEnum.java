package com.nti56.nlink.product.device.server.domain.thing.enumerate;

import lombok.Getter;

public enum CustomFieldTargetTypeEnum {
    DRIVER_FIELD(1, "driverField", "协议字段"),
    MESSAGE_FIELD(2, "messageField", "协议包字段")
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    CustomFieldTargetTypeEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static CustomFieldTargetTypeEnum typeOfValue(Integer value){
        CustomFieldTargetTypeEnum[] values = CustomFieldTargetTypeEnum.values();
        for (CustomFieldTargetTypeEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static CustomFieldTargetTypeEnum typeOfName(String name){
        CustomFieldTargetTypeEnum[] values = CustomFieldTargetTypeEnum.values();
        for (CustomFieldTargetTypeEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static CustomFieldTargetTypeEnum typeOfNameDesc(String nameDesc){
        CustomFieldTargetTypeEnum[] values = CustomFieldTargetTypeEnum.values();
        for (CustomFieldTargetTypeEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }

}
