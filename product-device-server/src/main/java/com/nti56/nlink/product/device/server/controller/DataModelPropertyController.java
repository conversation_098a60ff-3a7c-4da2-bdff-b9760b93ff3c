package com.nti56.nlink.product.device.server.controller;

import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.BeanUtilsIntensifier;
import com.nti56.nlink.common.util.R;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.nlink.product.device.server.entity.DataModelPropertyEntity;
import com.nti56.nlink.product.device.server.model.DataModelPropertyBo;
import com.nti56.nlink.product.device.server.service.IDataModelPropertyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.function.Function;


/**
 * <p>
 * 数据模型属性表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2022-04-13 13:31:16
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/")
@Tag( name = "数据模型属性模块")
public class DataModelPropertyController {

    @Autowired
    IDataModelPropertyService service;

    @GetMapping("data-model-property/list")
    @Operation(summary = "获取列表" ,
            parameters  = {
                    @Parameter(name = "dataModelId",description = "所属数据模型id",required = true)
            }
    )
    public R list(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,Long dataModelId){
        Result<List<DataModelPropertyBo>> result = service.listByDataModelId(tenantIsolation, dataModelId);
        return R.result(result);
    }

    @PostMapping("data-model-property")
    @Operation(summary = "创建对象")
    public R create(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                    @Parameter(description = "对象") @RequestBody DataModelPropertyEntity entity
    ){
        Result<DataModelPropertyEntity> result = service.save(tenantIsolation, entity);
        return R.result(result);
    }

    @PutMapping("data-model-property")
    @Operation(summary = "更新")
    public R update(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                    @Parameter(description = "对象") @RequestBody DataModelPropertyEntity entity
    ){
        if (BeanUtilsIntensifier.checkBeanAndProperties(entity, DataModelPropertyEntity::getId)) {
            return R.error(ServiceCodeEnum.CODE_PARAM_ERROR);
        }
        Result<Integer> result = service.update(tenantIsolation, entity);
        return R.result(result);
    }

    @DeleteMapping("data-model-property/{entityId}")
    @Operation(summary = "删除对象")
    public R delete(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                    @Parameter(description = "目标ID") @PathVariable Long entityId
    ){
        Result<Integer> result = service.deleteById(tenantIsolation, entityId);
        return R.result(result);
        }

    @GetMapping("data-model-property/{entityId}")
    @Operation(summary = "获取对象")
    public R get(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                    @Parameter(description = "目标ID") @PathVariable Long entityId
    ){
        Result<DataModelPropertyEntity> result = service.getById(tenantIsolation, entityId);
        return R.result(result);
    }
    
}
