package com.nti56.nlink.product.device.server.controller;

import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.PageParam;
import com.nti56.nlink.common.util.R;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.enums.EmailProtocolEnum;
import com.nti56.nlink.product.device.server.enums.NotifyEnum;
import com.nti56.nlink.product.device.server.enums.ProviderEnum;
import com.nti56.nlink.product.device.server.model.channel.dto.CreateNotifyChannelDTO;
import com.nti56.nlink.product.device.server.model.channel.dto.EditNotifyChannelDTO;
import com.nti56.nlink.product.device.server.model.channel.dto.NotifyChannelDTO;
import com.nti56.nlink.product.device.server.service.INotifyChannelService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/3/22 17:12<br/>
 * @since JDK 1.8
 */
@RestController
@RequestMapping("channels")
@Tag( name = "渠道模块")
public class NotifyChannelController {

    @Autowired
    private INotifyChannelService channelService;

    @PostMapping()
    @Operation(summary = "新增渠道" )
    public R createChannel(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, @Validated @RequestBody CreateNotifyChannelDTO dto){
         return R.result(channelService.createChannel(dto,tenantIsolation));
    }

    @GetMapping("list")
    @Operation(summary = "查询全部渠道" )
    public R listChannel(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, @Validated NotifyChannelDTO dto){
        return R.result(channelService.listChannel( dto,tenantIsolation));
    }

    @GetMapping("page")
    @Operation(summary = "分页查询渠道消息" )
    public R pageChannel(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,PageParam pageParam,@Validated NotifyChannelDTO dto){
        return R.result(channelService.pageChannel(pageParam,dto,tenantIsolation));
    }


    @GetMapping("{id}")
    @Operation(summary = "根据id查询渠道信息" )
    public R getChannel(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@PathVariable Long id){
        return R.result(Result.ok(channelService.getByIdAndTenantIsolation(id,tenantIsolation)));
    }


    @PutMapping("{id}")
    @Operation(summary = "根据id更新渠道消息" )
    public R editChannel(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@Validated @RequestBody EditNotifyChannelDTO dto){
        return R.result(channelService.editChannel(dto,tenantIsolation));
    }


    @DeleteMapping("{id}")
    @Operation(summary = "逻辑删除渠道信息" )
    public R deleteChannel(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@PathVariable Long id){
        return R.result(channelService.deleteChannel(id,tenantIsolation));
    }

    @GetMapping("notifyType")
    @Operation(summary = "获取通知类型" )
    public R getNotifyType(){
        return R.ok(NotifyEnum.toList());
    }


    @GetMapping("providers")
    @Operation(summary = "获取服务商" )
    public R getProviders(){
        return R.ok(ProviderEnum.toList());
    }

    @GetMapping("email/protocol")
    @Operation(summary = "获取协议枚举" )
    public R getEmailProtocol(){
        return R.ok(EmailProtocolEnum.toList());
    }

}
