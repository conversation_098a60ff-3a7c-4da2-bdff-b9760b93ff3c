package com.nti56.nlink.product.device.server.config;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nti56.nlink.product.device.server.listener.NoChangeKeyExpireListener;
import com.nti56.nlink.product.device.server.util.redis.RedisRepository;
import com.nti56.nlink.product.device.server.util.redis.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.interceptor.KeyGenerator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.listener.PatternTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName RedisConfig
 * @date 2022/7/25 15:47
 * @Version 1.0
 */
@EnableConfigurationProperties({RedisProperties.class, CacheRedisConfigProperties.class})
@Configuration
@EnableCaching
public class RedisConfig {
    
    @Autowired
    private CacheRedisConfigProperties cacheRedisConfigProperties;

    public RedisConfig() {
    }
    
    @Value("${spring.redis.database}")
    private Integer redisDatabase;

    @Autowired
    private NoChangeKeyExpireListener noChangeKeyExpireListener;

    @Bean
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory factory) {
        RedisTemplate<String, Object> redisTemplate = new RedisTemplate();
        redisTemplate.setConnectionFactory(factory);
        Jackson2JsonRedisSerializer<Object> jackson2JsonRedisSerializer = new Jackson2JsonRedisSerializer(Object.class);
//        ObjectMapper om = new ObjectMapper();
//        om.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
//        om.enableDefaultTyping(ObjectMapper.DefaultTyping.NON_FINAL);
//        jackson2JsonRedisSerializer.setObjectMapper(om);
        StringRedisSerializer stringRedisSerializer = new StringRedisSerializer();
        redisTemplate.setKeySerializer(stringRedisSerializer);
        redisTemplate.setHashKeySerializer(stringRedisSerializer);
        redisTemplate.setValueSerializer(RedisSerializer.java());
        redisTemplate.setHashValueSerializer(RedisSerializer.java());
        redisTemplate.afterPropertiesSet();
        return redisTemplate;
    }

    @Bean
    @ConditionalOnMissingBean
    public RedisRepository redisRepository(RedisTemplate<String, Object> redisTemplate) {
        return new RedisRepository(redisTemplate);
    }

    @Bean(
            initMethod = "init",
            destroyMethod = "destroy"
    )
    @ConditionalOnMissingBean(
            name = {"cloudCacheUtil"}
    )
    public RedisUtil cloudCacheUtil(RedisTemplate<String, Object> redisTemplate) {
        return new RedisUtil(redisTemplate, "User");
    }

    @Bean(
            initMethod = "init",
            destroyMethod = "destroy"
    )
    @ConditionalOnMissingBean(
            name = {"cacheUtil"}
    )
    public RedisUtil cacheUtil(RedisTemplate<String, Object> redisTemplate) {
        return new RedisUtil(redisTemplate, "dataCenter");
    }

    @Bean(
            name = {"cacheManager"}
    )
    @Primary
    public CacheManager cacheManager(RedisConnectionFactory redisConnectionFactory) {
        RedisCacheConfiguration difConf = this.getDefConf().entryTtl(Duration.ofHours(1L));
        int configSize = this.cacheRedisConfigProperties.getConfigs() == null ? 0 : this.cacheRedisConfigProperties.getConfigs().size();
        Map<String, RedisCacheConfiguration> redisCacheConfigurationMap = new HashMap(configSize);
        if (configSize > 0) {
            this.cacheRedisConfigProperties.getConfigs().forEach((e) -> {
                RedisCacheConfiguration conf = this.getDefConf().entryTtl(Duration.ofSeconds(e.getSecond()));
                redisCacheConfigurationMap.put(e.getKey(), conf);
            });
        }

        return RedisCacheManager.builder(redisConnectionFactory).cacheDefaults(difConf).withInitialCacheConfigurations(redisCacheConfigurationMap).build();
    }

    @Bean
    public KeyGenerator keyGenerator() {
        return (target, method, objects) -> {
            StringBuilder sb = new StringBuilder();
            sb.append(target.getClass().getName());
            sb.append(":" + method.getName() + ":");
            Object[] var4 = objects;
            int var5 = objects.length;

            for(int var6 = 0; var6 < var5; ++var6) {
                Object obj = var4[var6];
                sb.append(obj.toString());
            }

            return sb.toString();
        };
    }

    private RedisCacheConfiguration getDefConf() {
        return RedisCacheConfiguration.defaultCacheConfig().disableCachingNullValues().computePrefixWith((cacheName) -> {
            return "cache".concat(":").concat(cacheName).concat(":");
        }).serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer())).serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(RedisSerializer.java()));
    }

    @Bean
    RedisMessageListenerContainer container(RedisConnectionFactory factory) {
        
        RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        container.setConnectionFactory(factory);
        String pattern = "__keyevent@" + redisDatabase + "__:expired";
        container.addMessageListener(noChangeKeyExpireListener, new PatternTopic(pattern));
        return container;
    }

}
