package com.nti56.nlink.product.device.server.domain.thing.label;

import com.nti56.nlink.product.device.client.model.dto.json.enumerate.ThingDataTypeEnum;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.Snap7DataTypeEnum;

/**
 * 类说明: s7协议数据类型和java数据类型匹配领域对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-07-13 16:33:35
 * @since JDK 1.8
 */
public class Snap7TypeMatcher {
    
    //匹配返回true
    public static boolean checkMatch(Snap7DataTypeEnum addressDataType, ThingDataTypeEnum dataType){
        if(addressDataType == null){
            return false;
        }
        switch (addressDataType) {
            case BIT: {
                if(ThingDataTypeEnum.BOOLEAN.equals(dataType)){
                    return true;
                }
                return false;
            }
            case BYTE: {
                if(ThingDataTypeEnum.BYTE.equals(dataType)){
                    return true;
                }
                if(ThingDataTypeEnum.CHAR.equals(dataType)){
                    return true;
                }
                if(ThingDataTypeEnum.STRING.equals(dataType)){
                    return true;
                }
                if(ThingDataTypeEnum.DOUBLE.equals(dataType)){
                    return true;
                }
                return false;
            }
            case WORD: {
                if(ThingDataTypeEnum.WORD.equals(dataType)){
                    return true;
                }
                if(ThingDataTypeEnum.SHORT.equals(dataType)){
                    return true;
                }
                if(ThingDataTypeEnum.BCD.equals(dataType)){
                    return true;
                }
                return false;
            }
            case DWORD: {
                if(ThingDataTypeEnum.DWORD.equals(dataType)){
                    return true;
                }
                if(ThingDataTypeEnum.LONG.equals(dataType)){
                    return true;
                }
                if(ThingDataTypeEnum.FLOAT.equals(dataType)){
                    return true;
                }
                if(ThingDataTypeEnum.LBCD.equals(dataType)){
                    return true;
                }
                return false;
            }
            case INT: {
                if(ThingDataTypeEnum.WORD.equals(dataType)){
                    return true;
                }
                if(ThingDataTypeEnum.SHORT.equals(dataType)){
                    return true;
                }
                if(ThingDataTypeEnum.BCD.equals(dataType)){
                    return true;
                }
                return false;
            }
            case DINT: {
                if(ThingDataTypeEnum.DWORD.equals(dataType)){
                    return true;
                }
                if(ThingDataTypeEnum.LONG.equals(dataType)){
                    return true;
                }
                if(ThingDataTypeEnum.FLOAT.equals(dataType)){
                    return true;
                }
                if(ThingDataTypeEnum.LBCD.equals(dataType)){
                    return true;
                }
                return false;
            }
            case REAL: {
                if(ThingDataTypeEnum.DWORD.equals(dataType)){
                    return true;
                }
                if(ThingDataTypeEnum.LONG.equals(dataType)){
                    return true;
                }
                if(ThingDataTypeEnum.FLOAT.equals(dataType)){
                    return true;
                }
                if(ThingDataTypeEnum.LBCD.equals(dataType)){
                    return true;
                }
                return false;
            }
            default: {
                return false;
            }
        }
    }

}
