package com.nti56.nlink.product.device.server.domain.thing.enumerate;

import lombok.Getter;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-07-08 15:25:40
 * @since JDK 1.8
 */
public enum ReportTypeEnum {
    NO_REPORT(0, "noReport", "不上报"),
    CHANGE_REPORT(1, "changeReport", "改变上报"),
    KEEP_REPORT(2, "keepReport ", "持续上报"),
    WRITE_TWIN(3, "writeTwin", "些孪生")
    ;
    
    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    ReportTypeEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static ReportTypeEnum typeOfValue(Integer value){
        ReportTypeEnum[] values = ReportTypeEnum.values();
        for (ReportTypeEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static ReportTypeEnum typeOfName(String name){
        ReportTypeEnum[] values = ReportTypeEnum.values();
        for (ReportTypeEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static ReportTypeEnum typeOfNameDesc(String nameDesc){
        ReportTypeEnum[] values = ReportTypeEnum.values();
        for (ReportTypeEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
