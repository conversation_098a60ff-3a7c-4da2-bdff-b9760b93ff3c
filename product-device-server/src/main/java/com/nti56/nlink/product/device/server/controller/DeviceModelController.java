package com.nti56.nlink.product.device.server.controller;

import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.R;
import com.nti56.nlink.product.device.server.model.deviceLog.PropertyLogConditionBo;
import com.nti56.nlink.product.device.server.model.product.dto.*;
import com.nti56.nlink.product.device.server.service.IDeviceModelService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 类说明: 设备模型controller
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 13:32:57
 * @since JDK 1.8
 */
@RestController
@RequestMapping("device/model/")
@Tag(name = "设备模型模块")
@Slf4j
public class DeviceModelController {

    @Autowired
    private IDeviceModelService deviceModelService;

    @PutMapping("{id}")
    @Operation(summary = "修改设备模型")
    public R editProductModel(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@Validated @RequestBody EditDeviceModelDTO dto){
        log.info("修改设备模型：{},租户信息：{}", dto, tenantIsolation);
        return R.result(deviceModelService.editDeviceModel(dto,tenantIsolation));
    }

    @GetMapping("{id}")
    @Operation(summary = "根据id获取设备模型")
    public R getDeviceModel(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@PathVariable Long id){
        log.info("获取设备模型：{},租户信息：{}", id, tenantIsolation);
        return R.result(deviceModelService.getDeviceModel(id,tenantIsolation));
    }

    @GetMapping("select/list/{id}")
    @Operation(summary = "根据id获取设备模型附属下拉框列表")
    public R getDeviceModelSelectList(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@PathVariable Long id){
        log.info("获取设备模型附属下拉框列表：{},租户信息：{}", id, tenantIsolation);
        return R.result(deviceModelService.getDeviceModelSelectList(id,tenantIsolation));
    }



    @GetMapping("{id}/4/service")
    @Operation(summary = "根据id获取设备模型")
    public R getDeviceModel4Service(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@PathVariable Long id){
        log.info("获取设备模型：{},租户信息：{}", id, tenantIsolation);
        return R.result(deviceModelService.getDeviceModel4Service(id,tenantIsolation));
    }

    @PostMapping("select/properties")
    @Operation(summary = "根据设备ID,数据类型，获取属性")
    public R selectProperties(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, @RequestBody PropertyLogConditionBo condition){
        log.debug("根据设备ID：{},数据类型：{}，获取属性",condition.getDeviceIds(),condition.getDataType());
        return R.result(deviceModelService.getPropertiesByDeviceIdsAndType(tenantIsolation.getTenantId(),condition));
    }
}
