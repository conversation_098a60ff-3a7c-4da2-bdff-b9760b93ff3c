package com.nti56.nlink.product.device.server.controller;

import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.PageParam;
import com.nti56.nlink.common.util.R;
import com.nti56.nlink.product.device.server.model.engineering.dto.CreateEngineeringDocDTO;
import com.nti56.nlink.product.device.server.service.IEngineeringDocService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/11/4 13:12<br/>
 * @since JDK 1.8
 */
@RestController
@RequestMapping("engineering/doc")
@Tag( name = "工程文件")
public class EngineeringDocController {

    @Autowired
    private IEngineeringDocService engineeringDocService;


    @GetMapping()
    @Operation(summary = "分页获取工程文件列表" )
    public R listEngineeringDoc(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, PageParam pageParam){
        return R.result(engineeringDocService.pageEngineeringDoc(tenantIsolation,pageParam));
    }

    @PostMapping("save")
    @Operation(summary = "保存实时当前工程文件" )
    public R createEngineeringDoc(HttpServletResponse response ,@RequestHeader("ot_headers") TenantIsolation tenantIsolation, @RequestBody @Validated CreateEngineeringDocDTO dto){
        return R.result(engineeringDocService.createEngineeringDoc(response,tenantIsolation,dto));
    }

    @GetMapping("download/{id}")
    @Operation(summary = "下载历史工程文件" )
    public void downloadEngineeringDoc(HttpServletResponse response ,@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@PathVariable Long id){
        engineeringDocService.downloadEngineeringDoc(response,tenantIsolation ,id);
    }

    @PostMapping("import")
    @Operation(summary = "导入工程文件" )
    public R importEngineeringDoc(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@RequestPart String password,MultipartFile file, String desc){
        return R.result(engineeringDocService.importEngineeringDoc(tenantIsolation,file,password,desc));
    }




}
