package com.nti56.nlink.product.device.server.controller;

import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.R;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.client.model.dto.json.device.ChannelElm;
import com.nti56.nlink.product.device.server.feign.IEdgeGatewaySpiProxy;
import com.nti56.nlink.product.device.server.model.ConnectLabelDto;
import com.nti56.nlink.product.device.server.model.ConnectResult;
import com.nti56.nlink.product.device.server.model.edgegateway.WriteParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/tmp")
public class TmpProxyController {

    @Autowired
    IEdgeGatewaySpiProxy edgeGatewaySpiProxy;

    @PostMapping("/connect-label")
    public R connectLabel(Long edgeGatewayId, 
                            @RequestHeader("ot_headers") TenantIsolation tenant,
                            @RequestBody ConnectLabelDto dto){
        Result<List<ConnectResult>> result = edgeGatewaySpiProxy.connectLabel(
            edgeGatewayId, tenant.getTenantId(), dto.getLabelList(), dto.getChannelList()
        );
        return R.result(result);
    }

    @PostMapping("/connect-channel")
    public R connectChannel(Long edgeGatewayId, 
                            @RequestHeader("ot_headers") TenantIsolation tenant,
                            @RequestBody List<ChannelElm> channelList){
        Result<List<ConnectResult>> result = edgeGatewaySpiProxy.connectChannel(edgeGatewayId, tenant.getTenantId(), channelList);
        return R.result(result);
    }

    @PostMapping("/write-bool")
    public R writeBool(Long labelId, Boolean value, Long edgeGatewayId){
        Result<Void> result = edgeGatewaySpiProxy.writeBool(edgeGatewayId, null, null, value);
        return R.result(result);
    }

    @PostMapping("/write-short")
    public R writeShort(Long labelId, Short value, Long edgeGatewayId){
        Result<Void> result = edgeGatewaySpiProxy.writeShort(edgeGatewayId, null, null, value);
        return R.result(result);
    }

    @PostMapping("/write-int")
    public R writeInt(Long labelId, Integer value, Long edgeGatewayId){
        Result<Void> result = edgeGatewaySpiProxy.writeInt(edgeGatewayId, null, null, value);
        return R.result(result);
    }

    @PostMapping("/write-float")
    public R writeFloat(Long labelId, Float value, Long edgeGatewayId){
        Result<Void> result = edgeGatewaySpiProxy.writeFloat(edgeGatewayId, null, null, value);
        return R.result(result);
    }

    @PostMapping("/write-string")
    public R writeString(Long labelId, String value, Long edgeGatewayId){
        Result<Void> result = edgeGatewaySpiProxy.writeString(edgeGatewayId, null, null, value);
        return R.result(result);
    }

    @PostMapping(value = "/write-byte")
    public R writeByte(Long labelId, @RequestBody byte[] value, Long edgeGatewayId){
        Result<Void> result = edgeGatewaySpiProxy.writeByte(edgeGatewayId, null, null, value[0]);
        return R.result(result);
    }

    @PostMapping("/write-bool-array")
    public R writeBoolArray(Long labelId, @RequestBody Boolean[] value, Long edgeGatewayId){
        Result<Void> result = edgeGatewaySpiProxy.writeBoolArray(edgeGatewayId, null, null, value);
        return R.result(result);
    }

    @PostMapping("/write-short-array")
    public R writeShortArray(Long labelId, @RequestBody Short[] value, Long edgeGatewayId){
        Result<Void> result = edgeGatewaySpiProxy.writeShortArray(edgeGatewayId, null, null, value);
        return R.result(result);
    }

    @PostMapping("/write-int-array")
    public R writeIntArray(Long labelId, @RequestBody Integer[] value, Long edgeGatewayId){
        Result<Void> result = edgeGatewaySpiProxy.writeIntArray(edgeGatewayId, null, null, value);
        return R.result(result);
    }

    @PostMapping("/write-float-array")
    public R writeFloatArray(Long labelId, @RequestBody Float[] value, Long edgeGatewayId){
        Result<Void> result = edgeGatewaySpiProxy.writeFloatArray(edgeGatewayId, null, null, value);
        return R.result(result);
    }

    @PostMapping("/write-string-array")
    public R writeStringArray(Long labelId, @RequestBody String[] value, Long edgeGatewayId){
        Result<Void> result = edgeGatewaySpiProxy.writeStringArray(edgeGatewayId, null, null, value);
        return R.result(result);
    }

    @PostMapping("/write-byte-array")
    public R writeByteArray(Long labelId, @RequestBody byte[] value, Long edgeGatewayId){
        Result<Void> result = edgeGatewaySpiProxy.writeByteArray(edgeGatewayId, null, null, value);
        return R.result(result);
    }

    @PostMapping("/multi-write")
    public R multiWrite(@RequestBody List<WriteParam> params, Long edgeGatewayId){
        Result<List<ConnectResult>> result = edgeGatewaySpiProxy.multiWrite(edgeGatewayId, null, params);
        return R.result(result);
    }
}
