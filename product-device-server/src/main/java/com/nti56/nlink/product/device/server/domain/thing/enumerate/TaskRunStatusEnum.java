package com.nti56.nlink.product.device.server.domain.thing.enumerate;

import lombok.Getter;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-24 14:07:07
 * @since JDK 1.8
 */
public enum TaskRunStatusEnum {
    STOP(0, "stop", "停止"), 
    RUNNING(1, "running", "运行中")
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    TaskRunStatusEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static TaskRunStatusEnum typeOfValue(Integer value){
        TaskRunStatusEnum[] values = TaskRunStatusEnum.values();
        for (TaskRunStatusEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static TaskRunStatusEnum typeOfName(String name){
        TaskRunStatusEnum[] values = TaskRunStatusEnum.values();
        for (TaskRunStatusEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static TaskRunStatusEnum typeOfNameDesc(String nameDesc){
        TaskRunStatusEnum[] values = TaskRunStatusEnum.values();
        for (TaskRunStatusEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
