package com.nti56.nlink.product.device.server.domain.thing.enumerate;

import lombok.Getter;

/**
 * 类说明: 下载状态
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023-3-22 14:36:36
 * @since JDK 1.8
 */
public enum DownloadStatusEnum {
    DOWNLOAD_INIT("0", "downloadInit", "downloadInit"),
    DOWNLOADING("1", "downloading", "downloading"),
    DOWNLOAD_SUCCESSFUL("2", "downloadSuccessful", "downloadSuccessful"),
    DOWNLOAD_FAIL("3", "downloadFail", "downloadFail"),
    UN_ZIP_SUCCESSFUL("4", "unZipSuccessful", "unZipSuccessful"),
    UN_ZIP_FAIL("5", "unZipFail", "unZipFail"),
    UPGRADING("6", "upgrading", "upgrading"),
    ;

    @Getter
    private String value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    DownloadStatusEnum(String value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static DownloadStatusEnum typeOfValue(String value){
        DownloadStatusEnum[] values = DownloadStatusEnum.values();
        for (DownloadStatusEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static String getNameByValue(String value){
        DownloadStatusEnum[] values = DownloadStatusEnum.values();
        for (DownloadStatusEnum v : values) {
            if (v.value.equals(value)) {
                return v.name;
            }
        }
        return null;
    }

    public static DownloadStatusEnum typeOfName(String name){
        DownloadStatusEnum[] values = DownloadStatusEnum.values();
        for (DownloadStatusEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static DownloadStatusEnum typeOfNameDesc(String nameDesc){
        DownloadStatusEnum[] values = DownloadStatusEnum.values();
        for (DownloadStatusEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }

}
