package com.nti56.nlink.product.device.server.domain.thing.enumerate;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

import lombok.Getter;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-24 14:06:22
 * @since JDK 1.8
 */
public enum Snap7DataTypeEnum {
    BIT(1, "BIT", "BIT", new HashSet<String>(Arrays.asList(new String[]{
        "DBX", "X", "BIT"
    }))),
    BYTE(2, "BYTE", "BYTE", new HashSet<String>(Arrays.asList(new String[]{
        "DBB", "B", "BYTE", "STRING", "S"
    }))),
    CHAR(3, "CHAR", "CHAR", new HashSet<String>(Arrays.asList(new String[]{
        "C", "CHAR"
    }))),
    WORD(4, "WORD", "WORD", new HashSet<String>(Arrays.asList(new String[]{
        "DBW", "W", "WORD"
    }))),
    INT(5, "INT", "INT", new HashSet<String>(Arrays.asList(new String[]{
        "I", "INT"
    }))),
    DWORD(6, "DWORD", "DWORD", new HashSet<String>(Arrays.asList(new String[]{
        "DBD", "D", "DWORD"
    }))),
    DINT(7, "DINT", "DINT", new HashSet<String>(Arrays.asList(new String[]{
        "DINT"
    }))),
    REAL(8, "REAL", "REAL", new HashSet<String>(Arrays.asList(new String[]{
        "REAL"
    }))),
    COUNTER(9, "COUNTER", "COUNTER", new HashSet<String>(Arrays.asList(new String[]{
        "COUNTER"
    }))),
    TIMER(10, "TIMER", "TIMER", new HashSet<String>(Arrays.asList(new String[]{
        "TIMER"
    })))
    ;
    
    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    @Getter
    private Set<String> alias;

    Snap7DataTypeEnum(Integer value, String name, String nameDesc, Set<String> alias) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
        this.alias = alias;
    }

    public static Snap7DataTypeEnum typeOfValue(Integer value){
        Snap7DataTypeEnum[] values = Snap7DataTypeEnum.values();
        for (Snap7DataTypeEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static Snap7DataTypeEnum typeOfName(String name){
        Snap7DataTypeEnum[] values = Snap7DataTypeEnum.values();
        for (Snap7DataTypeEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static Snap7DataTypeEnum typeOfAlias(String alias){
        Snap7DataTypeEnum[] values = Snap7DataTypeEnum.values();
        for (Snap7DataTypeEnum v : values) {
            if (v.alias.contains(alias)) {
                return v;
            }
        }
        return null;
    }
    public static Snap7DataTypeEnum typeOfNameDesc(String nameDesc){
        Snap7DataTypeEnum[] values = Snap7DataTypeEnum.values();
        for (Snap7DataTypeEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
