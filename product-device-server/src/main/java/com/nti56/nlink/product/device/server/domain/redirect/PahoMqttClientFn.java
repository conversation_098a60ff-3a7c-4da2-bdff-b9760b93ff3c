package com.nti56.nlink.product.device.server.domain.redirect;

import com.nti56.nlink.common.util.Result;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.mqttv5.client.MqttClient;
import org.eclipse.paho.mqttv5.client.MqttConnectionOptions;
import org.eclipse.paho.mqttv5.common.MqttException;


@Slf4j
public class PahoMqttClientFn {

    MqttClient client = null;



    public Result<Boolean> connect(String url,String clientId, MqttConnectionOptions options) throws MqttException {
        // 创建MQTT客户端实例
        client = new MqttClient(url, clientId);
        // 创建连接选项
        try {
            client.connect(options);
        } catch (MqttException e) {
            log.error("测试连接失败，错误信息：{}",e.getMessage());
            return Result.ok(false);
        }finally {
            if(client.isConnected()){
                client.disconnect();
            }
        }
        return Result.ok(true);
    }


    public void disconnect() {
        if (client != null) {
            // 断开连接
            try {
                client.disconnect();
            } catch (MqttException e) {
                log.error(e.getMessage(),e);
            }
        }
    }
}
