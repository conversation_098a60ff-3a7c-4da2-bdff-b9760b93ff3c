package com.nti56.nlink.product.device.server.controller;

import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.PageParam;
import com.nti56.nlink.common.util.R;
import com.nti56.nlink.product.device.server.model.connectorItem.dto.CreateConnectorItemDTO;
import com.nti56.nlink.product.device.server.model.connectorItem.dto.EditConnectorItemDTO;
import com.nti56.nlink.product.device.server.model.connectorItem.dto.QueryConnectorItemDTO;
import com.nti56.nlink.product.device.server.service.IConnectorItemService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 类说明: 连接器详情项controller
 * <AUTHOR>
 * @version 1.0
 * @since JDK 1.8
 */
@RestController
@RequestMapping("connector-item")
@Tag(name = "连接器详情项")
public class ConnectorItemController {
    
    @Autowired
    private IConnectorItemService connectorItemService;
    
    @GetMapping("page")
    @Operation(summary = "获取连接器详情项分页")
    public R pageConnectorItem(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, PageParam pageParam, QueryConnectorItemDTO queryConnectorItemDTO) {
        return R.result(connectorItemService.pageConnectorItem(pageParam, queryConnectorItemDTO, tenantIsolation));
    }
    
    @GetMapping("{id}")
    @Operation(summary = "获取连接器详情项")
    public R getConnectorItemInfo(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, @PathVariable Long id) {
        return R.result(connectorItemService.getConnectorItemInfo(id));
    }
    
    @PostMapping("")
    @Operation(summary = "新增连接器详情项")
    public R createConnectorItem(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, @RequestBody @Validated CreateConnectorItemDTO createConnectorItemDTO) {
        return R.result(connectorItemService.createConnectorItem(createConnectorItemDTO, tenantIsolation));
    }
    
    @PutMapping("{id}")
    @Operation(summary = "修改连接器详情项")
    public R editConnectorItem(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, @RequestBody @Validated EditConnectorItemDTO editConnectorItemDTO) {
        return R.result(connectorItemService.editConnectorItem(editConnectorItemDTO, tenantIsolation));
    }
    
    @DeleteMapping("{id}")
    @Operation(summary = "逻辑删除连接器详情项")
    public R deleteConnectorItem(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, @PathVariable Long id) {
        return R.result(connectorItemService.deleteConnectorItem(id, tenantIsolation));
    }
}
