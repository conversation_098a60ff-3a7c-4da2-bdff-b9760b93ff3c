package com.nti56.nlink.product.device.server.domain.event;

import org.springframework.context.ApplicationEvent;

/**
 * 类说明：
 *
 * @ClassName InvokeRedirectEvent
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/6/27 16:33
 * @Version 1.0
 */

public class InvokeRedirectEvent extends ApplicationEvent {

    private final Long redirectId;
    private final Long tenantId;

    public InvokeRedirectEvent(Object source, Long redirectId, Long tenantId) {
        super(source);
        this.redirectId = redirectId;
        this.tenantId = tenantId;
    }

    public Long getInstanceRedirectEntity() {
        return redirectId;
    }



    public Long getTenantId(){
        return tenantId;
    }

}
