package com.nti56.nlink.product.device.server.domain.thing.enumerate;

import lombok.Getter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 类说明: 状态枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 13:33:25
 * @since JDK 1.8
 */
public enum DeviceOptionEnum {
    DELETE(4, "DELETE", "设备删除操作"),
    OFFLINE(3, "offline", "设备停用操作"),
    SYNC(1, "SYNC", "设备同步操作"),
    ONLINE(2, "online", "设备上线操作"),
    SYNC_ALL(5, "SYNCAll", "设备全部同步操作"),

            ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    DeviceOptionEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static DeviceOptionEnum typeOfValue(Integer value){
        DeviceOptionEnum[] values = DeviceOptionEnum.values();
        for (DeviceOptionEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static DeviceOptionEnum typeOfName(String name){
        DeviceOptionEnum[] values = DeviceOptionEnum.values();
        for (DeviceOptionEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static DeviceOptionEnum typeOfNameDesc(String nameDesc){
        DeviceOptionEnum[] values = DeviceOptionEnum.values();
        for (DeviceOptionEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }


    public static List toList(){
        List<Map> result = new ArrayList<>();
        DeviceOptionEnum[] values = DeviceOptionEnum.values();
        Map<String,Object> map ;
        for (DeviceOptionEnum v : values) {
            map = new HashMap<>();
            map.put("name",v.name);
            map.put("value",v.value);
            map.put("nameDesc",v.nameDesc);
            result.add(map);
        }
        return result;
    }
}
