package com.nti56.nlink.product.device.server.domain.thing.channel;

import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.client.model.dto.json.ChannelRuntimeInfoField;
import com.nti56.nlink.product.device.client.model.dto.json.device.ChannelElm;
import com.nti56.nlink.product.device.server.entity.ChannelParamEntity;
import com.nti56.nlink.product.device.server.util.RegexUtil;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * 类说明: fins tcp驱动通道参数领域对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023-2-6 09:26:25
 * @since JDK 1.8
 */
@Getter
public class FinsChannelParam extends ChannelParam{
    private String ip;
    private Integer port;
    private Integer unitNo;
    private String endianness;

    public static final String[] requiredParam = new String[]{
            "ip::true",
            "port::true",
            "unitNo:0:true",
            "endianness:CDAB:true:ABCD,BADC,CDAB,DCBA:大小端配置",
            "reconnectGapMs:3000:true::断线重连间隔（毫秒）",
            "maxConnection:1:true::通道最多连接数",
            "delayIdleMs:0:true::延迟空闲时间（毫秒）"
    };

    
    public static Result<ChannelParam> checkParam(List<ChannelParamEntity> channelParamList){
        
        FinsChannelParam param = new FinsChannelParam();

        Result<Map<String, String>> baseResult = ChannelParam.checkBase(
            param, requiredParam, channelParamList
        );
        if(!baseResult.getSignal()){
            return Result.error(baseResult.getMessage());
        }
        Map<String, String> paramMap = baseResult.getResult();

        //ip
        String ipStr = paramMap.get("ip");
        if(ipStr == null){
            return Result.error("通道参数缺少ip");
        }
        if(!RegexUtil.checkIpv4(ipStr)){
            return Result.error("通道ip格式错误，ip:" + ipStr);
        }
        param.ip = ipStr;

        //port
        String portStr = paramMap.get("port");
        if(portStr == null){
            return Result.error("通道参数缺少port");
        }
        if(!RegexUtil.checkPort(portStr)){
            return Result.error("通道port格式错误，port:" + portStr);
        }
        param.port = Integer.parseInt(portStr);

        //channelKey
        param.channelKey = param.ip + param.port;

        //unitNo
        String unitNoStr = paramMap.get("unitNo");
        if(StringUtils.isBlank(unitNoStr)){
            return Result.error("通道参数缺少unitNo");
        }
        if(!RegexUtil.checkInt(unitNoStr)){
            return Result.error("unitNo 格式错误");
        }
        try {
            param.unitNo = Integer.parseInt(unitNoStr);
        }catch (Exception e){
            return Result.error("unitNo 格式错误");
        }

        //endiannessStr
        String endiannessStr = paramMap.get("endianness");
        if(endiannessStr == null){
            return Result.error("通道参数缺少endianness");
        }
        param.endianness = endiannessStr;

        return Result.ok(param);

    }

    @Override
    public void processRuntimeInfo(ChannelRuntimeInfoField info) {
        processBaseRuntimeInfo(info);

        info.setIp(ip);
        info.setPort(port);
        info.setUnitNo(unitNo);
        info.setEndianness(endianness);
    }

    @Override
    public void processChannelElm(ChannelElm channelElm) {
        processBaseChannelElm(channelElm);

        channelElm.setIp(ip);
        channelElm.setPort(port);
        channelElm.setUnitNo(unitNo);
        channelElm.setEndianness(endianness);
    }

    
}
