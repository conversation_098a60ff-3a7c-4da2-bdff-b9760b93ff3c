package com.nti56.nlink.product.device.server.proxy;

import com.alibaba.fastjson.JSONObject;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.MqttTopicEnum;
import com.nti56.nlink.product.device.server.feign.IEdgeGatewayNotAssignControlProxy;
import com.nti56.nlink.product.device.server.util.MqttProxyEventBusUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 类说明: 边缘网关未分配控制代理 - EventBus实现
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024-01-01 00:00:00
 * @since JDK 1.8
 */
@Component
public class EdgeGatewayNotAssignControlProxy implements IEdgeGatewayNotAssignControlProxy {

    @Autowired
    private MqttProxyEventBusUtil mqttProxyEventBusUtil;

    @Override
    public Result<Void> connectOt(String imeiOrHost, String adminPort, String contentBody) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("contentBody", contentBody);
            String body = JSONObject.toJSONString(params);
            
            CompletableFuture<String> future = mqttProxyEventBusUtil.sendNotAssignRequest(
                MqttTopicEnum.OT_CONTROL, 
                "send", 
                imeiOrHost, 
                adminPort, 
                "connectOt", 
                body
            );
            
            String response = future.get();
            return JSONObject.parseObject(response, Result.class);
        } catch (Exception e) {
            return Result.error("连接OT失败: " + e.getMessage());
        }
    }
} 