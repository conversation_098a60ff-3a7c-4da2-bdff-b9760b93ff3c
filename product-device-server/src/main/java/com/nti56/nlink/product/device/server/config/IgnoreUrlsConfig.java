package com.nti56.nlink.product.device.server.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;

@Configuration
@ConfigurationProperties(prefix = "urls")
@Data
public class IgnoreUrlsConfig {

    private List<String> anon=new ArrayList<>();
    private List<String> noDcm=new ArrayList<>();

    private List<String> openApi=new ArrayList<>();

    /**
     * 跨域白名单
     */
    private List<String> crossWeb =new ArrayList<>();

    private List<String> noRemoveJwt =new ArrayList<>();
    /**
     * 是否开启白名单
     */
    private Boolean openWhiteList=false;

    private List<String> verifiableGet = new ArrayList<>();

}
