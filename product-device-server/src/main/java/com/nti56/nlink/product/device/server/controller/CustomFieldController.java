package com.nti56.nlink.product.device.server.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.BeanUtilsIntensifier;
import com.nti56.nlink.common.util.PageParam;
import com.nti56.nlink.common.util.R;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.nlink.product.device.server.service.ICustomFieldService;
import com.nti56.nlink.product.device.server.entity.CustomFieldEntity;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;

import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;
import java.util.function.Function;


/**
 * <p>
 * 自定义字段表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2022-11-18 11:43:28
 * @since JDK 1.8
 */
// @RestController
// @RequestMapping("/")
// @Tag( name = "自定义协议字段模块")
// @Api(protocols = "http,https", tags = "自定义字段表模块")
public class CustomFieldController {

    @Autowired
    ICustomFieldService service;

    // @GetMapping("custom-field/page")
    // @Operation(summary = "获取分页")
    public R page(@RequestHeader("ot_headers") TenantIsolation tenant, PageParam pageParam,CustomFieldEntity entity){
        Page<CustomFieldEntity> page = pageParam.toPage(CustomFieldEntity.class);
        Result<Page<CustomFieldEntity>> result = service.getPage(tenant, entity,page);
        return R.result(result);
    }

    // @GetMapping("custom-field/list")
    // @Operation(summary = "获取列表" )
    public R list(@RequestHeader("ot_headers") TenantIsolation tenant, CustomFieldEntity entity){
        Result<List<CustomFieldEntity>> result = service.list(tenant, entity);
        return R.result(result);
    }

    // @PostMapping("custom-field")
    // @Operation(summary = "创建固定头字段")
    public R create(@RequestHeader("ot_headers") TenantIsolation tenant, @ApiParam("对象") @RequestBody CustomFieldEntity entity){
        Result<CustomFieldEntity> result = service.save(tenant, entity);
        return R.result(result);
    }

    // @PutMapping("custom-field")
    // @Operation(summary = "更新")
    public R update(@RequestHeader("ot_headers") TenantIsolation tenant, @ApiParam("对象") @RequestBody CustomFieldEntity entity){
        if (BeanUtilsIntensifier.checkBeanAndProperties(entity, CustomFieldEntity::getId)) {
            return R.error(ServiceCodeEnum.CODE_PARAM_ERROR);
        }
        Result<Void> result = service.update(tenant, entity);
        return R.result(result);
    }

    // @DeleteMapping("custom-field/{entityId}")
    // @Operation(summary = "删除对象")
    public R delete(@RequestHeader("ot_headers") TenantIsolation tenant, @ApiParam("目标ID") @PathVariable Long entityId){
        Result<Void> result = service.deleteById(tenant, entityId);
        return R.result(result);
        }

    // @GetMapping("custom-field/{entityId}")
    // @Operation(summary = "获取对象")
    public R get(@RequestHeader("ot_headers") TenantIsolation tenant, @ApiParam("目标ID") @PathVariable Long entityId){
        Result<CustomFieldEntity> result = service.getById(tenant, entityId);
        return R.result(result);
        }
    
}
