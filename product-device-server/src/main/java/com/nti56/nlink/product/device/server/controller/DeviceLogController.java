package com.nti56.nlink.product.device.server.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.PageParam;
import com.nti56.nlink.common.util.R;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.entity.DeviceEntity;
import com.nti56.nlink.product.device.server.entity.DeviceServiceLogEntity;
import com.nti56.nlink.product.device.server.model.device.DeviceSelectBo;
import com.nti56.nlink.product.device.server.model.deviceLog.*;
import com.nti56.nlink.product.device.server.service.IDeviceLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName DeviceLogController
 * @date 2022/8/8 16:55
 * @Version 1.0
 */
@Slf4j
@RestController
@RequestMapping("device/log")
@Tag(name = "设备日志模块")
public class DeviceLogController {

    @Autowired
    IDeviceLogService deviceLogService;

//    @PostMapping("page/properties")
    @Deprecated
    @Operation(summary = "获取设备属性日志分页")
    public R pagePropertiesLog(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@RequestBody PropertyLogRequestBo requestBo) {
        if (ObjectUtil.isEmpty(requestBo.getCondition()) ||
                ObjectUtil.isEmpty(requestBo.getCondition().getDataType())
        ) {
            log.error("请选择要查询的数据类型！");
            return R.error("请选择要查询的数据类型！");
        }
        Page<PropertyLogBo> page = requestBo.getPageParam().toPage(PropertyLogBo.class);
        Result<Page<PropertyLogBo>> pageResult = deviceLogService.pagePropertiesLog(page,requestBo.getCondition(),tenantIsolation);
        return R.result(pageResult);
    }

    @GetMapping("select/device")
    @Operation(summary = "获取设备事件触发日志分页")
    public R selectDevice(@RequestHeader("ot_headers") TenantIsolation tenantIsolation) {
        Result<List<DeviceSelectBo>> listResult = deviceLogService.selectDevice(tenantIsolation);
        return R.result(listResult);
    }

    @PostMapping("select/properties")
    @Operation(summary = "根据设备ID,数据类型，获取属性")
    public R selectProperties(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, @RequestBody PropertyLogConditionBo condition){
        log.debug("根据设备ID：{},数据类型：{}，获取属性",condition.getDeviceIds(),condition.getDataType());
        return R.result(deviceLogService.getPropertiesByDeviceIds( tenantIsolation, condition));
    }


    @PostMapping("page/properties")
    @Operation(summary = "获取设备属性日志分页")
    public R propertiesLog(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@RequestBody PropertyLogConditionBo requestBo) {
        if (ObjectUtil.isEmpty(requestBo) || CollectionUtil.isEmpty(requestBo.getDeviceIds())
        ) {
            log.error("请选择要查询的设备！");
            return R.error("请选择要查询的设备！");
        }
        Result<List<PropertyLogBo>> pageResult = deviceLogService.propertiesLog(requestBo,tenantIsolation);
        return R.result(pageResult);
    }

    @GetMapping("page/events")
    @Operation(summary = "获取设备事件触发日志分页")
    public R pageEventTriggerLog(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, PageParam pageParam, EventTriggerLogBo eventTriggerLogBo) {
        if (ObjectUtil.isEmpty(eventTriggerLogBo) ||
                ObjectUtil.isEmpty(eventTriggerLogBo.getDeviceId()) ||
                ObjectUtil.isEmpty(pageParam) ||
                ObjectUtil.isEmpty(pageParam.getCurrent()) ||
                ObjectUtil.isEmpty(pageParam.getSize())
        ) {
            log.error("请选择设备查看日志");
            return R.error("请选择设备查看日志");
        }
        Page<EventTriggerLogBo> page = pageParam.toPage(EventTriggerLogBo.class);
        Result<Page<EventTriggerLogBo>> pageResult = deviceLogService.pageEventTriggerLog(page,eventTriggerLogBo,tenantIsolation);
        return R.result(pageResult);
    }

    @GetMapping("page/services")
    @Operation(summary = "获取设备服务日志分页")
    public R pageServiceLog(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, PageParam pageParam, ServiceCallLogRequestBo serviceCallLogRequestBo) {
        if ( ObjectUtil.isEmpty(pageParam) ||
                ObjectUtil.isEmpty(pageParam.getCurrent()) ||
                ObjectUtil.isEmpty(pageParam.getSize())
        ) {log.error("请选择页码查看日志");
            return R.error("请选择页码查看日志");
        }
        Page<DeviceServiceLogEntity> page = pageParam.toPage(DeviceServiceLogEntity.class);
        Result<Page<DeviceServiceLogEntity>> pageResult = deviceLogService.pageServiceCallLogBo(page, serviceCallLogRequestBo,tenantIsolation);
        return R.result(pageResult);
    }

}
