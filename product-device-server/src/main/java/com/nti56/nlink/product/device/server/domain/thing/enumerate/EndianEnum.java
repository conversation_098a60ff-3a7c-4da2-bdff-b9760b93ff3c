package com.nti56.nlink.product.device.server.domain.thing.enumerate;

import lombok.Getter;

/**
 * 类说明: 大小端枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-11-04 11:25:57
 * @since JDK 1.8
 */
public enum EndianEnum {
    
    BIG_ENDIAN(1, "bigEndian", "大端"), 
    LITTLE_ENDIAN(2, "littleEndian", "小端")
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    EndianEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static EndianEnum typeOfValue(Integer value){
        EndianEnum[] values = EndianEnum.values();
        for (EndianEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static EndianEnum typeOfName(String name){
        EndianEnum[] values = EndianEnum.values();
        for (EndianEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static EndianEnum typeOfNameDesc(String nameDesc){
        EndianEnum[] values = EndianEnum.values();
        for (EndianEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
