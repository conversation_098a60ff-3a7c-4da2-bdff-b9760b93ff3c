package com.nti56.nlink.product.device.server.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.BeanUtilsIntensifier;
import com.nti56.nlink.common.util.PageParam;
import com.nti56.nlink.common.util.R;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.nlink.product.device.server.model.DeviceRequestBo;
import com.nti56.nlink.product.device.server.model.deviceTemplate.DeviceTemplateBuildDTO;
import com.nti56.nlink.product.device.server.service.IDeviceTemplateService;
import com.nti56.nlink.product.device.server.entity.DeviceTemplateEntity;

import io.swagger.v3.oas.annotations.*;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.function.Function;


/**
 * <p>
 * 设备模板表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2023-03-07 17:33:19
 * @since JDK 1.8
 */
@RestController
@RequestMapping("device/template")
@Tag(name = "设备模板表模块")
@Slf4j
public class DeviceTemplateController {

    @Autowired
    IDeviceTemplateService service;

    @GetMapping("download/{id}")
    @Operation(summary = "下载设备模板文件" )
    public void downloadDeviceTemplate(HttpServletResponse response , @RequestHeader("ot_headers") TenantIsolation tenantIsolation, @PathVariable Long id){
        service.downloadDeviceTemplate(response,tenantIsolation ,id);
    }

    @PostMapping("import")
    @Operation(summary = "导入设备模板" )
    public R importDeviceTemplate(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, MultipartFile file){
        return R.result(service.importDeviceTemplate(tenantIsolation,file));
    }

    @PostMapping("buildTemplate")
    @Operation(summary = "构建设备模板_新" )
    public R buildTemplate(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, @RequestPart("build") DeviceTemplateBuildDTO build, MultipartFile file){
        if (CollectionUtil.isEmpty(build.getLabelList()) || StringUtils.isEmpty(build.getName()) || ObjectUtil.isNull(build.getDriver()) || CollectionUtil.isEmpty(build.getThingModelIdList())) {
            return R.error(ServiceCodeEnum.CODE_PARAM_ERROR);
        }
        return R.result(service.buildTemplate(tenantIsolation,build, file));
    }

    @PostMapping("build")
    @Operation(summary = "构建设备模板" )
    public R buildDeviceTemplate(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, @RequestBody DeviceRequestBo requestBo){
        if (CollectionUtil.isEmpty(requestBo.getIds()) || StringUtils.isEmpty(requestBo.getName())) {
            return R.error(ServiceCodeEnum.CODE_PARAM_ERROR);
        }
        return R.result(service.buildDeviceTemplate(tenantIsolation,requestBo.getIds(),requestBo.getName(), requestBo.getDescript()));
    }

    @GetMapping("page")
    @Operation(summary = "获取分页")
    public R page(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,PageParam pageParam, DeviceTemplateEntity entity) {
        BeanUtilsIntensifier.propertyInjection(tenantIsolation,entity);
        Page<DeviceTemplateEntity> page = pageParam.toPage(DeviceTemplateEntity.class);
        Result<Page<DeviceTemplateEntity>> result = service.getPage(entity, page);
        return R.result(result);
    }

    @GetMapping("list")
    @Operation(summary = "获取列表")
    public R list(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@RequestBody DeviceTemplateEntity entity) {
        BeanUtilsIntensifier.propertyInjection(tenantIsolation,entity);
        Result<List<DeviceTemplateEntity>> result = service.list(entity);
        return R.result(result);
    }

    @PostMapping("")
    @Operation(summary = "创建对象")
    public R create(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@Parameter(description = "对象") @RequestBody @Validated DeviceTemplateEntity entity) {
        BeanUtilsIntensifier.propertyInjection(tenantIsolation,entity);
        return checkParamAndDoSomething(entity, service::save);
    }

    @PutMapping("")
    @Operation(summary = "更新")
    public R update(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@Parameter(description = "对象") @RequestBody @Validated DeviceTemplateEntity entity) {
        if (BeanUtilsIntensifier.checkBeanAndProperties(entity, DeviceTemplateEntity::getId)) {
            return R.error(ServiceCodeEnum.CODE_PARAM_ERROR);
        }
        BeanUtilsIntensifier.propertyInjection(tenantIsolation,entity);
        Result<Void> result = service.update(entity);
        return R.result(result);
    }

    @DeleteMapping("{entityId}")
    @Operation(summary = "删除对象")
    public R delete(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@Parameter(description = "目标ID") @PathVariable Long entityId) {
        Result<Void> result = service.deleteById(entityId,tenantIsolation);
        return R.result(result);
    }

    @GetMapping("{entityId}")
    @Operation(summary = "获取对象")
    public R get(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@Parameter(description = "目标ID") @PathVariable Long entityId) {
        Result<DeviceTemplateEntity> result = service.getById(entityId,tenantIsolation);
        return R.result(result);
    }

    private R checkParamAndDoSomething(DeviceTemplateEntity entity, Function<DeviceTemplateEntity, Result> func) {
        //TODO: do check params
        //if (BeanUtilsIntensifier.checkBeanAndProperties(entity, DeviceTemplateEntity::getName)) {
        //    return R.error(ServiceCodeEnum.CODE_PARAM_ERROR);
        //}
        Result result = func.apply(entity);
        return R.result(result);
    }

}
