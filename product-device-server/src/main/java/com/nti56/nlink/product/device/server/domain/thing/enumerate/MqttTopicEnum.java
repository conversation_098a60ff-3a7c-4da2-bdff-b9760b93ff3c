package com.nti56.nlink.product.device.server.domain.thing.enumerate;

import lombok.Getter;


/**
 * 类说明: mqtt topic
 * 
 * 设计时
 * 1个虚拟网关可以给多个租户使用，即租户可以把他的n个网关挂到同一个虚拟网关上，
 * 网关监听虚拟网关id，virtualGatewayId
 *
 * 运行时
 * 1个租户只会有1个虚拟网关，租户可以把他的n个网关挂到同一个虚拟网关上
 * 网关监听网关id，edgeGatewayId
 * 
 * <AUTHOR>
 * @version 1.0
 * @date 2022-05-13 15:00:14
 * @since JDK 1.8
 */
public enum MqttTopicEnum {
    /*
    #### ot  --下发指令->  边缘网关

    同步采集任务
    ot/command/{tenantId}/{edgeGatewayId}/sync-gather-task

    同步计算任务
    ot/command/{tenantId}/{edgeGatewayId}/sync-compute-task
    */
    OT_COMMAND(1,"command","ot/command/","ot命令"),

    /*
    #### ot  --调用网关服务-->  边缘网关
    ot/spi/{tenantId}/{edgeGatewayId}/writeInt
    */
    OT_SPI(2,"spi","ot/spi/","ot服务调用"),

    
    /*
    #### ot  --调用网关控制-->  边缘网关
    ot/control/{tenantId}/{edgeGatewayId}/stopServer
    ot/control/{tenantId}/{edgeGatewayId}/startServer
    */
    OT_CONTROL(3,"control","ot/control/","ot控制"),

    OT_CACHE(4,"cache","ot/cache/","ot缓存调用"),

    /*
    #### 边缘网关 --响应指令--> ot
    gw/response/{balanceId}/{tenantId}/{edgeGatewayId}/{spi}/{requestId}
    */
    GW_RESPONSE(5,"response","gw/response/","网关响应"),

    /* 
    #### 边缘网关  --上报数据-->  ot

    触发事件
    gw/up/{tenantId}/{edgeGatewayId}/{deviceId}/thing/events/trigger/{eventName}

    属性改变事件
    gw/up/{deviceId}/{edgeGatewayId}/thing/events/change/{eventName}

    属性上报事件
    gw/up/{tenantId}/{edgeGatewayId}/{deviceId}/thing/events/report/{eventName}

    标签改变
    gw/up/{tenantId}/{edgeGatewayId}/labelChange/{labelId}

    标签上报
    gw/up/{tenantId}/{edgeGatewayId}/labelReport/{labelId}

    故障事件上报
    gw/up/{tenantId}/{edgeGatewayId}/{deviceId}/thing/events/fault/{eventName}
    
    */
    GW_UP(6,"up","gw/up/","网关上报"),

    /*
    #### ot  ---->  规则引擎

    规则引擎实例
    ot/rule/{tenantId}/{instanceId}
    */
    OT_RULE(7,"rule","ot/rule/","ot规则"),

    GW_SYNC(8,"sync","gw/sync/","网关同步"),

    GW_HEARTBEAT(9,"heartbeat","gw/heartbeat/","网关心跳"),

    OT_HEARTBEAT_ECHO(10,"heartbeatEcho","ot/heartbeat/echo/","网关心跳响应"),
    
    GW_CUSTOM(11, "gwCustom", "gw/custom/", "网关自定义协议上报"),
    
    THIRD_CUSTOM(12, "thirdCustom", "third/custom/", "网关自定义协议调用"),
    
    OT_CUSTOM(13,"custom","ot/custom/","ot自定义协议调用"),

    GW_NOT_ASSIGN_HEARTBEAT(14,"notAssignHeartbeat","gw/notAssignHeartbeat/","未分配心跳"),
    
    GW_SYNC_DATA(15, "gwSyncData","gw/sync/data/","网关同步数据给OT"),
    GW_HARDWARE_MONITOR(16,"gw/hardware/monitor/","gw/hardware/monitor/","网格硬件监控"),
    GW_DEBUG(99, "debug", "gw/debug/", "网关调试上报")
    ;
    
    @Getter
    private Integer value;

    @Getter
    private String topicType;

    @Getter
    private String prefix;

    @Getter
    private String nameDesc;

    MqttTopicEnum(Integer value, String topicType, String prefix, String nameDesc) {
        this.value = value;
        this.topicType = topicType;
        this.prefix = prefix;
        this.nameDesc = nameDesc;
    }
    
    public static MqttTopicEnum typeOfValue(Integer value){
        MqttTopicEnum[] values = MqttTopicEnum.values();
        for (MqttTopicEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }
    public static MqttTopicEnum typeOfType(String topicType){
        MqttTopicEnum[] values = MqttTopicEnum.values();
        for (MqttTopicEnum v : values) {
            if (v.topicType.equals(topicType)) {
                return v;
            }
        }
        return null;
    }

    public static MqttTopicEnum typeOfPrefix(String prefix){
        MqttTopicEnum[] values = MqttTopicEnum.values();
        for (MqttTopicEnum v : values) {
            if (v.prefix.equals(prefix)) {
                return v;
            }
        }
        return null;
    }

    public static MqttTopicEnum typeOfNameDesc(String nameDesc){
        MqttTopicEnum[] values = MqttTopicEnum.values();
        for (MqttTopicEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }

}
