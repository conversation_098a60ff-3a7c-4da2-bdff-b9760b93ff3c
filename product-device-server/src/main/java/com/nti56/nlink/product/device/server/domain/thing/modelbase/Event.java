package com.nti56.nlink.product.device.server.domain.thing.modelbase;

import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.client.model.dto.json.ModelField;
import com.nti56.nlink.product.device.client.model.dto.json.dpo.EventDpo;
import com.nti56.nlink.product.device.client.model.dto.json.modelfield.EventDefineElm;
import com.nti56.nlink.product.device.client.model.dto.json.modelfield.EventElm;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.EventTypeEnum;
import com.nti56.nlink.product.device.server.model.inherit.EventOfInherit;
import com.nti56.nlink.product.device.server.util.RegexUtil;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;

/**
 * 类说明: 物模型事件领域对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-24 16:41:00
 * @since JDK 1.8
 */
@Getter
@Slf4j
public class Event {

    private String name;
    private EventTypeEnum type;
    private EventDefine eventDefine;
    private EventElm raw;

    //直属物模型id
    private Long thingModelId;

    //直属物模型名称
    private String thingModelName;

    private String descript;

    public static Result<Event> checkInfo( Long thingModelId,
                                           String thingModelName,
                                           EventElm eventElm, 
                                           Map<String, Property> propertyNameMap
    ) {

        if(eventElm == null){
            return Result.error("事件不能为空");
        }

        String nameStr = eventElm.getName();
        if(nameStr == null){
            return Result.error("事件名称不能为空");
        }

        Event event = new Event();
        
        //名称
        if(!RegexUtil.checkName(nameStr)){
            return Result.error(
                "事件名称格式错误，只支持英文数字下划线，且英文首字母，name: " + nameStr
            );
        }
        event.name = nameStr;
        event.thingModelId = thingModelId;
        event.thingModelName = thingModelName;
        event.descript = eventElm.getDescript();

        //事件类型
        String typeStr = eventElm.getType();
        if(typeStr == null){
            return Result.error("事件缺少事件类型");
        }
        EventTypeEnum typeEnum = EventTypeEnum.typeOfName(typeStr);
        if(typeEnum == null){
            return Result.error(
                "事件类型错误，type: " + typeStr
            );
        }
        event.type = typeEnum;

        //事件定义
        EventDefineElm eventDefineElm = eventElm.getEventDefine();
        eventDefineElm.setThingServiceId(eventElm.getThingServiceId());
        Result<EventDefine> eventDefineResult = EventDefine.checkInfo(
            nameStr, typeEnum, eventDefineElm, propertyNameMap
        );
        if(!eventDefineResult.getSignal()){
            return Result.error(eventDefineResult.getMessage() + ", name:" + nameStr);
        }
        event.eventDefine = eventDefineResult.getResult();
        event.raw = eventElm;

        return Result.ok(event);
    }

    public static Result<List<Event>> batchCheckInfo(Long thingModelId,
                                                     String thingModelName, ModelField model, List<Property> properties){
        List<Event> events = new ArrayList<>();
        if(model != null){
            List<EventElm> eventElms = model.getEvents();
            if(eventElms != null && eventElms.size() > 0){
                
                Map<String, Property> propertyNameMap = new HashMap<>();
                for(Property p:properties){
                    propertyNameMap.put(p.getName(), p);
                }
                //自定义事件
                for(EventElm eventElm:eventElms){
                    Result<Event> eventResult = Event.checkInfo(thingModelId,thingModelName,eventElm, propertyNameMap);
                    if(!eventResult.getSignal()){
                        return Result.error(eventResult.getMessage());
                    }
                    events.add(eventResult.getResult());
                }
            }
        }
        return Result.ok(events);
    }

    public static Result<List<Event>> checkRepeat(
        List<Event> inheritEvents,
        List<Event> selfEvents
    ){
        
        List<Event> events = new ArrayList<>();
        if(inheritEvents != null){
            events.addAll(inheritEvents);
        }
        if(selfEvents != null){
            events.addAll(selfEvents);
        }
        //事件名称不能重复
        if(events != null && events.size() > 0){
            Map<String, Long> eventCountMap = events.stream()
                    .map(Event::getName)
                    .collect(Collectors.groupingBy(
                        p -> p,
                        Collectors.counting()
                    ));
            for(Entry<String, Long> entry:eventCountMap.entrySet()){
                if(entry.getValue() > 1){
                    return Result.error("物模型事件名重复，name:" + entry.getKey());
                }
            }
        }
        return Result.ok(events);
    }


    public EventOfInherit toEventOfInherit() {
        EventOfInherit e = new EventOfInherit();
        BeanUtils.copyProperties(raw, e);
        e.setBaseThingModelId(thingModelId);
        e.setBaseThingModelName(thingModelName);
        return e;
    }

    public EventDpo toDpo() {
        EventDpo dpo = new EventDpo();
        dpo.setThingModelId(thingModelId);
        dpo.setThingModelName(thingModelName);
        dpo.setName(name);
        dpo.setType(type.getName());
        dpo.setDescript(raw.getDescript());
        dpo.setEventDefine(eventDefine.toDpo());
        dpo.setThingServiceId(eventDefine.getThingServiceId());

        return dpo;
    }

    public Boolean evalTrigger(String trigger, Map<String, Object> prop){
        log.debug("parseTrigger begin");
        ExpressionParser parser = new SpelExpressionParser();
        Expression expression = parser.parseExpression(trigger);
        EvaluationContext context = new StandardEvaluationContext();
        context.setVariable("prop", prop);
        Boolean result = expression.getValue(context, Boolean.class);
        log.debug("parseTrigger result: {}", result);
        return result;
    }

    public static Boolean doEvalTrigger(String trigger, Map<String, Object> prop){
        log.debug("parseTrigger begin");
        ExpressionParser parser = new SpelExpressionParser();
        Expression expression = parser.parseExpression(trigger);
        EvaluationContext context = new StandardEvaluationContext();
        context.setVariable("prop", prop);
        Boolean result = expression.getValue(context, Boolean.class);
        log.debug("parseTrigger result: {}", result);
        return result;
    }

    
}
