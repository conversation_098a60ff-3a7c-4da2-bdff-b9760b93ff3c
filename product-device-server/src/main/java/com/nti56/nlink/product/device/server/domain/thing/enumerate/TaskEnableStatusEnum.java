package com.nti56.nlink.product.device.server.domain.thing.enumerate;

import lombok.Getter;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-24 14:07:02
 * @since JDK 1.8
 */
public enum TaskEnableStatusEnum {
    DISABLED(0, "disabled", "关闭"), 
    ENABLED(1, "enabled", "启用")
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    TaskEnableStatusEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static TaskEnableStatusEnum typeOfValue(Integer value){
        TaskEnableStatusEnum[] values = TaskEnableStatusEnum.values();
        for (TaskEnableStatusEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static TaskEnableStatusEnum typeOfName(String name){
        TaskEnableStatusEnum[] values = TaskEnableStatusEnum.values();
        for (TaskEnableStatusEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static TaskEnableStatusEnum typeOfNameDesc(String nameDesc){
        TaskEnableStatusEnum[] values = TaskEnableStatusEnum.values();
        for (TaskEnableStatusEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
