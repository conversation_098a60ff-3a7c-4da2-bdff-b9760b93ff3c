package com.nti56.nlink.product.device.server.controller;

import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.R;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.model.*;
import com.nti56.nlink.product.device.server.model.label.dto.*;
import com.nti56.nlink.product.device.server.model.label.vo.PageLabelVO;
import com.nti56.nlink.product.device.server.service.ILabelService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 类说明: 标签controller
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 13:32:04
 * @since JDK 1.8
 */
@RestController
@RequestMapping("label")
@Tag(name = "标签模块")
public class LabelController {
    
    @Autowired
    ILabelService labelService;


    @PostMapping("page")
    @Operation(summary = "获取标签分页" ,
            parameters  = {
                    @Parameter(name = "current",description = "要获取的页码",required = true),
                    @Parameter(name="size",description = "每页获取多少条",required = true)
            })
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200",description = "返回值",
                    content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = PageLabelVO.class)
                    )})
    })
    public R pageLabel(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@RequestBody @Validated PageLabelDTO dto){
        return R.result(labelService.pageLabel(dto,tenantIsolation));
    }

    @PostMapping("page/full_name")
    @Operation(summary = "获取标签分页(fullName)" ,
            parameters  = {
                    @Parameter(name = "current",description = "要获取的页码",required = true),
                    @Parameter(name="size",description = "每页获取多少条",required = true)
            })
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200",description = "返回值",
                    content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = PageLabelVO.class)
                    )})
    })
    public R pageLabelWithFullName(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@RequestBody @Validated PageLabelDTO dto){
        return R.result(labelService.pageLabelWithFullName(dto,tenantIsolation));
    }

    @DeleteMapping("batch/delete")
    @Operation(summary = "批量删除标签")
    public R labelBatchDelete(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@RequestBody LabelRequestBo requestBo) {
        Result<Void> result = labelService.deleteByIdsAndTenantIsolation(requestBo.getIds(),tenantIsolation);
        return R.result(result);
    }

    @DeleteMapping("batch/unbind")
    @Operation(summary = "批量解绑标签")
    public R labelBatchUnbind(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@RequestBody LabelRequestBo requestBo) {
        Result<Void> result = labelService.labelBatchUnbind(tenantIsolation,requestBo);
        return R.result(result);
    }

    @PostMapping("batch/input/{channelId}")
    @Operation(summary = "导入标签")
    public R labelBatchInput(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@PathVariable Long channelId,@RequestBody List<LabelDTO> list) {
        Result<List<ExcelLabelDTO>> result = labelService.labelExcelBatchInput(tenantIsolation,channelId,list);
        return R.result(result);
    }


    @PostMapping("check")
    @Operation(summary = "检查label数据是否正确")
    public R checkLabel(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
        @RequestBody @Validated CheckLabelDTO dto
    ){
        return R.result(labelService.checkLabel(dto,tenantIsolation));
    }

    @PostMapping(value="connect-test")
    @Operation(summary = "标签连接测试")
    public R connectTest(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                         @RequestBody List<Long> labelIds) {
        return R.result(labelService.connectTest(tenantIsolation,labelIds));
    }


    @PutMapping("{id}")
    @Operation(summary = "根据id更新标签")
    public R editLabel(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                       @RequestBody @Validated EditLabelDTO dto
    ){
        return R.result(labelService.editLabel(dto,tenantIsolation));
    }

    @PostMapping("")
    @Operation(summary = "创建标签")
    public R addLabel(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                      @RequestBody @Validated LabelDTO dto
    ){
        return R.result(labelService.createLabel(dto,tenantIsolation));
    }


    @PutMapping("alias")
    @Operation(summary = "修改别名")
    @Deprecated
    public R editLabelAlias(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                       @RequestBody @Validated EditLabelAliasDTO dto
    ){
        return R.result(labelService.editLabelAlias(dto,tenantIsolation));
    }

    @PutMapping(value="connectByLabelGroupId/{labelGroupId}")
    @Operation(summary = "标签连接测试")
    public R connectByLabelGroupId(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@PathVariable("labelGroupId") Long labelGroupId) {
        return R.result(labelService.connectByLabelGroupId(tenantIsolation,labelGroupId));
    }

    @PutMapping(value="moveOrCopy")
    @Operation(summary = "移动或复制标签")
    public R moveOrCopyLabel(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                    @RequestBody @Validated MoveOrCopyLabelDTO dto) {
        return R.result(labelService.moveOrCopyLabel(dto,tenantIsolation));
    }

    @PutMapping("batch/updateInterval")
    @Operation(summary = "批量更新轮询间隔")
    public R updateInterval(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@RequestBody LabelRequestBo requestBo) {
        Result<Void> result = labelService.batchUpdateInterval(tenantIsolation,requestBo);
        return R.result(result);
    }

    @PostMapping("listByLabelGroupIds")
    @Operation(summary = "根据分组id集合查询标签集合")
    public R listByLabelGroupIds(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@RequestBody ModelLabelRequestBo modelLabelRequestBo) {
        return R.result(labelService.listByLabelGroupIds(tenantIsolation,modelLabelRequestBo));
    }

    @GetMapping("list/{channelName}/{groupName}/{edgeGatewayId}")
    @Operation(summary = "根据分组id集合查询标签集合")
    public R listByAbsolutionName(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, @PathVariable String channelName, @PathVariable String groupName,@PathVariable Long edgeGatewayId) {
        return R.result(labelService.listByAbsolutionName(tenantIsolation, edgeGatewayId,channelName,groupName));
    }
    
}
