package com.nti56.nlink.product.device.server.domain.thing.enumerate;

import lombok.Getter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 类说明: 状态枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 13:33:25
 * @since JDK 1.8
 */
public enum SyncStatusEnum {
    HAVE_SYNC(1, "已同步", "已同步"),
    NOT_SYNC(0, "未同步", "未同步"),
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    SyncStatusEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static SyncStatusEnum typeOfValue(Integer value){
        SyncStatusEnum[] values = SyncStatusEnum.values();
        for (SyncStatusEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static SyncStatusEnum typeOfName(String name){
        SyncStatusEnum[] values = SyncStatusEnum.values();
        for (SyncStatusEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static SyncStatusEnum typeOfNameDesc(String nameDesc){
        SyncStatusEnum[] values = SyncStatusEnum.values();
        for (SyncStatusEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }


    public static List toList(){
        List<Map> result = new ArrayList<>();
        SyncStatusEnum[] values = SyncStatusEnum.values();
        Map<String,Object> map ;
        for (SyncStatusEnum v : values) {
            map = new HashMap<>();
            map.put("name",v.name);
            map.put("value",v.value);
            map.put("nameDesc",v.nameDesc);
            result.add(map);
        }
        return result;
    }
}
