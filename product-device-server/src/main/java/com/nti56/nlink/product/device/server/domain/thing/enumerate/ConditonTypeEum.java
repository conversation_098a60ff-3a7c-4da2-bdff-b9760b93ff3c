package com.nti56.nlink.product.device.server.domain.thing.enumerate;

import lombok.Getter;

/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName ConditonTypeEum
 * @date 2022/4/18 15:53
 * @Version 1.0
 */
public enum ConditonTypeEum {
    PROPERTY_CONDITION(1, "属性条件", "属性条件"),
    EVENT_CONDITION(2, "事件条件", "事件条件"),
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    ConditonTypeEum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static ConditonTypeEum typeOfValue(Integer value){
        ConditonTypeEum[] values = ConditonTypeEum.values();
        for (ConditonTypeEum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static ConditonTypeEum typeOfName(String name){
        ConditonTypeEum[] values = ConditonTypeEum.values();
        for (ConditonTypeEum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static ConditonTypeEum typeOfNameDesc(String nameDesc){
        ConditonTypeEum[] values = ConditonTypeEum.values();
        for (ConditonTypeEum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
