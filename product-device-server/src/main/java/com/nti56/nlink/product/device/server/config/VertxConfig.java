package com.nti56.nlink.product.device.server.config;

import com.nti56.nlink.product.device.server.factory.SpringVerticleFactory;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import io.vertx.core.Vertx;
import io.vertx.core.VertxOptions;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-16 14:30:47
 * @since JDK 1.8
 */
@Configuration
public class VertxConfig {
    
    @Autowired
    private SpringVerticleFactory springVerticleFactory;

    @Value("${vertx.workerPoolSize:20}")
    private Integer workerPoolSize;

    @Value("${vertx.blockedThreadCheckInterval:1000}")
    private Long blockedThreadCheckInterval;

    @Bean("vertx")
    public Vertx vertxInstance(){

        VertxOptions options = new VertxOptions();
        options.setWorkerPoolSize(workerPoolSize);
        options.setBlockedThreadCheckInterval(blockedThreadCheckInterval);
        Vertx vertx = Vertx.vertx(options);
        vertx.registerVerticleFactory(springVerticleFactory);

        return vertx;
    }
    
}
