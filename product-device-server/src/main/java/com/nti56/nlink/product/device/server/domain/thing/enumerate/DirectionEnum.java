package com.nti56.nlink.product.device.server.domain.thing.enumerate;

import lombok.Getter;

public enum DirectionEnum {
    SEND_AND_RECEIVE(1, "sendAndReceive", "sendAndReceive"), 
    SEND(2, "send", "send"), 
    RECEIVE(3, "receive", "receive")
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    DirectionEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static DirectionEnum typeOfValue(Integer value){
        DirectionEnum[] values = DirectionEnum.values();
        for (DirectionEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static DirectionEnum typeOfName(String name){
        DirectionEnum[] values = DirectionEnum.values();
        for (DirectionEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static DirectionEnum typeOfNameDesc(String nameDesc){
        DirectionEnum[] values = DirectionEnum.values();
        for (DirectionEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
