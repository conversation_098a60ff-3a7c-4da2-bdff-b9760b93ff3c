package com.nti56.nlink.product.device.server.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.nti56.model.BaseModel;
import lombok.*;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/2/15 13:08<br/>
 * @since JDK 1.8
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName("tag")
public class Tag extends BaseModel {

    /**
     * 空间id
     */
    private Long spaceId;
    /**
     * 工程id
     */
    private Long engineeringId;

    /**
     * 模块id
     */
    private Long moduleId;

    /**
     * 键
     */
    private String tagKey;
    /**
     *值
     */
    private String tagValue;
    /**
     * 租户id
     */
    private Long tenantId;



}
