package com.nti56.nlink.product.device.server.domain.thing.enumerate;

import lombok.Getter;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-24 14:06:33
 * @since JDK 1.8
 */
public enum ServiceTypeEnum {
    NORMAL_SERVICE(0, "普通服务", "normal service"),
    FAULT_SERVICE(1, "故障服务", "fault service"),
    COMMON_SERVICE(4, "通用服务", "common service"),
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    ServiceTypeEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static ServiceTypeEnum typeOfValue(Integer value){
        ServiceTypeEnum[] values = ServiceTypeEnum.values();
        for (ServiceTypeEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static ServiceTypeEnum typeOfName(String name){
        ServiceTypeEnum[] values = ServiceTypeEnum.values();
        for (ServiceTypeEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static ServiceTypeEnum typeOfNameDesc(String nameDesc){
        ServiceTypeEnum[] values = ServiceTypeEnum.values();
        for (ServiceTypeEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
