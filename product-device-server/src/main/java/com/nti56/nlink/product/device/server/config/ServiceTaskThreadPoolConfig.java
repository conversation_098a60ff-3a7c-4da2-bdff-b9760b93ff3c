package com.nti56.nlink.product.device.server.config;

import com.nti56.nlink.product.device.server.serviceEngine.ServiceThreadPoolTaskExecutor;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import java.util.concurrent.Executor;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName ServiceTaskThreadPoolConfig
 * @date 2022/4/24 13:19
 * @Version 1.0
 */
@Configuration
@EnableAsync
public class ServiceTaskThreadPoolConfig {

    @Bean
    public ServiceThreadPoolTaskExecutor taskExecutor() {
        ServiceThreadPoolTaskExecutor poolExecutor = new ServiceThreadPoolTaskExecutor();
        // 核心线程数
        poolExecutor.setCorePoolSize(5);
        // 最大线程数
        poolExecutor.setMaxPoolSize(16);
        // 队列大小
        poolExecutor.setQueueCapacity(100);
        // 线程最大空闲时间
        poolExecutor.setKeepAliveSeconds(300);
        // 拒绝策略
        poolExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        // 线程名称前缀
        poolExecutor.setThreadNamePrefix("service-pool-");

        return poolExecutor;
    }

    @Value("${spring.task.execution.websocket-pool.core-size}")
    private int websocketCorePoolSize;
    @Value("${spring.task.execution.websocket-pool.max-size}")
    private int websocketMaxPoolSize;
    @Value("${spring.task.execution.websocket-pool.queue-capacity:120}")
    private int websocketQueueCapacity;
    @Value("${spring.task.execution.websocket-pool.thread-name-prefix}")
    private String websocketNamePrefix;
    @Value("${spring.task.execution.websocket-pool.keep-alive}")
    private int websocketKeepAliveSeconds;

    @Bean(name = "collectingOnceAsyncExecutor")
    public Executor threadPoolTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //最大线程数
        executor.setMaxPoolSize(websocketMaxPoolSize);
        //核心线程数
        executor.setCorePoolSize(websocketCorePoolSize);
        //任务队列的大小
        executor.setQueueCapacity(websocketQueueCapacity);
        //线程前缀名
        executor.setThreadNamePrefix(websocketNamePrefix);
        //线程存活时间
        executor.setKeepAliveSeconds(websocketKeepAliveSeconds);

        /**
         * 拒绝处理策略
         * CallerRunsPolicy()：交由调用方线程运行，比如 main 线程。
         * AbortPolicy()：直接抛出异常。
         * DiscardPolicy()：直接丢弃。
         * DiscardOldestPolicy()：丢弃队列中最老的任务。
         */
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        //线程初始化
        executor.initialize();
        return executor;
    }

    @Value("${spring.task.execution.property-consumer-pool.core-size}")
    private int propertyCorePoolSize;
    @Value("${spring.task.execution.property-consumer-pool.max-size}")
    private int propertyMaxPoolSize;
    @Value("${spring.task.execution.property-consumer-pool.queue-capacity:120}")
    private int propertyQueueCapacity;
    @Value("${spring.task.execution.property-consumer-pool.thread-name-prefix}")
    private String propertyNamePrefix;
    @Value("${spring.task.execution.property-consumer-pool.keep-alive}")
    private int propertyKeepAliveSeconds;

    @Bean(name = "propertyConsumerAsyncExecutor")
    public Executor propertyConsumerAsyncExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //最大线程数
        executor.setMaxPoolSize(propertyMaxPoolSize);
        //核心线程数
        executor.setCorePoolSize(propertyCorePoolSize);
        //任务队列的大小 默认是Integer.MAX_VALUE
        executor.setQueueCapacity(propertyQueueCapacity);
        //线程前缀名
        executor.setThreadNamePrefix(propertyNamePrefix);
        //线程存活时间
        executor.setKeepAliveSeconds(propertyKeepAliveSeconds);

        /**
         * 拒绝处理策略
         * CallerRunsPolicy()：交由调用方线程运行，比如 main 线程。
         * AbortPolicy()：直接抛出异常。
         * DiscardPolicy()：直接丢弃。
         * DiscardOldestPolicy()：丢弃队列中最老的任务。
         */
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        executor.setAllowCoreThreadTimeOut(true);
        //线程初始化
        executor.initialize();
        return executor;
    }

    @Bean(name = "deviceOptionExecutor")
    public Executor deviceOptionExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //最大线程数
        executor.setMaxPoolSize(1000);
        //核心线程数
        executor.setCorePoolSize(20);
        //任务队列的大小 默认是Integer.MAX_VALUE
        executor.setQueueCapacity(5_0000);
        //线程前缀名
        executor.setThreadNamePrefix("device-option-");
        //线程存活时间
        executor.setKeepAliveSeconds(20);

        /**
         * 拒绝处理策略
         * CallerRunsPolicy()：交由调用方线程运行，比如 main 线程。
         * AbortPolicy()：直接抛出异常。
         * DiscardPolicy()：直接丢弃。
         * DiscardOldestPolicy()：丢弃队列中最老的任务。
         */
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.DiscardOldestPolicy());
        //线程初始化
        executor.initialize();
        return executor;
    }

    @Value("${spring.task.execution.label-consumer-pool.core-size}")
    private int labelCorePoolSize;
    @Value("${spring.task.execution.label-consumer-pool.max-size}")
    private int labelMaxPoolSize;
    @Value("${spring.task.execution.label-consumer-pool.queue-capacity:120}")
    private int labelQueueCapacity;
    @Value("${spring.task.execution.label-consumer-pool.thread-name-prefix}")
    private String labelNamePrefix;
    @Value("${spring.task.execution.label-consumer-pool.keep-alive}")
    private int labelKeepAliveSeconds;

    @Bean(name = "labelConsumerAsyncExecutor")
    public Executor labelConsumerAsyncExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //最大线程数
        executor.setMaxPoolSize(labelMaxPoolSize);
        //核心线程数
        executor.setCorePoolSize(labelCorePoolSize);
        //任务队列的大小
        executor.setQueueCapacity(labelQueueCapacity);
        //线程前缀名
        executor.setThreadNamePrefix(labelNamePrefix);
        //线程存活时间
        executor.setKeepAliveSeconds(labelKeepAliveSeconds);

        /**
         * 拒绝处理策略
         * CallerRunsPolicy()：交由调用方线程运行，比如 main 线程。
         * AbortPolicy()：直接抛出异常。
         * DiscardPolicy()：直接丢弃。
         * DiscardOldestPolicy()：丢弃队列中最老的任务。
         */
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.DiscardOldestPolicy());
        //线程初始化
        executor.initialize();
        return executor;
    }

    @Value("${spring.task.execution.subscription-pool.core-size}")
    private int subscriptionCorePoolSize;
    @Value("${spring.task.execution.subscription-pool.max-size}")
    private int subscriptionMaxPoolSize;
    @Value("${spring.task.execution.subscription-pool.queue-capacity:120}")
    private int subscriptionQueueCapacity;
    @Value("${spring.task.execution.subscription-pool.thread-name-prefix}")
    private String subscriptionNamePrefix;
    @Value("${spring.task.execution.subscription-pool.keep-alive}")
    private int subscriptionKeepAliveSeconds;


    @Bean(name = "subscriptionAsyncExecutor")
    public Executor subscriptionAsyncExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //最大线程数
        executor.setMaxPoolSize(subscriptionMaxPoolSize);
        //核心线程数
        executor.setCorePoolSize(subscriptionCorePoolSize);
        //任务队列的大小
        executor.setQueueCapacity(subscriptionQueueCapacity);
        //线程前缀名
        executor.setThreadNamePrefix(subscriptionNamePrefix);
        //线程存活时间
        executor.setKeepAliveSeconds(subscriptionKeepAliveSeconds);

        /**
         * 拒绝处理策略
         * CallerRunsPolicy()：交由调用方线程运行，比如 main 线程。
         * AbortPolicy()：直接抛出异常。
         * DiscardPolicy()：直接丢弃。
         * DiscardOldestPolicy()：丢弃队列中最老的任务。
         */
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        //线程初始化
        executor.initialize();
        return executor;
    }

    @Value("${spring.task.execution.log-pool.core-size}")
    private int logCorePoolSize;
    @Value("${spring.task.execution.log-pool.max-size}")
    private int logMaxPoolSize;
    @Value("${spring.task.execution.log-pool.queue-capacity:120}")
    private int logQueueCapacity;
    @Value("${spring.task.execution.log-pool.thread-name-prefix}")
    private String logNamePrefix;
    @Value("${spring.task.execution.log-pool.keep-alive}")
    private int logKeepAliveSeconds;

    @Bean(name = "logCronJobExecutor")
    public ExecutorService logCronJobExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //最大线程数
        executor.setMaxPoolSize(logMaxPoolSize);
        //核心线程数
        executor.setCorePoolSize(logCorePoolSize);
        //任务队列的大小
        executor.setQueueCapacity(logQueueCapacity);
        //线程前缀名
        executor.setThreadNamePrefix(logNamePrefix);
        //线程存活时间
        executor.setKeepAliveSeconds(logKeepAliveSeconds);

        /**
         * 拒绝处理策略
         * CallerRunsPolicy()：交由调用方线程运行，比如 main 线程。
         * AbortPolicy()：直接抛出异常。
         * DiscardPolicy()：直接丢弃。
         * DiscardOldestPolicy()：丢弃队列中最老的任务。
         */
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.DiscardOldestPolicy());
        //线程初始化
        executor.initialize();
        return executor.getThreadPoolExecutor();
    }


    @Bean(name = "deviceCloudAsyncExecutor")
    public Executor deviceCloudAsyncExecutor() {
        return getThreadPoolTaskExecutor();
    }
    
    @Bean(name = "edgeGatewayAsyncExecutor")
    public Executor edgeGatewayAsyncExecutor() {
        return getThreadPoolTaskExecutor();
    }
    
    @Bean(name = "channelAsyncExecutor")
    public Executor channelAsyncExecutor() {
        return getThreadPoolTaskExecutor();
    }

    @Bean(name = "notAssignHeartbeatAsyncExecutor")
    public Executor notAssignHeartbeatAsyncExecutor() {
        return getThreadPoolTaskExecutor();
    }


    @Value("${spring.task.execution.statusChange-pool.core-size}")
    private int statusChangeCorePoolSize;
    @Value("${spring.task.execution.statusChange-pool.max-size}")
    private int statusChangeMaxPoolSize;
    @Value("${spring.task.execution.statusChange-pool.queue-capacity:120}")
    private int statusChangeQueueCapacity;
    @Value("${spring.task.execution.statusChange-pool.thread-name-prefix}")
    private String statusChangeNamePrefix;
    @Value("${spring.task.execution.statusChange-pool.keep-alive}")
    private int statusChangeKeepAliveSeconds;

    @NotNull
    private Executor getThreadPoolTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //最大线程数
        executor.setMaxPoolSize(statusChangeMaxPoolSize);
        //核心线程数
        executor.setCorePoolSize(statusChangeCorePoolSize);
        //任务队列的大小
        executor.setQueueCapacity(statusChangeQueueCapacity);
        //线程前缀名
        executor.setThreadNamePrefix(statusChangeNamePrefix);
        //线程存活时间
        executor.setKeepAliveSeconds(statusChangeKeepAliveSeconds);

        /**
         * 拒绝处理策略
         * CallerRunsPolicy()：交由调用方线程运行，比如 main 线程。
         * AbortPolicy()：直接抛出异常。
         * DiscardPolicy()：直接丢弃。
         * DiscardOldestPolicy()：丢弃队列中最老的任务。
         */
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.DiscardOldestPolicy());
        //线程初始化
        executor.initialize();
        return executor;
    }
}
