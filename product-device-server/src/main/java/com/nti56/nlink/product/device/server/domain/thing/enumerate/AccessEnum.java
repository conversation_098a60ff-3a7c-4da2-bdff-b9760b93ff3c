package com.nti56.nlink.product.device.server.domain.thing.enumerate;

import lombok.Getter;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-05-26 08:59:36
 * @since JDK 1.8
 */
public enum AccessEnum {
    READ_ONLY(1, "r", "只读"), 
    WRITE_ONLY(2, "w", "只写"),
    READ_WRITE(3, "rw", "读写")
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    AccessEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static AccessEnum typeOfValue(Integer value){
        AccessEnum[] values = AccessEnum.values();
        for (AccessEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static AccessEnum typeOfName(String name){
        AccessEnum[] values = AccessEnum.values();
        for (AccessEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static AccessEnum typeOfNameDesc(String nameDesc){
        AccessEnum[] values = AccessEnum.values();
        for (AccessEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
