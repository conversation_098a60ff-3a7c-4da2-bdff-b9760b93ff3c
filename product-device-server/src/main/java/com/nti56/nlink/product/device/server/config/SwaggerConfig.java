package com.nti56.nlink.product.device.server.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.servers.Server;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 类说明: Swagger配置<br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2021/9/4 15:19<br/>
 * @since JDK 1.8
 */
@Configuration
public class SwaggerConfig {

    @Value("${nlink.swagger.serverUrl}")
    private String serverUrl;

    @Bean
    public OpenAPI openAPI(@Value("${springdoc.version}") String appVersion) {
        OpenAPI openAPI = new OpenAPI();
        openAPI.info(new Info()
                .title("Product-Device Manager API")
                .description("Product-Device manager server api.")
                .version(appVersion)
                .license(new License()
                        .name("Apache2.0")
                        .url("http://springdoc.org")));

        if (!"localhost".equalsIgnoreCase(serverUrl)) {
            openAPI.addServersItem(new Server().url(serverUrl));
        }
        return openAPI;
    }
}