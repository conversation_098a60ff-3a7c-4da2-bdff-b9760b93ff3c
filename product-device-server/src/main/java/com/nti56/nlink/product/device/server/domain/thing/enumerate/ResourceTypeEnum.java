package com.nti56.nlink.product.device.server.domain.thing.enumerate;

import lombok.Getter;

/**
 * 标识绑定资源类型
 * <AUTHOR>
 * @ClassName ResourceTypeEnum
 * @date 2022/4/14 8:48
 * @Version 1.0
 */
public enum ResourceTypeEnum {

    PRODUCT(1, "product", "product"),
    DEVICE(2, "device", "device"),
    EDGE_GATEWAY(3, "edgeGateway", "edgeGateway"),
    LABEL(4,"label","label"),
    THING_MODEL(5,"thingModel","thingModel"),
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    ResourceTypeEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static ResourceTypeEnum typeOfValue(Integer value){
        ResourceTypeEnum[] values = ResourceTypeEnum.values();
        for (ResourceTypeEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static ResourceTypeEnum typeOfName(String name){
        ResourceTypeEnum[] values = ResourceTypeEnum.values();
        for (ResourceTypeEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static ResourceTypeEnum typeOfNameDesc(String nameDesc){
        ResourceTypeEnum[] values = ResourceTypeEnum.values();
        for (ResourceTypeEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
