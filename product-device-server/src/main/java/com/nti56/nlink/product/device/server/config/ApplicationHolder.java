package com.nti56.nlink.product.device.server.config;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * 类说明：
 *
 * @ClassName VertxHolder
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/9/7 17:48
 * @Version 1.0
 */

@Component
public class ApplicationHolder implements ApplicationContextAware {

    private static ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        ApplicationHolder.applicationContext = applicationContext;
    }

    public static ApplicationContext getApplicationContext(){
        return applicationContext;
    }


}
