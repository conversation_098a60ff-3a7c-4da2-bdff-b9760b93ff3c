package com.nti56.nlink.product.device.server.domain.thing.modelbase;

import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.client.model.dto.json.condition.LeftElm;
import com.nti56.nlink.product.device.client.model.dto.json.condition.RightElm;
import com.nti56.nlink.product.device.client.model.dto.json.condition.TriggerConditionElm;
import com.nti56.nlink.product.device.client.model.dto.json.enumerate.ThingDataTypeEnum;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.BracketEnum;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.ExtractionItemEnum;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.TriggerConditionOperatorEnum;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.TriggerConditionTypeEnum;
import com.nti56.nlink.product.device.server.domain.thing.label.LabelBindRelation;
import com.nti56.nlink.product.device.server.util.RegexUtil;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 类说明: 事件触发器领域对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-16 15:16:35
 * @since JDK 1.8
 */
@Slf4j
public class EventTrigger {

    public static final Map<String,FaultStatus> faultStateMap = new ConcurrentHashMap<>();
    
    @Getter
    private List<TriggerConditionElm> triggerConditionElmList;

    private static boolean checkExtraction(List<ExtractionItemEnum> extraction) {
        if(extraction == null || extraction.size() <= 0){
            return true;
        }
        ExtractionItemEnum flip = ExtractionItemEnum.EXPRESSION;
        for(ExtractionItemEnum item:extraction){
            if(!flip.equals(item)){
                return false;
            }
            flip = flip.flip();
        }
        //最后一个是EXPRESSION，那么flip最后就会是LOGIC
        if(ExtractionItemEnum.LOGIC.equals(flip)){
            return true;
        }
        return false;
    }

    public static Result<EventTrigger> checkInfo(
        List<TriggerConditionElm> triggerConditionElmList, 
        Map<String, Property> propertyNameMap
    ){
        if(triggerConditionElmList == null || triggerConditionElmList.size() <= 0){
            return Result.error("触发条件元素不能为空");
        }

        //检查触发条件格式是否正确
        
        //括号栈
        Stack<BracketEnum> bracketStack = new Stack<>();

        //层级 -> ["expression", "logic", "expression"]
        Map<Integer, List<ExtractionItemEnum>> levelExtractionMap = new HashMap<>();
        for(TriggerConditionElm elm:triggerConditionElmList){
            
            Integer level = elm.getLevel();
            if(level == null){
                return Result.error("触发条件元素level不能为空");
            }

            //检查类型
            TriggerConditionTypeEnum type = TriggerConditionTypeEnum.typeOfName(elm.getType());
            switch (type) {
                case COMPARE:{
                    //检查level，没有出入栈，可以直接检查
                    if(!level.equals(bracketStack.size())){
                        return Result.error("触发条件元素level错误, level:" + level + ", bracketStackSize:" + bracketStack.size());
                    }
                    //检查左中右格式
                    LeftElm left = elm.getLeft();
                    if(left == null){
                        return Result.error("触发条件元素格式left错误, type" + elm.getType() + ", left:" + left);
                    }
                    RightElm right = elm.getRight();
                    if(right == null){
                        return Result.error("触发条件元素格式right错误, type" + elm.getType() + ", right:" + right);
                    }
                    TriggerConditionOperatorEnum operator = TriggerConditionOperatorEnum.typeOfValue(elm.getOperator());
                    if(operator == null){
                        return Result.error("触发条件元素格式operator错误, type" + elm.getType() + ", operator:" + elm.getOperator());
                    }

                    //检查条件属性是否是物模型中的属性
                    String property = left.getProperty();
                    if(property == null){
                        return Result.error("触发条件元素格式left错误, type" + elm.getType() + ", left:" + left);
                    }
                    if(!propertyNameMap.containsKey(property)){
                        return Result.error("触发条件元素属性不在模型属性列表中, type:" + elm.getType() + ", property:" + property);
                    }

                    //检查左右类型是否一致
                    Property thingProperty = propertyNameMap.get(property);
                    ThingDataTypeEnum dataType = thingProperty.getDataType().getType();
                    String value = right.getValue();
                    boolean checkResult = RegexUtil.checkValueString(value, dataType);
                    if(!checkResult){
                        return Result.error("触发条件元素属性类型值错误, dataType:" + dataType.getName() + ", value:" + value);
                    }
                    elm.setPropertyType(dataType);

                    //记录层级表达式抽象
                    List<ExtractionItemEnum> extraction = levelExtractionMap.get(level);
                    if(extraction == null){
                        extraction = new ArrayList<>();
                        levelExtractionMap.put(level, extraction);
                    }
                    extraction.add(ExtractionItemEnum.EXPRESSION);

                    //检查逻辑符类型
                    String logic = elm.getLogic();
                    if(logic != null && !"".equals(logic)){
                        if(!logic.equals("and") && !logic.equals("or")){
                            return Result.error("触发条件元素属性逻辑符号错误, logic:" + logic);
                        }
                        extraction.add(ExtractionItemEnum.LOGIC);
                    }
                    break;
                }
                case BRACKET: {
                    BracketEnum bracket = BracketEnum.typeOfValue(elm.getBracket());
                    if(bracket == null){
                        return Result.error("触发条件元素括号错误, bracket:" + elm.getBracket());
                    }
                    if(BracketEnum.LEFT.equals(bracket)){
                        //左括号
                        //检查level，入栈前，检查层级
                        if(!level.equals(bracketStack.size())){
                            return Result.error("触发条件元素level错误, level:" + level + ", bracketStackSize:" + bracketStack.size());
                        }
                        //记录层级表达式抽象，左括号记录表达式
                        List<ExtractionItemEnum> extraction = levelExtractionMap.get(level);
                        if(extraction == null){
                            extraction = new ArrayList<>();
                            levelExtractionMap.put(level, extraction);
                        }
                        extraction.add(ExtractionItemEnum.EXPRESSION);
                        //入栈
                        bracketStack.add(bracket);
                    }else{
                        //右括号
                        //检查当前层级表达式抽象是否正确
                        List<ExtractionItemEnum> subExtraction = levelExtractionMap.get(bracketStack.size());
                        if(!checkExtraction(subExtraction)){
                            return Result.error("表达式逻辑错误");
                        }

                        //清空当前层级表达式抽象
                        levelExtractionMap.remove(bracketStack.size());
                        
                        //记录层级表达式抽象，右括号记录逻辑符
                        List<ExtractionItemEnum> extraction = levelExtractionMap.get(level);
                        //检查逻辑符类型
                        String logic = elm.getLogic();
                        if(logic != null && !"".equals(logic)){
                            if(!logic.equals("and") && !logic.equals("or")){
                                return Result.error("触发条件元素属性逻辑符号错误, logic:" + logic);
                            }
                            extraction.add(ExtractionItemEnum.LOGIC);
                        }

                        //出栈
                        if(bracketStack.empty()){
                            return Result.error("括号不配对");
                        }
                        bracketStack.pop();
                        //检查level，出栈后，检查层级
                        if(!level.equals(bracketStack.size())){
                            return Result.error("触发条件元素level错误, level:" + level + ", bracketStackSize:" + bracketStack.size());
                        }
                    }
                    break;
                }
                case SINGLE: {
                    //检查level，没有出入栈，可以直接检查
                    if(!level.equals(bracketStack.size())){
                        return Result.error("触发条件元素level错误, level:" + level + ", bracketStackSize:" + bracketStack.size());
                    }
                    //检查单个格式
                    LeftElm single = elm.getSingle();
                    if(single == null){
                        return Result.error("触发条件元素格式single错误, type" + elm.getType() + ", single:" + single);
                    }
                    
                    //记录层级表达式抽象
                    List<ExtractionItemEnum> extraction = levelExtractionMap.get(level);
                    if(extraction == null){
                        extraction = new ArrayList<>();
                        levelExtractionMap.put(level, extraction);
                    }
                    extraction.add(ExtractionItemEnum.EXPRESSION);

                    //检查逻辑符类型
                    String logic = elm.getLogic();
                    if(logic != null && !"".equals(logic)){
                        if(!logic.equals("and") && !logic.equals("or")){
                            return Result.error("触发条件元素属性逻辑符号错误, logic:" + logic);
                        }
                        extraction.add(ExtractionItemEnum.LOGIC);
                    }
                    break;
                }
                default: {
                    return Result.error("触发条件元素类型错误, type" + elm.getType());
                }
            }
        }

        //检查括号是否配对
        if(bracketStack.size() > 0){
            return Result.error("括号不配对");
        }
        
        //检查顶层表达式抽象是否正确
        List<ExtractionItemEnum> subExtraction = levelExtractionMap.get(0);
        if(!checkExtraction(subExtraction)){
            return Result.error("表达式逻辑错误");
        }

        EventTrigger eventTrigger = new EventTrigger();
        eventTrigger.triggerConditionElmList = triggerConditionElmList;

        return Result.ok(eventTrigger);
    }

    @Data
    public static class CompileInfo {
        private String compileStr;
        private Set<String> conditionProperties;
        public CompileInfo(String compileStr, Set<String> conditionProperties) {
            this.compileStr = compileStr;
            this.conditionProperties = conditionProperties;
        }
    }

    //编译表达式
    public CompileInfo compile(Map<String, LabelBindRelation> propertyLabelMap,Boolean isDebug){
        
        Set<String> conditionProperties = new HashSet<>();
        StringBuilder sb = new StringBuilder();
        for(TriggerConditionElm elm:triggerConditionElmList){
            sb.append(" ");
            TriggerConditionTypeEnum type = TriggerConditionTypeEnum.typeOfName(elm.getType());
            switch (type) {
                case COMPARE:{
                    //把property转化成#prop.get({labelId})
                   //debug把property转化成#prop.get({propertyName})
                    String property = elm.getLeft().getProperty();
                    sb.append(" #prop.get(");
                    sb.append("\'")
                            .append(property)
                            .append("\') ");
                    conditionProperties.add(property);
                    sb.append(TriggerConditionOperatorEnum.typeOfValue(elm.getOperator()).getSpelValue());
                    sb.append(" ");
                    //字符串自动增加""
                    if(ThingDataTypeEnum.STRING.equals(elm.getPropertyType())){
                        sb.append("\"");
                    }
                    sb.append(elm.getRight().getValue());
                    if(ThingDataTypeEnum.STRING.equals(elm.getPropertyType())){
                        sb.append("\"");
                    }
                    sb.append(" ");
                    String logic = elm.getLogic();
                    if(logic != null  && !"".equals(logic)){
                        sb.append(" ")
                            .append(logic)
                            .append(" ");
                    }
                    break;
                }
                case BRACKET: {
                    BracketEnum bracket = BracketEnum.typeOfValue(elm.getBracket());
                    if(BracketEnum.LEFT.equals(bracket)){
                        sb.append(" ( ")
                            ;
                    }else{
                        sb.append(" ) ")
                        ;
                        String logic = elm.getLogic();
                        if(logic != null  && !"".equals(logic)){
                            sb.append(" ")
                                .append(logic)
                                .append(" ");
                        }
                    }
                    break;
                }
                case SINGLE: {
                    sb.append(" ")
                        .append(elm.getSingle().getEvent())
                        .append(" ")
                        ;
                    String logic = elm.getLogic();
                    if(logic != null  && !"".equals(logic)){
                        sb.append(" ")
                            .append(logic)
                            .append(" ");
                    }
                    break;
                }
                default: {

                }
            }
        }
        String compileStr = sb.toString();
        return new CompileInfo(compileStr, conditionProperties);
    }

    public List<String> listConditionProperty() {
        return listConditionProperty(this.triggerConditionElmList);
    }

    public static List<String> listConditionProperty(List<TriggerConditionElm> triggerConditionElmList){
        return triggerConditionElmList.stream()
                .filter(t -> {
                    return TriggerConditionTypeEnum.COMPARE.getName()
                            .equals(t.getType());
                })
                .map(t -> {
                    return t.getLeft().getProperty();
                })
                .collect(Collectors.toList());
    }

}
