package com.nti56.nlink.product.device.server.domain.thing.channel;

import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.client.model.dto.json.ChannelRuntimeInfoField;
import com.nti56.nlink.product.device.client.model.dto.json.device.ChannelElm;
import com.nti56.nlink.product.device.server.util.RegexUtil;
import lombok.Getter;

import java.util.Map;
import java.util.HashMap;
import java.util.List;
import com.nti56.nlink.product.device.server.entity.ChannelParamEntity;


/**
 * 类说明: modbus驱动通道参数领域对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-07-12 09:26:25
 * @since JDK 1.8
 */
@Getter
public class ModbusChannelParam extends ChannelParam{
    private String ip;
    private Integer port;
    private Integer slaveId;
    private String endianness;
    private String firstAddress;
    private Integer retryCount;
    private Integer queueTimeout;
    
    public static final String[] requiredParam = new String[]{
            "ip::true",
            "port::true",
            "slaveId::true",
            "endianness:ABCD:true:ABCD,BADC,CDAB,DCBA:大小端配置",
            "firstAddress:是:true:是,否:首地址是否从0开始",
            "retryCount:0:true::重试次数，不能小于0",
            "queueTimeout:10:true::队列超时清除时间（秒），不能小于10",
            "reconnectGapMs:3000:true::断线重连间隔（毫秒）",
            "maxConnection:1:true::通道最多连接数",
            "delayIdleMs:0:true::延迟空闲时间（毫秒）"
    };

    
    public static Result<ChannelParam> checkParam(List<ChannelParamEntity> channelParamList){
        
        ModbusChannelParam param = new ModbusChannelParam();

        Result<Map<String, String>> baseResult = ChannelParam.checkBase(
            param, requiredParam, channelParamList
        );
        if(!baseResult.getSignal()){
            return Result.error(baseResult.getMessage());
        }
        Map<String, String> paramMap = baseResult.getResult();

        //ip
        String ipStr = paramMap.get("ip");
        if(ipStr == null){
            return Result.error("通道参数缺少ip");
        }
        if(!RegexUtil.checkIpv4(ipStr)){
            return Result.error("通道ip格式错误，ip:" + ipStr);
        }
        param.ip = ipStr;

        //port
        String portStr = paramMap.get("port");
        if(portStr == null){
            return Result.error("通道参数缺少port");
        }
        if(!RegexUtil.checkPort(portStr)){
            return Result.error("通道port格式错误，port:" + portStr);
        }
        param.port = Integer.parseInt(portStr);

        //channelKey
        param.channelKey = param.ip + param.port;

        //slaveId
        String slaveIdStr = paramMap.get("slaveId");
        if(slaveIdStr == null){
            return Result.error("通道参数缺少slaveId");
        }
        if(!RegexUtil.checkInt(slaveIdStr)){
            return Result.error("通道slaveId格式错误，slaveId:" + slaveIdStr);
        }
        param.slaveId = Integer.parseInt(slaveIdStr);

        String endiannessStr = paramMap.get("endianness");
        if(endiannessStr == null){
            return Result.error("通道参数缺少endianness");
        }
        param.endianness = endiannessStr;

        String firstAddressStr = paramMap.get("firstAddress");
        if(firstAddressStr == null){
            return Result.error("通道参数缺少firstAddress");
        }
        param.firstAddress = firstAddressStr;

        
        //retryCount
        String retryCountStr = paramMap.get("retryCount");
        if(retryCountStr == null){
            return Result.error("通道参数缺少retryCount");
        }
        if(!RegexUtil.checkPort(retryCountStr)){
            return Result.error("通道retryCount格式错误，retryCount:" + retryCountStr);
        }
        Integer retryCount = Integer.parseInt(retryCountStr);
        if(retryCount < 0){
            return Result.error("通道retryCount不能小于0，retryCount:" + retryCountStr);
        }
        param.retryCount = retryCount;

        //queueTimeout
        String queueTimeoutStr = paramMap.get("queueTimeout");
        if(queueTimeoutStr == null){
            return Result.error("通道参数缺少queueTimeout");
        }
        if(!RegexUtil.checkInt(queueTimeoutStr)){
            return Result.error("通道queueTimeout格式错误，queueTimeout:" + queueTimeoutStr);
        }
        Integer queueTimeout = Integer.parseInt(queueTimeoutStr);
        if(queueTimeout < 10){
            return Result.error("通道queueTimeout不能小于10秒，queueTimeout:" + queueTimeoutStr);
        }
        param.queueTimeout = queueTimeout;

        return Result.ok(param);

    }

    @Override
    public void processRuntimeInfo(ChannelRuntimeInfoField info) {
        processBaseRuntimeInfo(info);
        
        info.setIp(ip);
        info.setPort(port);
        info.setSlaveId(slaveId);
        info.setEndianness(endianness);
        info.setFirstAddress(firstAddress);
        info.setRetryCount(retryCount);
        info.setQueueTimeout(queueTimeout);
    }

    @Override
    public void processChannelElm(ChannelElm channelElm) {
        processBaseChannelElm(channelElm);
       
        channelElm.setIp(ip);
        channelElm.setPort(port);
        channelElm.setSlaveId(slaveId);
        channelElm.setEndianness(endianness);
        channelElm.setFirstAddress(firstAddress);
        channelElm.setRetryCount(retryCount);
        channelElm.setQueueTimeout(queueTimeout);
    }

    
}
