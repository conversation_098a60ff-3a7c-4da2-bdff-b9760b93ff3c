package com.nti56.nlink.product.device.server.domain.thing.modelbase;

import com.nti56.nlink.product.device.server.domain.thing.up.UpProp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName EventData
 * @date 2022/9/28 9:18
 * @Version 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class FaultData {

    public Integer faultLevel;
    public Integer faultStatus;
    public Long faultStartTime;
    public Long faultEndTime;
    public Long faultOverTime;

}
