package com.nti56.nlink.product.device.server.domain.thing.enumerate;

import lombok.Getter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 类说明: 状态枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 13:33:25
 * @since JDK 1.8
 */
public enum StatusEnum {
    INACTIVATED(0, "inactivated", "未激活"),
    OFFLINE(1, "offline", "离线"),
    ONLINE(2, "online", "上线")
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    StatusEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static StatusEnum typeOfValue(Integer value){
        StatusEnum[] values = StatusEnum.values();
        for (StatusEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static StatusEnum typeOfName(String name){
        StatusEnum[] values = StatusEnum.values();
        for (StatusEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static StatusEnum typeOfNameDesc(String nameDesc){
        StatusEnum[] values = StatusEnum.values();
        for (StatusEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }


    public static List toList(){
        List<Map> result = new ArrayList<>();
        StatusEnum[] values = StatusEnum.values();
        Map<String,Object> map ;
        for (StatusEnum v : values) {
            map = new HashMap<>();
            map.put("name",v.name);
            map.put("value",v.value);
            map.put("nameDesc",v.nameDesc);
            result.add(map);
        }
        return result;
    }
}
