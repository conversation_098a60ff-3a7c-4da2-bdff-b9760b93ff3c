package com.nti56.nlink.product.device.server.domain.thing.enumerate;

import lombok.Getter;

public enum DriverCheckTypeEnum {
    
    LRC(1, "LRC", "LRC"), 
    CRC_8(2, "CRC_8", "CRC-8"),
    CRC_8_CDMA2000(3, "CRC_8_CDMA2000", "CRC-8/CDMA2000"),
    CRC_8_DARC(4, "CRC_8_DARC", "CRC-8/DARC"),
    CRC_8_DVB_S2(5, "CRC_8_DVB_S2", "CRC-8/DVB-S2"),
    CRC_8_EBU(6, "CRC_8_EBU", "CRC-8/EBU"),
    CRC_8_I_CODE(7, "CRC_8_I_CODE", "CRC-8/I-CODE"),
    CRC_8_ITU(8, "CRC_8_ITU", "CRC-8/ITU"),
    CRC_8_MAXIM(9, "CRC_8_MAXIM", "CRC-8/MAXIM"),
    CRC_8_ROHC(10, "CRC_8_ROHC", "CRC-8/ROHC"),
    CRC_8_WCDMA(11, "CRC_8_WCDMA", "CRC-8/WCDMA"),
    CRC_16_CCITT_FALSE(12, "CRC_16_CCITT_FALSE", "CRC-16/CCITT-FALSE"),
    CRC_16_ARC(13, "CRC_16_ARC", "CRC-16/ARC"),
    CRC_16_AUG_CCITT(14, "CRC_16_AUG_CCITT", "CRC-16/AUG-CCITT"),
    CRC_16_BUYPASS(15, "CRC_16_BUYPASS", "CRC-16/BUYPASS"),
    CRC_16_CDMA2000(16, "CRC_16_CDMA2000", "CRC-16/CDMA2000"),
    CRC_16_DDS_110(17, "CRC_16_DDS_110", "CRC-16/DDS-110"),
    CRC_16_DECT_R(18, "CRC_16_DECT_R", "CRC-16/DECT-R"),
    CRC_16_DECT_X(19, "CRC_16_DECT_X", "CRC-16/DECT-X"),
    CRC_16_DNP(20, "CRC_16_DNP", "CRC-16/DNP"),
    CRC_16_EN_13757(21, "CRC_16_EN_13757", "CRC-16/EN-13757"),
    CRC_16GENIBUS(22, "CRC_16GENIBUS", "CRC-16/GENIBUS"),
    CRC_16_MAXIM(23, "CRC_16_MAXIM", "CRC-16/MAXIM"),
    CRC_16_MCRF4XX(24, "CRC_16_MCRF4XX", "CRC-16/MCRF4XX"),
    CRC_16_RIELLO(25, "CRC_16_RIELLO", "CRC-16/RIELLO"),
    CRC_16_T10_DIF(26, "CRC_16_T10_DIF", "CRC-16/T10-DIF"),
    CRC_16_TELEDISK(27, "CRC_16_TELEDISK", "CRC-16/TELEDISK"),
    CRC_16_TMS37157(28, "CRC_16_TMS37157", "CRC-16/TMS37157"),
    CRC_16_USB(29, "CRC_16_USB", "CRC-16/USB"),
    CRC_A(30, "CRC_A", "CRC-A"),
    CRC_16_KERMIT(31, "CRC_16_KERMIT", "CRC-16/KERMIT"),
    CRC_16_MODBUS(32, "CRC_16_MODBUS", "CRC-16/MODBUS"),
    CRC_16_X_25(33, "CRC_16_X_25", "CRC-16/X-25"),
    CRC_16_XMODEM(34, "CRC_16_XMODEM", "CRC-16/XMODEM"),
    CRC_32(35, "CRC_32", "CRC-32"),
    CRC_32_BZIP2(36, "CRC_32_BZIP2", "CRC-32/BZIP2"),
    CRC_32C(37, "CRC_32C", "CRC-32C"),
    CRC_32D(38, "CRC_32D", "CRC-32D"),
    CRC_32_JAMCRC(39, "CRC_32_JAMCRC", "CRC-32/JAMCRC"),
    CRC_32_MPEG_2(40, "CRC_32_MPEG_2", "CRC-32/MPEG-2"),
    CRC_32_POSIX(41, "CRC_32_POSIX", "CRC-32/POSIX"),
    CRC_32Q(42, "CRC_32Q", "CRC-32Q"),
    CRC_32_XFER(43, "CRC_32_XFER", "CRC-32/XFER"),
    CRC_64(44, "CRC_64", "CRC-64"),
    CRC_64_WE(45, "CRC_64_WE", "CRC-64/WE"),
    CRC_64_XZ(46, "CRC_64_XZ", "CRC-64/XZ"),
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    DriverCheckTypeEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static DriverCheckTypeEnum typeOfValue(Integer value){
        DriverCheckTypeEnum[] values = DriverCheckTypeEnum.values();
        for (DriverCheckTypeEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static DriverCheckTypeEnum typeOfName(String name){
        DriverCheckTypeEnum[] values = DriverCheckTypeEnum.values();
        for (DriverCheckTypeEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static DriverCheckTypeEnum typeOfNameDesc(String nameDesc){
        DriverCheckTypeEnum[] values = DriverCheckTypeEnum.values();
        for (DriverCheckTypeEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
