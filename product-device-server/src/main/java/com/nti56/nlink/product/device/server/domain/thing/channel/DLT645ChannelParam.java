package com.nti56.nlink.product.device.server.domain.thing.channel;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.client.model.dto.json.ChannelRuntimeInfoField;
import com.nti56.nlink.product.device.client.model.dto.json.device.ChannelElm;
import com.nti56.nlink.product.device.server.entity.ChannelParamEntity;
import com.nti56.nlink.product.device.server.util.RegexUtil;
import lombok.Getter;

import java.util.List;
import java.util.Map;


/**
 * 类说明: AB plc/EtherIp驱动通道参数领域对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-08-10 14:29:25
 * @since JDK 1.8
 */
@Getter
public class DLT645ChannelParam extends ChannelParam {
    private String ip;
    private Integer port;

    private String meterAddress;

    private String password;

    public static final String[] requiredParam = new String[]{
            "ip::true",
            "port::true",
            "reconnectGapMs:3000:true::断线重连间隔（毫秒）",
            "maxConnection:1:true::通道最多连接数",
            "delayIdleMs:0:true::延迟空闲时间（毫秒）",
            "meterAddress::true::电表号",
            "password::true::电表密码"
    };


    public static Result<ChannelParam> checkParam(List<ChannelParamEntity> channelParamList) {

        DLT645ChannelParam param = new DLT645ChannelParam();

        Result<Map<String, String>> baseResult = ChannelParam.checkBase(
                param, requiredParam, channelParamList
        );
        if (!baseResult.getSignal()) {
            return Result.error(baseResult.getMessage());
        }
        Map<String, String> paramMap = baseResult.getResult();

        //ip
        String ipStr = paramMap.get("ip");
        if (ipStr == null) {
            return Result.error("通道参数缺少ip");
        }
       /* if(!RegexUtil.checkIpv4(ipStr)){
            return Result.error("通道ip格式错误，ip:" + ipStr);
        }*/
        param.ip = ipStr;

        //port
        String portStr = paramMap.get("port");
        if (portStr == null) {
            return Result.error("通道参数缺少port");
        }
       /* if(!RegexUtil.checkPort(portStr)){
            return Result.error("通道port格式错误，port:" + portStr);
        }*/
        param.port = Integer.parseInt(portStr);

        //channelKey
        param.channelKey = param.ip + param.port;

        if (CollectionUtil.isNotEmpty(channelParamList)) {
            channelParamList.forEach(cp -> {
                if ("meterAddress".equals(cp.getName())) {
                    param.meterAddress = cp.getValue();
                }
                if ("password".equals(cp.getName())) {
                    param.password = cp.getValue();
                }
            });
        }
        if (StrUtil.isBlank(param.meterAddress)) {
            return Result.error("通道参数缺少电表号");
        }
        if (StrUtil.isBlank(param.password)) {
            return Result.error("通道参数缺少电表密码");
        }

        return Result.ok(param);

    }

    @Override
    public void processRuntimeInfo(ChannelRuntimeInfoField info) {
        processBaseRuntimeInfo(info);
        info.setIp(ip);
        info.setPort(port);
        info.setPassword(password);
        info.setMeterAddress(meterAddress);
    }

    @Override
    public void processChannelElm(ChannelElm channelElm) {
        processBaseChannelElm(channelElm);
        channelElm.setIp(ip);
        channelElm.setPort(port);
        channelElm.setPassword(password);
        channelElm.setMeterAddress(meterAddress);
    }


}
