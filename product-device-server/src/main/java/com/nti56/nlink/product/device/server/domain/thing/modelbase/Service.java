package com.nti56.nlink.product.device.server.domain.thing.modelbase;

import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.client.model.dto.json.InputDataField;
import com.nti56.nlink.product.device.client.model.dto.json.OutputDataField;
import com.nti56.nlink.product.device.server.domain.thing.dpo.ServiceDpo;
import com.nti56.nlink.product.device.server.model.inherit.ServiceOfInherit;
import com.nti56.nlink.product.device.server.util.RegexUtil;
import lombok.Getter;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;

/**
 * 类说明: 物模型服务领域对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-24 16:41:31
 * @since JDK 1.8
 */
@Getter
public class Service {
    
    private Long id;

    private String serviceName;

    private String descript;

    private Boolean override; //是否运行覆盖

    private Boolean covered; //是否被覆盖

    private Boolean async;

    private InputDataField[] inputData;

    private OutputDataField outputData;

    private String outputDataDescript;

    private String serviceCode;

    private ServiceEntityBase raw;

    //直属物模型id
    private Long thingModelId;

    //直属物模型名称
    private String thingModelName;

    private Integer serviceType;

    public static Result<Service> checkInfo( Long thingModelId,
                                             String thingModelName,
                                             ServiceEntityBase serviceEntity
    ) {
        
        if(serviceEntity == null){
            return Result.error("服务不能为空");
        }

        Long id = serviceEntity.getId();
        if(id== null){
            return Result.error("服务id不能为空");
        }

        Service service = new Service();

        service.id = serviceEntity.getId();
        
        String nameStr = serviceEntity.getServiceName();
        if(nameStr == null){
            return Result.error("服务名称不能为空");
        }

        //名称
        if(!RegexUtil.checkName(nameStr)){
            return Result.error(
                "服务名称格式错误，只支持英文数字下划线，且英文首字母，name: " + nameStr
            );
        }
        service.serviceName = nameStr;

        service.descript = serviceEntity.getDescript();

        service.override = serviceEntity.getOverride();
        service.async = serviceEntity.getAsync();
        
        service.inputData = serviceEntity.getInputData();
        service.outputData = serviceEntity.getOutputData();
        service.outputDataDescript = serviceEntity.getOutputDataDescript();
        service.serviceCode = serviceEntity.getServiceCode();
        service.thingModelId = thingModelId;
        service.thingModelName = thingModelName;
        service.serviceType = serviceEntity.getServiceType();

        service.raw = serviceEntity;
        return Result.ok(service);
    }

    
    public static Result<List<Service>> batchCheckInfo( Long thingModelId,
                                                        String thingModelName,List<ServiceEntityBase> serviceEntities){
        List<Service> services = new ArrayList<>();
        if(serviceEntities != null && serviceEntities.size() > 0){
            //自定义服务
            for(ServiceEntityBase serviceEntity:serviceEntities){
                Result<Service> serviceResult = Service.checkInfo(thingModelId,thingModelName,serviceEntity);
                if(!serviceResult.getSignal()){
                    return Result.error(serviceResult.getMessage());
                }
                services.add(serviceResult.getResult());
            }
        }
        return Result.ok(services);
    }

    public static Result<List<Service>> checkRepeat(
        List<Service> inheritServices
    ){
        //服务名称不能重复
        if(!CollectionUtils.isEmpty(inheritServices)){
            Map<String,Long> servicesCountMap=inheritServices.stream()
                .map(Service::getServiceName)
                .collect(Collectors.groupingBy(p->p,Collectors.counting()));
            for(Entry<String,Long> entry:servicesCountMap.entrySet()){
                if(entry.getValue()>1){
                    return Result.error("物模型服务名重复，name:"+entry.getKey());
                }
            }
        }
        return Result.ok(inheritServices);
    }

    public static Result<List<Service>> checkOverride(
        List<Service> inheritServices,
        List<Service> selfServices
    ){
        List<Service> services = new ArrayList<>();
        if(inheritServices != null){
            services.addAll(inheritServices);
        }
        if(selfServices != null){
            services.addAll(selfServices);
        }


        //服务名称覆盖检查
        Map<String, Service> pickMap = new HashMap<>();

        if(services != null && services.size() > 0){
            Map<String, List<Service>> serviceMap = services.stream().collect(
                    Collectors.groupingBy(Service::getServiceName)
            );
            for(Entry<String, List<Service>> entry:serviceMap.entrySet()){
                String serviceName = entry.getKey();
                List<Service> list = entry.getValue();
                final Integer size = list.size();
                if(size > 1){
                    Boolean canOverride = true;
                    for(Service service:list){
                        if(!canOverride){
                            return Result.error("物模型服务不能覆盖，serviceName:" + service.getServiceName());
                        }
                        canOverride = service.getOverride();
                    }
                    pickMap.put(serviceName, list.get(size - 1));
                    for(int i=0;i<size-1;i++){
                        list.get(i).covered = true;
                    }
                }else if(size == 1){
                    pickMap.put(serviceName, list.get(0));
                }
            }
        }
        
        List<Service> overrideServices = new ArrayList<>();
        for(Service tmp:services){
            String serviceName = tmp.getServiceName();
            Service service = pickMap.get(serviceName);
            if(service != null){
                overrideServices.add(service);
                pickMap.remove(serviceName);
            }
        }

        return Result.ok(overrideServices);
    }


    public ServiceOfInherit toServiceOfInherit() {
        ServiceOfInherit e = new ServiceOfInherit();
        BeanUtils.copyProperties(raw, e);
        e.setBaseThingModelId(thingModelId);
        e.setBaseThingModelName(thingModelName);
        return e;
    }


    public ServiceDpo toDpo() {
        ServiceDpo dpo = new ServiceDpo();
        dpo.setId(id);
        dpo.setThingModelId(thingModelId);
        dpo.setThingModelName(thingModelName);
        dpo.setServiceName(serviceName);
        dpo.setOverride(override);
        dpo.setAsync(async);
        dpo.setInputData(inputData);
        dpo.setOutputData(outputData);
        dpo.setServiceCode(serviceCode);
        dpo.setCovered(covered);
        dpo.setDescript(raw.getDescript());
        dpo.setServiceType(serviceType);
        return dpo;
    }

}
