package com.nti56.nlink.product.device.server.proxy;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.client.model.dto.json.ChannelRuntimeInfoField;
import com.nti56.nlink.product.device.client.model.dto.json.GatherParamField;
import com.nti56.nlink.product.device.client.model.dto.json.CustomDriverRuntimeInfoField;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.MqttTopicEnum;
import com.nti56.nlink.product.device.server.feign.IEdgeGatewayControlProxy;
import com.nti56.nlink.product.device.server.model.ComputeTaskBo;
import com.nti56.nlink.product.device.server.model.datasync.SyncEdgeGatewayDto;
import com.nti56.nlink.product.device.server.model.edgegateway.vo.HardwareInfo;
import com.nti56.nlink.product.device.server.util.MqttProxyEventBusUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 类说明: 边缘网关控制代理 - EventBus实现
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024-01-01 00:00:00
 * @since JDK 1.8
 */
@Component
public class EdgeGatewayControlProxy implements IEdgeGatewayControlProxy {

    @Autowired
    private MqttProxyEventBusUtil mqttProxyEventBusUtil;

    @Override
    public Result<Void> stopProxy(Long edgeGatewayId, Long tenantId) {
        try {
            Map<String, Object> params = new HashMap<>();
            String body = JSONObject.toJSONString(params);
            
            CompletableFuture<String> future = mqttProxyEventBusUtil.sendAssignRequest(
                MqttTopicEnum.OT_CONTROL, 
                "send", 
                tenantId.toString(), 
                edgeGatewayId.toString(), 
                "/stopProxy", 
                body
            );
            
            String response = future.get();
            return JSONObject.parseObject(response, Result.class);
        } catch (Exception e) {
            return Result.error("停止代理失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> startProxy(Long edgeGatewayId, Long tenantId) {
        try {
            Map<String, Object> params = new HashMap<>();
            String body = JSONObject.toJSONString(params);
            
            CompletableFuture<String> future = mqttProxyEventBusUtil.sendAssignRequest(
                MqttTopicEnum.OT_CONTROL, 
                "send", 
                tenantId.toString(), 
                edgeGatewayId.toString(), 
                "/startProxy", 
                body
            );
            
            String response = future.get();
            return JSONObject.parseObject(response, Result.class);
        } catch (Exception e) {
            return Result.error("启动代理失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> syncGatherTask(Long edgeGatewayId, Long tenantId, 
                                      List<GatherParamField> gatherParamList, 
                                      List<ChannelRuntimeInfoField> channelRuntimeInfoList) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("gatherParamList", gatherParamList);
            params.put("channelRuntimeInfoList", channelRuntimeInfoList);
            String body = JSONObject.toJSONString(params);
            
            CompletableFuture<String> future = mqttProxyEventBusUtil.sendAssignRequest(
                MqttTopicEnum.OT_CONTROL, 
                "request", 
                tenantId.toString(), 
                edgeGatewayId.toString(), 
                "/syncGatherTask", 
                body
            );
            
            String response = future.get();
            return JSONObject.parseObject(response, Result.class);
        } catch (Exception e) {
            return Result.error("同步采集任务失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> syncCustomDriver(Long edgeGatewayId, Long tenantId, 
                                        List<CustomDriverRuntimeInfoField> customDriverRuntimeInfoList) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("customDriverRuntimeInfoList", customDriverRuntimeInfoList);
            String body = JSONObject.toJSONString(params);
            
            CompletableFuture<String> future = mqttProxyEventBusUtil.sendAssignRequest(
                MqttTopicEnum.OT_CONTROL, 
                "request", 
                tenantId.toString(), 
                edgeGatewayId.toString(), 
                "/syncCustomDriver", 
                body
            );
            
            String response = future.get();
            return JSONObject.parseObject(response, Result.class);
        } catch (Exception e) {
            return Result.error("同步自定义驱动失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> syncComputeTask(Long edgeGatewayId, Long tenantId, List<ComputeTaskBo> computeTaskList) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("computeTaskList", computeTaskList);
            String body = JSONObject.toJSONString(params);
            
            CompletableFuture<String> future = mqttProxyEventBusUtil.sendAssignRequest(
                MqttTopicEnum.OT_CONTROL, 
                "request", 
                tenantId.toString(), 
                edgeGatewayId.toString(), 
                "/syncComputeTask", 
                body
            );
            
            String response = future.get();
            return JSONObject.parseObject(response, Result.class);
        } catch (Exception e) {
            return Result.error("同步计算任务失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> disconnectOt(Long edgeGatewayId, Long tenantId, String heartbeatUuid) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("heartbeatUuid", heartbeatUuid);
            String body = JSONObject.toJSONString(params);
            
            CompletableFuture<String> future = mqttProxyEventBusUtil.sendAssignRequest(
                MqttTopicEnum.OT_CONTROL, 
                "send", 
                tenantId.toString(), 
                edgeGatewayId.toString(), 
                "/disconnectOt", 
                body
            );
            
            String response = future.get();
            return JSONObject.parseObject(response, Result.class);
        } catch (Exception e) {
            return Result.error("断开OT连接失败: " + e.getMessage());
        }
    }

    @Override
    public Result<String> updateLogView(Long edgeGatewayId, Long tenantId) {
        try {
            Map<String, Object> params = new HashMap<>();
            String body = JSONObject.toJSONString(params);
            
            CompletableFuture<String> future = mqttProxyEventBusUtil.sendAssignRequest(
                MqttTopicEnum.OT_CONTROL, 
                "request", 
                tenantId.toString(), 
                edgeGatewayId.toString(), 
                "updateLogView", 
                body
            );
            
            String response = future.get();
            return JSONObject.parseObject(response, Result.class);
        } catch (Exception e) {
            return Result.error("更新日志视图失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> downloadUpgradePackage(Long edgeGatewayId, Long tenantId, String url, 
                                              String upgradeVersion, String md5Proofread, String instance) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("url", url);
            params.put("upgradeVersion", upgradeVersion);
            params.put("md5Proofread", md5Proofread);
            params.put("instance", instance);
            String body = JSONObject.toJSONString(params);
            
            CompletableFuture<String> future = mqttProxyEventBusUtil.sendAssignRequest(
                MqttTopicEnum.OT_CONTROL, 
                "send", 
                tenantId.toString(), 
                edgeGatewayId.toString(), 
                "downloadUpgradePackage", 
                body
            );
            
            String response = future.get();
            return JSONObject.parseObject(response, Result.class);
        } catch (Exception e) {
            return Result.error("下载升级包失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> executeUpgrade(Long edgeGatewayId, Long tenantId, String upgradeVersion, String instance) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("upgradeVersion", upgradeVersion);
            params.put("instance", instance);
            String body = JSONObject.toJSONString(params);
            
            CompletableFuture<String> future = mqttProxyEventBusUtil.sendAssignRequest(
                MqttTopicEnum.OT_CONTROL, 
                "send", 
                tenantId.toString(), 
                edgeGatewayId.toString(), 
                "executeUpgrade", 
                body
            );
            
            String response = future.get();
            return JSONObject.parseObject(response, Result.class);
        } catch (Exception e) {
            return Result.error("执行升级失败: " + e.getMessage());
        }
    }

    @Override
    public Result<String> otSyncData(Long edgeGatewayId, Long tenantId, SyncEdgeGatewayDto syncEdgeGatewayDto) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("syncEdgeGatewayDto", syncEdgeGatewayDto);
            String body = JSONObject.toJSONString(params);
            
            CompletableFuture<String> future = mqttProxyEventBusUtil.sendAssignRequest(
                MqttTopicEnum.OT_CONTROL, 
                "send", 
                tenantId.toString(), 
                edgeGatewayId.toString(), 
                "otSyncData", 
                body
            );
            
            String response = future.get();
            return JSONObject.parseObject(response, Result.class);
        } catch (Exception e) {
            return Result.error("OT同步数据失败: " + e.getMessage());
        }
    }

    @Override
    public Result<SyncEdgeGatewayDto> pullConfig(Long edgeGatewayId, Long tenantId) {
        try {
            Map<String, Object> params = new HashMap<>();
            String body = JSONObject.toJSONString(params);
            
            CompletableFuture<String> future = mqttProxyEventBusUtil.sendAssignRequest(
                MqttTopicEnum.OT_CONTROL, 
                "request", 
                tenantId.toString(), 
                edgeGatewayId.toString(), 
                "pull", 
                body
            );
            
            String response = future.get();
            return JSONObject.parseObject(response, new TypeReference<Result<SyncEdgeGatewayDto>>() {});
        } catch (Exception e) {
            return Result.error("拉取配置失败: " + e.getMessage());
        }
    }

    @Override
    public Result<HardwareInfo> hardwareInfo(Long edgeGatewayId, Long tenantId) {
        try {
            Map<String, Object> params = new HashMap<>();
            String body = JSONObject.toJSONString(params);
            
            CompletableFuture<String> future = mqttProxyEventBusUtil.sendAssignRequest(
                MqttTopicEnum.OT_CONTROL, 
                "request", 
                tenantId.toString(), 
                edgeGatewayId.toString(), 
                "hardwareInfo", 
                body
            );
            
            String response = future.get();
            return JSONObject.parseObject(response, new TypeReference<Result<HardwareInfo>>() {});
        } catch (Exception e) {
            return Result.error("获取硬件信息失败: " + e.getMessage());
        }
    }
} 