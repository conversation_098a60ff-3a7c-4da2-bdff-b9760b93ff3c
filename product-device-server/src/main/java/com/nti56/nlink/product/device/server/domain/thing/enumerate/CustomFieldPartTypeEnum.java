package com.nti56.nlink.product.device.server.domain.thing.enumerate;

import lombok.Getter;

public enum CustomFieldPartTypeEnum {
    HEADER(1, "header", "头字段"),
    BODY(2, "body", "体字段"),
    TAIL(3, "tail", "尾字段")
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    CustomFieldPartTypeEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static CustomFieldPartTypeEnum typeOfValue(Integer value){
        CustomFieldPartTypeEnum[] values = CustomFieldPartTypeEnum.values();
        for (CustomFieldPartTypeEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static CustomFieldPartTypeEnum typeOfName(String name){
        CustomFieldPartTypeEnum[] values = CustomFieldPartTypeEnum.values();
        for (CustomFieldPartTypeEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static CustomFieldPartTypeEnum typeOfNameDesc(String nameDesc){
        CustomFieldPartTypeEnum[] values = CustomFieldPartTypeEnum.values();
        for (CustomFieldPartTypeEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }

}
