package com.nti56.nlink.product.device.server.domain.thing.enumerate;

import lombok.Getter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 类说明: 变动项目
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023-02-1:33:25
 * @since JDK 1.8
 */
public enum ChangeSubjectEnum {
    EDGE_GATEWAY(1, "edgeGateWay", "网关"),
    DEVICE(2, "device", "设备"),
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    ChangeSubjectEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static ChangeSubjectEnum typeOfValue(Integer value){
        ChangeSubjectEnum[] values = ChangeSubjectEnum.values();
        for (ChangeSubjectEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static ChangeSubjectEnum typeOfName(String name){
        ChangeSubjectEnum[] values = ChangeSubjectEnum.values();
        for (ChangeSubjectEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static ChangeSubjectEnum typeOfNameDesc(String nameDesc){
        ChangeSubjectEnum[] values = ChangeSubjectEnum.values();
        for (ChangeSubjectEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }


    public static List toList(){
        List<Map> result = new ArrayList<>();
        ChangeSubjectEnum[] values = ChangeSubjectEnum.values();
        Map<String,Object> map ;
        for (ChangeSubjectEnum v : values) {
            map = new HashMap<>();
            map.put("name",v.name);
            map.put("value",v.value);
            map.put("nameDesc",v.nameDesc);
            result.add(map);
        }
        return result;
    }
}
