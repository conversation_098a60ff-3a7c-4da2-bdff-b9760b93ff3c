package com.nti56.nlink.product.device.server.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.*;
import com.nti56.nlink.product.device.server.domain.thing.channel.Channel;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.EdgeGatewayTypeEnum;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.StatusEnum;
import com.nti56.nlink.product.device.server.entity.ChannelEntity;
import com.nti56.nlink.product.device.server.entity.EdgeGatewayEntity;
import com.nti56.nlink.product.device.server.entity.LabelGroupEntity;
import com.nti56.nlink.product.device.server.exception.BizException;
import com.nti56.nlink.product.device.server.model.ChannelLabelGroupsBo;
import com.nti56.nlink.product.device.server.model.EdgeGatewayDto;
import com.nti56.nlink.product.device.server.model.IdListDTO;
import com.nti56.nlink.product.device.server.model.LabelGroupSimpleBo;
import com.nti56.nlink.product.device.server.model.VersionQueryDTO;
import com.nti56.nlink.product.device.server.model.channel.vo.ChannelVO;
import com.nti56.nlink.product.device.server.model.edgegateway.EdgeGatewayExcel;
import com.nti56.nlink.product.device.server.model.edgegateway.Traffic5gInfo;
import com.nti56.nlink.product.device.server.model.edgegateway.dto.*;
import com.nti56.nlink.product.device.server.model.edgegateway.vo.EdgeGatewayCurrentTimeInfoVO;
import com.nti56.nlink.product.device.server.model.edgegateway.vo.EdgeGatewayVO;
import com.nti56.nlink.product.device.server.service.*;
import com.nti56.nlink.product.device.server.service.cache.IEdgeGatewayCacheService;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 类说明: 网关controller
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 13:32:04
 * @since JDK 1.8
 */
@Slf4j
@RestController
@RequestMapping("/")
@Tag( name = "网关模块")
public class EdgeGatewayController {

    @Autowired
    IEdgeGatewayService edgeGatewayService;

    @Autowired
    IEdgeGatewayCacheService edgeGatewayCacheService;

    @Autowired
    IChannelService channelService;

    @Autowired
    ILabelGroupService labelGroupService;
    
    @Autowired
    private INotAssignGatewayService notAssignGatewayService;

    @Autowired
    private IConnectorService connectorService;

    @GetMapping("edge-gateway/page")
    @Operation(summary = "获取网关分页" ,
            parameters  = {
                    @Parameter(name = "current",description = "要获取的页码",required = true),
                    @Parameter(name="size",description = "每页获取多少条",required = true)
            })
    public R pageEdgeGateway(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
            PageParam pageParam,EdgeGatewayDto edgeGatewayDto){
        Page<EdgeGatewayVO> page = pageParam.toPage(EdgeGatewayVO.class);
        Result<Page<EdgeGatewayVO>> result = edgeGatewayService.getEdgeGatewayPage(edgeGatewayDto,page,tenantIsolation);
        return R.result(result);
    }

    @GetMapping("edge-gateway/list")
    @Operation(summary = "获取全部网关列表" )
    public R listAllEdgeGateway(@RequestHeader("ot_headers") TenantIsolation tenantIsolation){
        Result<List<EdgeGatewayEntity>> result = edgeGatewayService.listEdgeGateway(null,tenantIsolation);
        return R.result(result);
    }

    @GetMapping("edge-gateway/channel/group/list/{edgeGatewayId}")
    @Operation(summary = "获取网关下所有通道及标签分组" )
    public R listChannelByEdgeGatewayId(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, @PathVariable Long edgeGatewayId){
        Result<List<Channel>> result = edgeGatewayService.listChannelByEdgeGatewayId(edgeGatewayId,tenantIsolation);
        return R.result(result);
    }

    @PostMapping("edge-gateway")
    @Operation(summary = "创建网关", description = "创建一个网关")
    public R createEdgeGateway(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@Validated @RequestBody CreateEdgeGatewayDTO dto){
        return R.result(edgeGatewayService.createEdgeGateway(dto,tenantIsolation));
    }

    @PutMapping("edge-gateway/{edgeGatewayId}")
    @Operation(summary = "更新网关", description = "更新网关")
    public R updateEdgeGateway(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@Validated @RequestBody EditEdgeGatewayDTO dto){
        return R.result( edgeGatewayService.updateEdgeGateway(dto,tenantIsolation));
    }

    @PutMapping("edge-gateway/{id}/sync")
    @Operation(summary = "根据id进行网关同步", description = "根据id进行网关同步")
    public R edgeGatewaySync(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, @PathVariable Long id){
        Result<Void> result = edgeGatewayService.edgeGatewaySyncById(id,tenantIsolation);
        return R.result(result);
    }

    @GetMapping("edge-gateway/{id}/pull")
    @Operation(summary = "根据id拉去边缘网关配置", description = "根据id拉去边缘网关配置")
    public R edgeGatewayPull(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, @PathVariable Long id){
        return R.result(edgeGatewayService.edgeGatewayPullById(id,tenantIsolation));
    }

    @PutMapping("edge-gateway/{edgeGatewayId}/sync-custom-driver")
    @Operation(summary = "同步网关自定义协议", description = "同步网关自定义协议")
    public R syncCustomDriver(@RequestHeader("ot_headers") TenantIsolation tenant, 
                    @PathVariable Long edgeGatewayId
    ){
        log.info("syncCustomDriver edgeGatewayId: {}, tenant: {}", edgeGatewayId, tenant);
        Result<Void> result = edgeGatewayService.syncCustomDriver(tenant, edgeGatewayId);
        return R.result(result);
    }

    
    @PutMapping("edge-gateway/batch/sync")
    @Operation(summary = "批量进行网关同步", description = "批量进行网关同步")
    @Deprecated
    @Hidden
    public R batchEdgeGatewaySync(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,  EdgeGatewayDto edgeGatewayDto){
        return R.result(edgeGatewayService.batchEdgeGatewaySync(edgeGatewayDto,tenantIsolation));
    }

    @DeleteMapping("edge-gateway/{edgeGatewayId}")
    @Operation(summary = "删除网关",
    parameters = {
            @Parameter(name = "edgeGatewayId",description = "网关对象",required = true)
    })
    public R deleteEdgeGateway(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,  @PathVariable String edgeGatewayId){
        Result<EdgeGatewayEntity> result = edgeGatewayService.deleteEdgeGatewayById(Long.valueOf(edgeGatewayId),tenantIsolation);
        return R.result(result);
    }

    @GetMapping("edge-gateway/{edgeGatewayId}")
    @Operation(summary = "获取网关", description = "通过ID获取一个网关",
    parameters = {
            @Parameter(name = "edgeGatewayId",description = "网关ID",required = true)
    })
    public R getEdgeGateway(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, @PathVariable String edgeGatewayId){
        if (!Optional.ofNullable(edgeGatewayId).isPresent()) {
            return R.error(ServiceCodeEnum.CODE_GET_FAIL);
        }
        Result<EdgeGatewayVO> result = edgeGatewayService.getEdgeGatewayById(Long.valueOf(edgeGatewayId),tenantIsolation);
        return R.result(result);
    }

    @GetMapping("edge-gateway/internal")
    @Operation(summary = "获取网关信息",
        parameters = {
            @Parameter(name = "edgeGatewayId", description = "边缘网关id",
                    required = true)
        })
    @Hidden
    public EdgeGatewayEntity getEdgeGatewayInternal(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, Long edgeGatewayId){
        Result<EdgeGatewayEntity> result = edgeGatewayService.getByIdAndTenantIsolation(edgeGatewayId,tenantIsolation);
        return result.getResult();
    }

    @GetMapping("edge-gateway/channel/list/{edgeGatewayId}")
    @Operation(summary = "根据网关获取通道" )
    public R listChannel(
            @RequestHeader("ot_headers") TenantIsolation tenantIsolation,
            @Parameter(description = "网关ID") @PathVariable Long edgeGatewayId){
        return R.result(channelService.listChannel(ChannelEntity.builder().edgeGatewayId(edgeGatewayId).build(),tenantIsolation));
    }

    @GetMapping("edge-gateway/label-group/list/{edgeGatewayId}")
    @Operation(summary = "根据网关获取标签分组" )
    public R listLabelGroupByEdgeGateway(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                                         @Parameter(description = "网关ID") @PathVariable Long edgeGatewayId){
        Result result = edgeGatewayService.listLabelGroupByEdgeGateway(edgeGatewayId,tenantIsolation);
        return R.result(result);
    }
    
    @PostMapping("edge-gateway/channel/list")
    @Operation(summary = "网关绑定通道" )
    public R listChannel(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                         @Parameter(description = "网关模型，带通道ID") @RequestBody EdgeGatewayDto edgeGatewayDto){
        if (Optional.ofNullable(edgeGatewayDto).isPresent() && Optional.ofNullable(edgeGatewayDto.getChannelIdList()).isPresent()) {
            EdgeGatewayEntity edgeGatewayEntity = edgeGatewayService.getByIdAndTenantIsolation(edgeGatewayDto.getId(), tenantIsolation).getResult();
            if (edgeGatewayEntity == null){
                throw new BizException("该租户下不存在此网关");
            }
            List<ChannelVO> oldBingChannel =  channelService.listChannel(ChannelEntity.builder().edgeGatewayId(edgeGatewayDto.getId()).build(),tenantIsolation).getResult();
            List<Long> oldChannelIds = BeanUtilsIntensifier.getIds(oldBingChannel, ChannelVO::getId);
            List<Long> channelIds = new ArrayList<>(oldChannelIds);
            oldChannelIds.removeAll(edgeGatewayDto.getChannelIdList());
            List<Long> newChannelIds = edgeGatewayDto.getChannelIdList();
            newChannelIds.removeAll(channelIds);
            Map<String,String> errorMsg = new HashMap<>();
            List<Long> unbindChannelId = new ArrayList<>();
            for (Long channelId:newChannelIds) {
                Result<ChannelEntity> result = channelService.getChannelById(channelId);
                if (result.getSignal() && Optional.ofNullable(result.getResult()).isPresent()) {
                    if (Optional.ofNullable(result.getResult().getEdgeGatewayId()).isPresent()) {
                        errorMsg.put(result.getResult().getName(),ServiceCodeEnum.CODE_EDGE_GATEWAY_CHANNEL_BING.getMessage());
                        unbindChannelId.add(channelId);
                    }
                }else {
                    errorMsg.put(channelId.toString(),ServiceCodeEnum.CODE_GET_CHANNEL_FAIL.getMessage());
                }
            }
            newChannelIds.removeAll(unbindChannelId);
            //解绑
            channelService.unbindEdgeGateway(oldChannelIds);
            //绑定
            channelService.bingEdgeGateway(newChannelIds,edgeGatewayDto.getId());
            if(CollectionUtils.isNotEmpty(oldChannelIds) || CollectionUtils.isNotEmpty(newChannelIds)){
                edgeGatewayService.setNotSyncById(edgeGatewayDto.getId());
            }
            return R.ok().put("errorMsg",errorMsg);
        }
        return R.error(ServiceCodeEnum.CODE_PARAM_ERROR);
    }

    @PostMapping("edge-gateway/online")
    @Operation(summary = "获取网关实时信息" )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200",description = "返回值",
                    content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = EdgeGatewayCurrentTimeInfoVO.class)
                    )})
    })
    public R listEdgeGatewayCurrentTimeInfo(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@RequestBody IdListDTO dto){
        return R.ok(edgeGatewayService.listEdgeGatewayCurrentTimeInfo(dto,tenantIsolation));
    }

    @GetMapping("edge-gateway/type")
    @Operation(summary = "网关类型" )
    public R getEdgeGatewayType(){
        return R.ok(EdgeGatewayTypeEnum.toList());
    }

    @GetMapping("edge-gateway/status")
    @Operation(summary = "网关状态" )
    public R getStatus(){
        return R.ok(StatusEnum.toList());
    }
    
    @GetMapping("edge-gateway/channel-label-group-tree")
    @Operation(summary = "获取网关下的通道、标签分组树" )
    public R getChannelLabelGroupTree(
        @RequestHeader("ot_headers") TenantIsolation tenant,
        @Parameter(description = "网关ID") Long edgeGatewayId
    ){
        Result<List<ChannelEntity>> channelListResult = channelService.listByEdgeGatewayId(edgeGatewayId,tenant.getTenantId(),false);
        if(!channelListResult.getSignal()){
            return R.error(channelListResult.getMessage());
        }
        List<ChannelEntity> channelList = channelListResult.getResult();
        List<Long> channelIds = channelList.stream().map(ChannelEntity::getId).collect(Collectors.toList());

        Result<List<LabelGroupEntity>> labelGroupListResult = labelGroupService.listLabelGroupByChannelIds(channelIds, tenant);
        if(!labelGroupListResult.getSignal()){
            return R.error(labelGroupListResult.getMessage());
        }

        List<LabelGroupEntity> labelGroupList = labelGroupListResult.getResult();
        //channelId -> labelGroupListGroup
        Map<Long, List<LabelGroupSimpleBo>> group = labelGroupList.stream().map(t -> {
            LabelGroupSimpleBo bo = new LabelGroupSimpleBo();
            bo.setId(t.getId());
            bo.setName(t.getName());
            bo.setChannelId(t.getChannelId());
            bo.setLabelGroupId(t.getId());
            bo.setLabelGroupName(t.getName());
            return bo;
        }).collect(Collectors.groupingBy(LabelGroupSimpleBo::getChannelId));
        
        List<ChannelLabelGroupsBo> tree = channelList.stream().map(t -> {
            ChannelLabelGroupsBo bo = new ChannelLabelGroupsBo();
            bo.setId(t.getId());
            bo.setName(t.getName());
            bo.setChannelId(t.getId());
            bo.setChannelName(t.getName());
            bo.setLabelGroupList(group.get(t.getId()));
            return bo;
        }).collect(Collectors.toList());

        return R.ok(tree);
    }

    @GetMapping("edge-gateway/heartbeat-uuid/{edgeGatewayId}")
    @Operation(summary = "获取网关心跳uuid" )
    public R getHeartbeatUuid(
        @RequestHeader("ot_headers") TenantIsolation tenant,
        @Parameter(description = "网关ID") @PathVariable Long edgeGatewayId
    ){
        String heartbeatUuid = edgeGatewayCacheService.getHeartbeatUuid(tenant.getTenantId(), edgeGatewayId);
        return R.ok().put("heartbeatUuid", heartbeatUuid);
    }

    @PutMapping("edge-gateway/start-gather/{edgeGatewayId}")
    @Operation(summary = "启用网关", description = "启用网关，即开始采集")
    public R startGather(
        @RequestHeader("ot_headers") TenantIsolation tenantIsolation,
        @Parameter(description = "网关ID") @PathVariable Long edgeGatewayId
    ){
        return R.result( edgeGatewayService.startGather(edgeGatewayId, tenantIsolation));
    }
    
    @PutMapping("edge-gateway/stop-gather/{edgeGatewayId}")
    @Operation(summary = "关闭网关", description = "关闭网关，即关闭采集")
    public R stopGather(
        @RequestHeader("ot_headers") TenantIsolation tenantIsolation,
        @Parameter(description = "网关ID") @PathVariable Long edgeGatewayId
    ){
        return R.result( edgeGatewayService.stopGather(edgeGatewayId, tenantIsolation));
    }

    @GetMapping("edge-gateway/delete/check/{id}")
    @Operation(summary = "网关删除检查" )
    public R edgeGatewayDeleteCheck(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@PathVariable Long id){
        return R.result(edgeGatewayService.edgeGatewayDeleteCheck(id,tenantIsolation));
    }

    @PutMapping("edge-gateway/{id}/runTimeInfo")
    @Operation(summary = "根据id进行网关runTimeInfo更新", description = "根据id进行网关runTimeInfo更新")
    public R runTimeInfo(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, @PathVariable Long id){
        Result<Void> result = edgeGatewayService.updateGatewayRunTimeInfo(id,tenantIsolation);
        return R.result(result);
    }
    
    @GetMapping("edge-gateway/versionList")
    @Operation(summary = "获取网关版本列表" )
    public R listEdgeGatewayVersion(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,PageParam pageParam,VersionQueryDTO versionQueryDTO){
        return R.ok(edgeGatewayService.listEdgeGatewayVersion(pageParam,versionQueryDTO,tenantIsolation.getTenantId()));
    }
    
    @GetMapping("edge-gateway/otaInfo/{edgeGatewayId}")
    @Operation(summary = "获取网关ota信息" )
    public R getOtaInfo(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@Parameter(description = "网关ID") @PathVariable Long edgeGatewayId){
        return R.ok(edgeGatewayService.getOtaInfo(edgeGatewayId,tenantIsolation));
    }

    @GetMapping(path = "edge-gateway/traffic5g/{edgeGatewayId}")
    @Operation(summary = "通过网关id查看5G流量",  parameters = {
            @Parameter(name = "edgeGatewayId", description = "网关ID")
    })
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "返回值",
                    content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = Traffic5gInfo.class)
                    )})
    })
    public R getTraffic5g(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, @Parameter(description = "网关ID") @PathVariable Long edgeGatewayId) {
        Result<Traffic5gInfo> result = edgeGatewayService.getTraffic5g(tenantIsolation, edgeGatewayId);
        return R.result(result);
    }
    
    @GetMapping("edge-gateway/listByTenantId")
    @Operation(summary = "根据租户ID获取全部网关列表" )
    public R listByTenantId(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,Long tenantId){
        Result<List<EdgeGatewayEntity>> result = edgeGatewayService.getAllEdgeGatewayByTenantId(tenantId);
        return R.result(result);
    }
    
    @PostMapping("edge-gateway/connect/{edgeGatewayId}")
    @Operation(summary = "连接网关" )
    public R connectEdgeGateway(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@PathVariable Long edgeGatewayId){
        return R.result(notAssignGatewayService.connectEdgeGateway(edgeGatewayId,tenantIsolation.getTenantId()));
    }
    
    @PostMapping("edge-gateway/disConnect/{edgeGatewayId}")
    @Operation(summary = "断开网关" )
    public R disConnectEdgeGateway(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@PathVariable Long edgeGatewayId){
        return R.result(notAssignGatewayService.disConnectEdgeGateway(edgeGatewayId,tenantIsolation.getTenantId()));
    }

    @PutMapping("edge-gateway/changeStatus/{edgeGatewayId}/{status}")
    @Operation(summary = "启用和停止直连网关", description = "启用和停止直连网关")
    public R changeStatus(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, @PathVariable Long edgeGatewayId, @PathVariable("status") Integer status) {
        return R.result( connectorService.changeStatusByEdgeGatewayId(edgeGatewayId,status,tenantIsolation));
    }

    @GetMapping("edge-gateway/hardwareInfo/{edgeGatewayId}")
    @Operation(summary = "获取边缘网关硬件信息", description = "启用和停止直连网关")
    public R hardwareInfo(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, @PathVariable Long edgeGatewayId) {
        return R.result(edgeGatewayService.hardwareInfo(tenantIsolation,edgeGatewayId));
    }

    @PutMapping("edge-gateway/monitor/{edgeGatewayId}/{type}")
    @Operation(summary = "更新网关监控信息", description = "更新网关监控信息")
    public R updateEdgeGatewayMonitor(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@Validated @RequestBody EditEdgeGatewayMonitorDTO dto,@PathVariable Long edgeGatewayId,@PathVariable Integer type){
        return R.result( edgeGatewayService.updateEdgeGatewayMonitor(dto,type,tenantIsolation));
    }

    @PostMapping("edge-gateway/batch/input/{exportType}")
    @Operation(summary = "导入网关", description = "exportType 0:不保留;1:保留两者")
    public R edgeGatewayBatchInput(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@PathVariable Integer exportType,@RequestBody List<EdgeGatewayExcel> list) {
        Result<List<ExcelMessageDTO>> result = edgeGatewayService.edgeGatewayBatchInput(tenantIsolation,exportType,list);
        return R.result(result);
    }

    @GetMapping("edge-gateway/notConnectList")
    @Operation(summary = "获取非直连网关列表" )
    public R notConnectList(@RequestHeader("ot_headers") TenantIsolation tenantIsolation){
        Result<List<EdgeGatewayEntity>> result = edgeGatewayService.notConnectList(tenantIsolation.getTenantId());
        return R.result(result);
    }

    @PostMapping({"edge-gateway/export"})
    @Operation(summary = "导出网关")
    public void exportEdgeGateways(HttpServletResponse response, @RequestHeader("ot_headers") TenantIsolation tenantIsolation,@RequestBody Set<Long> edgeGatewayIds) throws IOException {
        edgeGatewayService.exportEdgeGateway(response,tenantIsolation.getTenantId(),edgeGatewayIds);
    }
    
}
