package com.nti56.nlink.product.device.server.controller;

import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.rule.feign.IRuleTasksService;
import com.nti56.nlink.common.rule.model.RuleParam;
import com.nti56.nlink.common.util.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 类说明: 规则任务controller
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-04-18 13:36:42
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/")
@Tag(name = "规则任务模块")
public class RuleTaskController {
    
    @Autowired
    IRuleTasksService ruleTaskService;
    
    @PostMapping("rule-task")
    @Operation(summary = "更新规则任务" )
    public Result<Void> upsertRuleTask(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                                       @RequestBody RuleParam ruleParam){
        return ruleTaskService.upsertRuleTask(ruleParam,tenantIsolation);
    }

    @PutMapping("rule-task/stop/{instanceId}")
    @Operation(summary = "停止规则任务" )
    public Result<Void> stopRuleTask(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                                        @PathVariable("instanceId") Long instanceId){
        return ruleTaskService.stopRuleTask(instanceId, tenantIsolation);
    }

    @PutMapping("rule-task/sync/{instanceId}")
    @Operation(summary = "同步规则任务" )
    public Result<Void> syncRuleTask(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                                          @PathVariable("instanceId") Long instanceId){
        return ruleTaskService.syncRuleTask(instanceId,tenantIsolation);
    }
    
    @PutMapping("rule-task/enable/{instanceId}")
    @Operation(summary = "启用规则任务" )
    public Result<Void> enableRuleTask(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                                          @PathVariable("instanceId") Long instanceId){
        return ruleTaskService.enableRuleTask(instanceId,tenantIsolation);
    }

    @DeleteMapping("rule-task/{instanceId}")
    @Operation(summary = "停止规则任务" )
    public Result<Void> deleteRuleTask(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                                        @PathVariable("instanceId") Long instanceId){
        return ruleTaskService.deleteRuleTask(instanceId, tenantIsolation);
    }

}
