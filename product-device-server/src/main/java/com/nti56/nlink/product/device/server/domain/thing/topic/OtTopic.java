package com.nti56.nlink.product.device.server.domain.thing.topic;

import com.nti56.nlink.product.device.server.domain.thing.enumerate.MqttTopicEnum;

import lombok.Builder;
import lombok.Data;

/**
 * 类说明: ot下发spi调用topic
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-05-26 09:02:08
 * @since JDK 1.8
 */
public class OtTopic {
    
    @Data
    @Builder
    public static class TopicInfo{
        private Long requestId;
    }

    public static TopicInfo parseTopic(String topic){
        String[] split = topic.split("/");
        long requestId = new Long(split[6]);
        
        return TopicInfo.builder()
            .requestId(requestId)
            .build();
    }
    
    public static String createNotAssignTopic(
        MqttTopicEnum type,
        String imeiOrHost,
        String adminPort, 
        String api, 
        Long requestId, 
        String balanceId
    ){
        return type.getPrefix() 
            + imeiOrHost + "/"
            + adminPort + "/" 
            + api + "/"
            + "?requestId=" + requestId 
            + "&balanceId=" + balanceId;
    }

    public static String createAssignTopic(
        MqttTopicEnum type,
        String tenantId, 
        String edgeGatewayId, 
        String api, 
        Long requestId, 
        String balanceId
    ){
        return type.getPrefix() 
            + tenantId + "/"
            + edgeGatewayId + "/" 
            + api + "/"
            + "?requestId=" + requestId 
            + "&balanceId=" + balanceId;
    }

    public static String createAssignTopic(
        MqttTopicEnum type,
        Long tenantId, 
        String edgeGatewayId, 
        String api, 
        Long requestId, 
        String balanceId
    ){
        return createAssignTopic(
            type, 
            tenantId.toString(),
            edgeGatewayId, 
            api, 
            requestId, 
            balanceId
        );
    }

    /**
     * 创建网关响应topic
     */
    public static String createResponseSubscribeTopic(String group, String balanceId){
        return "$share/" + group + "/" 
                + MqttTopicEnum.GW_RESPONSE.getPrefix() 
                + balanceId + "/#";
    }

}
