package com.nti56.nlink.product.device.server.domain.event;

import com.nti56.nlink.product.device.client.model.dto.engineering.ProductDeviceServerDataDTO;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * 类说明：
 *
 * @ClassName ImportDataEvent
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/11/30 15:18
 * @Version 1.0
 */
@Getter
public class ImportDataEvent extends ApplicationEvent {

    private ProductDeviceServerDataDTO data;

    /**
     * Create a new {@code ApplicationEvent}.
     *
     * @param source the object on which the event initially occurred or with
     *               which the event is associated (never {@code null})
     */
    public ImportDataEvent(Object source, ProductDeviceServerDataDTO data) {
        super(source);
        this.data = data;
    }


}
