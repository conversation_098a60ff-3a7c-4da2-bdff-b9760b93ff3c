package com.nti56.nlink.product.device.server.controller;

import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.PageParam;
import com.nti56.nlink.common.util.R;
import com.nti56.nlink.product.device.server.service.IMessageAggregationService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("aggregation")
@Slf4j
public class MessageAggregationController {

    @Autowired
    private IMessageAggregationService messageAggregationService;

    @GetMapping("/{id}")
    @Operation(summary = "分页查询模板消息")
    public R pageTemplate(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, PageParam pageParam, @PathVariable("id") Long id) {
        return R.result(messageAggregationService.pageMessage(pageParam, tenantIsolation, id));
    }
}
