package com.nti56.nlink.product.device.server.domain.redirect;

import cn.hutool.json.JSONUtil;
import com.nti56.nlink.common.fetcher.CommonFetcher;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.nlink.product.device.server.entity.InstanceRedirectEntity;
import com.nti56.nlink.product.device.server.enums.RedirectTypeEnum;
import com.nti56.nlink.product.device.server.handler.RedirectHandler;
import com.nti56.nlink.product.device.server.service.impl.strategy.RedirectHandlerImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * 类说明：
 *
 * @ClassName InstanceRedirect
 * @Description 实例回调领域模型
 * <AUTHOR>
 * @Date 2022/6/27 9:48
 * @Version 1.0
 */

@Slf4j
public class InstanceRedirect {

    private final InstanceRedirectEntity instanceRedirectEntity;

    private Object payload;

    public Object getPayload() {
        return payload;
    }

    public void setPayload(Object payload) {
        this.payload = payload;
    }

    public InstanceRedirect(InstanceRedirectEntity instanceRedirectEntity) {
        this.instanceRedirectEntity = instanceRedirectEntity;
    }


    public Result<Boolean> invoke(){

        if(Objects.isNull(instanceRedirectEntity)){
            log.error("invoke redirect error ,can't find redirect record!");
            return Result.error("找不到回调信息");
        }
        RedirectTypeEnum typeEnum = RedirectTypeEnum.typeOfCode((byte) instanceRedirectEntity.getRedirectType().intValue());
        RedirectFn redirectFn;
        switch (typeEnum) {
            case WEBHOOK:
                redirectFn = WebhookRedirectFn.getFunctionInstance(instanceRedirectEntity, payload);
                break;
            case MQTT_SINK:
                redirectFn = MQTTRedirectFn.getFunctionInstance(instanceRedirectEntity, payload);
                break;
            default:
                log.warn("un support redirect type:{}",typeEnum.getTypeName());
                return Result.error("暂不支持的回调类型："+ typeEnum.getTypeName());
        }
        if(Objects.isNull(redirectFn)){
            return Result.error("找不到回调信息");
        }
        RedirectHandler redirectHandler = new RedirectHandlerImpl(redirectFn,typeEnum.getTypeName());
        Result<Boolean> booleanResult = redirectHandler.invokeRedirect(instanceRedirectEntity.getId());
        return booleanResult;
    }

    public static Result<InstanceRedirect> checkBeforeSave(InstanceRedirectEntity instanceRedirectEntity, CommonFetcher commonFetcher) {

        String redirectName = instanceRedirectEntity.getRedirectName();
        if (StringUtils.isBlank(redirectName)) {
            return Result.error("回调名称不能为空！");
        }
        Long tenantId = instanceRedirectEntity.getTenantId();
        Long spaceId = instanceRedirectEntity.getSpaceId();
        //判断在该租户，该空间下的回调名称是否唯一
        // TODO: 2022/6/27
        //检查连接超时时间、请求超时时间
        Integer redirectRequestTimeout = instanceRedirectEntity.getRedirectRequestTimeout();
        if (Objects.isNull(redirectRequestTimeout)) {
            return Result.error("请求超时时间不能为空！");
        }
        Integer redirectRequestConnectTimeout = instanceRedirectEntity.getRedirectRequestConnectTimeout();
        if (Objects.isNull(redirectRequestConnectTimeout)) {
            return Result.error("请求连接超时时间不能为空！");
        }
        //判断回调方法体是否存在
        boolean checkRedirectFn = checkByRedirectType(instanceRedirectEntity.getRedirectFn(), instanceRedirectEntity.getRedirectType());
        if (!checkRedirectFn) {
            return Result.error("回调方法为空或必要参数缺失！");
        }
        return Result.ok();

    }

    private static boolean checkByRedirectType(String redirectFn, Integer redirectType) {

        if (StringUtils.isBlank(redirectFn)) {
            return false;
        }
        RedirectTypeEnum typeEnum = RedirectTypeEnum.typeOfCode(((byte) redirectType.intValue()));
        boolean checkResult = false;
        switch (typeEnum) {
            case WEBHOOK:
                checkResult = checkWebhook(redirectFn);
                break;
            case MQTT_SINK:
                checkResult = checkMqttSink(redirectFn);
                break;
            default:
                checkResult = checkDefault(redirectFn);
        }

        return checkResult;
    }

    private static boolean checkMqttSink(String redirectFn) {
        try {
            MQTTRedirectFn mqttRedirectFn = JSONUtil.toBean(redirectFn, MQTTRedirectFn.class);
            if (StringUtils.isBlank(mqttRedirectFn.getIp())) {
                return false;
            }
            if (Objects.isNull(mqttRedirectFn.getPort())) {
                return false;
            }
            return true;
        } catch (Exception e) {
            log.error("webhook redirect convert to object error");
            return false;
        }

    }

    private static boolean checkDefault(String redirectFn) {
        log.warn("check before save redirect error,[unknown redirect type], check return false");
        return false;
    }

    private static boolean checkWebhook(String redirectFn) {
        try {
            WebhookRedirectFn webhookRedirectFn = JSONUtil.toBean(redirectFn, WebhookRedirectFn.class);
            if (StringUtils.isBlank(webhookRedirectFn.getMethod())) {
                return false;
            }
            if (StringUtils.isBlank(webhookRedirectFn.getUrl())) {
                return false;
            }
            return true;
        } catch (Exception e) {
            log.error("webhook redirect convert to object error");
            return false;
        }
    }

    public static Result<InstanceRedirect> checkBeforeUpdate(InstanceRedirectEntity instanceRedirectEntity, CommonFetcher commonFetcher) {

        //校验id
        if (Objects.isNull(instanceRedirectEntity.getId())) {
            return Result.error(ServiceCodeEnum.CODE_PARAM_ERROR, "参数异常，ID为空");
        }
        InstanceRedirectEntity existRedirect = commonFetcher.get(instanceRedirectEntity.getId(), InstanceRedirectEntity.class);
        if (Objects.isNull(existRedirect)) {
            return Result.error(ServiceCodeEnum.CODE_GET_FAIL, "租户下不存在该回调配置");
        }
        return checkBeforeSave(instanceRedirectEntity, commonFetcher);
    }

    public static Result<InstanceRedirect> checkBeforeDelete(Long redirectId, CommonFetcher commonFetcher) {

        if (Objects.isNull(redirectId)) {
            return Result.error(ServiceCodeEnum.CODE_PARAM_ERROR, "参数异常，ID为空");
        }
        InstanceRedirectEntity existRedirect = commonFetcher.get(redirectId, InstanceRedirectEntity.class);
        if (Objects.isNull(existRedirect)) {
            return Result.error(ServiceCodeEnum.CODE_GET_FAIL, "租户下不存在该回调配置");
        }
        //校验回调是否被规则引擎使用
        // TODO: 2022/6/28 现阶段commonFetch 还不够灵活，为了不使domain直接依赖mapper,这块逻辑先放到业务层
        return Result.ok();
    }

    public static Result<InstanceRedirectEntity> getRedirect(Long redirectId, CommonFetcher commonFetcher){
        if (Objects.isNull(redirectId)) {
            return Result.error(ServiceCodeEnum.CODE_PARAM_ERROR, "参数异常，ID为空");
        }
        InstanceRedirectEntity existRedirect = commonFetcher.get(redirectId, InstanceRedirectEntity.class);
        return Result.ok(existRedirect);
    }

}
