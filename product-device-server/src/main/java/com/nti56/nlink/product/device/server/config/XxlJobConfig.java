package com.nti56.nlink.product.device.server.config;


import com.xxl.job.core.executor.impl.XxlJobSpringExecutor;
import com.xxl.job.core.util.IpUtil;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.net.InetAddress;

/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName XxlJobConfig
 * @date 2022/8/15 9:51
 * @Version 1.0
 */
@Slf4j
@Configuration
@Getter
@ConditionalOnProperty(name = "job.enabled", havingValue = "true")
public class XxlJobConfig {

    @Value("${job.server.admin.addresses}")
    private String adminAddresses;

    @Value("${job.server.accessToken}")
    private String accessToken;

    @Value("${job.server.executor.appName}")
    private String appName;

//    @Value("${job.server.executor.port}")
//    private int port;

    @Value("${job.server.userName}")
    private String userName;

    @Value("${job.server.password}")
    private String password;

    @Value("${job.server.jobGroup}")
    private String jobGroup;

    @Value("${job.executor.logretentiondays:3}")
    private int logRetentionDays;

    @Bean
    public XxlJobSpringExecutor xxlJobExecutor() {
        log.debug(">>>>>>>>>>> job-server config init >>>>>>>>>>> ");
        XxlJobSpringExecutor xxlJobSpringExecutor = new XxlJobSpringExecutor();
        xxlJobSpringExecutor.setAdminAddresses(adminAddresses);
        InetAddress inetAddress= IpUtil.getLocalAddress();
        if(inetAddress!=null){
            xxlJobSpringExecutor.setIp(inetAddress.getHostAddress());
        }
//        xxlJobSpringExecutor.setPort(port);
        xxlJobSpringExecutor.setAppname(appName);
        xxlJobSpringExecutor.setAccessToken(accessToken);
        xxlJobSpringExecutor.setLogRetentionDays(logRetentionDays);
        return xxlJobSpringExecutor;
    }

}
