package com.nti56.nlink.product.device.server.config;

import com.nti56.nlink.product.device.server.proxy.*;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import lombok.extern.slf4j.Slf4j;

/**
 * 类说明: EventBus代理配置
 * 
 * 替代原来的Feign HTTP客户端，使用EventBus进行通信
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-05-26 09:25:26
 * @since JDK 1.8
 */
@Configuration
@Slf4j
public class EventBusProxyConfig {
    
    @Bean
    public EdgeGatewayControlProxy edgeGatewayControlProxy(){
        EdgeGatewayControlProxy proxy = new EdgeGatewayControlProxy();
        log.info("EventBusProxyConfig edgeGatewayControlProxy created: {}", proxy);
        return proxy;
    }
    
    @Bean
    public EdgeGatewaySpiProxy edgeGatewaySpiProxy(){
        EdgeGatewaySpiProxy proxy = new EdgeGatewaySpiProxy();
        log.info("EventBusProxyConfig edgeGatewaySpiProxy created: {}", proxy);
        return proxy;
    }
    
    @Bean
    public EdgeGatewayCacheProxy edgeGatewayCacheProxy(){
        EdgeGatewayCacheProxy proxy = new EdgeGatewayCacheProxy();
        log.info("EventBusProxyConfig edgeGatewayCacheProxy created: {}", proxy);
        return proxy;
    }

    @Bean
    public EdgeGatewayCustomProxy edgeGatewayCustomProxy(){
        EdgeGatewayCustomProxy proxy = new EdgeGatewayCustomProxy();
        log.info("EventBusProxyConfig edgeGatewayCustomProxy created: {}", proxy);
        return proxy;
    }

    @Bean
    public EdgeGatewayNotAssignControlProxy edgeGatewayNotAssignControlProxy(){
        EdgeGatewayNotAssignControlProxy proxy = new EdgeGatewayNotAssignControlProxy();
        log.info("EventBusProxyConfig edgeGatewayNotAssignControlProxy created: {}", proxy);
        return proxy;
    }
}
