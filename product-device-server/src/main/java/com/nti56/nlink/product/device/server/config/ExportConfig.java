package com.nti56.nlink.product.device.server.config;

import lombok.Data;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @date 2022/5/18 17:00<br/>
 * @version 1.0
 * @since JDK 1.8
 */
@ConfigurationProperties(prefix = "nlink.pd.export")
@Configuration
@Data
@RefreshScope
public class ExportConfig {
  /**
   * sql目录
   */
  private String sqlExport;

  /**
   * 边缘网关导出配置文件
   */
  private String edgeGatewayConfigPath = "/data/export/edge-gateway/config";

  /**
   * 导出租户配置信息
   */
  private String tenantConfigPath = "/data/export/tenant/config";

  /**
   * 边缘网关配置文件名
   */
  private String edgeGatewayConfigName = "config.json";
  /**
   * export Name
   */
  private String name = "export.sql";

  private String sql = "select 1 from DATABASECHANGELOG;";

  /**
   *  产品设备jar地址
   */
  private String jarPath;

  /**
   * 产品设备bootstrap.yml
   */
  private String bootstrapPath;

  /**
   * 产品设备application.yml
   */
  private String applicationPath;

  private String shell;

  private String bat;

  /**
   * 运行时是否发布 默认 true 运行态 false 设计态
   */
  private boolean runtimePublish = true;

}
