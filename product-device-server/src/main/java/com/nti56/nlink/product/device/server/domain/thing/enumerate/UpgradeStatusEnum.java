package com.nti56.nlink.product.device.server.domain.thing.enumerate;

import lombok.Getter;

/**
 * 类说明: 下载状态
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023-3-22 14:36:36
 * @since JDK 1.8
 */
public enum UpgradeStatusEnum {
    UPGRADE_STATUS_INIT(0, "upgradeStatusInit", "upgradeStatusInit"),//初始化状态
    DOWNLOAD_STATUS(1, "downloadStatus", "downloadStatus"),//下载安装包状态
    DO_DOWNLOAD_STATUS(2, "doDownloadStatus", "doDownloadStatus"),//下载中状态
    UPGRADE_STATUS(3, "upgradeStatus", "upgradeStatus"),//升级状态状态
    DO_UPGRADE_STATUS(4, "doUpgradeStatus", "doUpgradeStatus")//升级中状态
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    UpgradeStatusEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static UpgradeStatusEnum typeOfValue(Integer value){
        UpgradeStatusEnum[] values = UpgradeStatusEnum.values();
        for (UpgradeStatusEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static String getNameByValue(Integer value){
        UpgradeStatusEnum[] values = UpgradeStatusEnum.values();
        for (UpgradeStatusEnum v : values) {
            if (v.value.equals(value)) {
                return v.name;
            }
        }
        return null;
    }

    public static UpgradeStatusEnum typeOfName(String name){
        UpgradeStatusEnum[] values = UpgradeStatusEnum.values();
        for (UpgradeStatusEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static UpgradeStatusEnum typeOfNameDesc(String nameDesc){
        UpgradeStatusEnum[] values = UpgradeStatusEnum.values();
        for (UpgradeStatusEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }

}
