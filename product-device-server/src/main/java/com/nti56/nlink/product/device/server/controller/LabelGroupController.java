package com.nti56.nlink.product.device.server.controller;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.util.MapUtils;
import com.alibaba.fastjson.JSON;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.R;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.nlink.product.device.server.model.LabelGroupDto;
import com.nti56.nlink.product.device.server.model.label.CopyLabelGroupReq;
import com.nti56.nlink.product.device.server.model.label.dto.LabelDTO;
import com.nti56.nlink.product.device.server.model.label.dto.LabelGroupDTO;
import com.nti56.nlink.product.device.server.model.label.dto.MoveOrCopyLabelGroupDTO;
import com.nti56.nlink.product.device.server.service.ILabelGroupService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;

/**
 * 类说明: 标签分组controller
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 13:32:04
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/label-group")
@Tag(name = "标签分组模块")
public class LabelGroupController {
    
    @Autowired
    private ILabelGroupService labelGroupService;

    @PostMapping({"add/{channelId}"})
    @Operation(summary = "创建标签分组")
    public R creatLabelGroup(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                             @Parameter(description = "标签分组名字")@RequestBody LabelGroupDTO labelGroupDTO,
                             @Parameter(description = "通道Id")@PathVariable Long channelId
    ){
        return R.result(labelGroupService.creatLabelGroup(channelId, labelGroupDTO.getNewLevelName(), tenantIsolation, labelGroupDTO));
    }

    @PutMapping({"{channelId}/unbind"})
    @Operation(summary = "按标签分组解绑标签")
    public R unbindByLabelGroup(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                             @Parameter(description = "标签分组名字")@RequestBody LabelGroupDTO labelGroupDTO,
                             @Parameter(description = "通道Id")@PathVariable Long channelId
    ){
        return R.result(labelGroupService.unbindByLabelGroup(channelId, tenantIsolation, labelGroupDTO));
    }

    @DeleteMapping({"{channelId}"})
    @Operation(summary = "删除标签分组及其下属分组及其标签")
    public R delete(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                                @Parameter(description = "标签分组名字")@RequestBody LabelGroupDTO labelGroupDTO,
                                @Parameter(description = "通道Id")@PathVariable Long channelId
    ){
        return R.result(labelGroupService.delete(channelId, tenantIsolation, labelGroupDTO));
    }

    @PostMapping({"update/{channelId}"})
    @Operation(summary = "更新标签分组")
    public R updateLabelGroup(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                             @Parameter(description = "标签分组")@RequestBody LabelGroupDTO labelGroupDTO,
                             @Parameter(description = "通道Id")@PathVariable Long channelId
    ){
        return R.result(labelGroupService.updateLabelGroup(labelGroupDTO.getNewLevelName(), channelId, tenantIsolation, labelGroupDTO));
    }

    @GetMapping({"export/{channelId}/{name}","export/{channelId}"})
    @Operation(summary = "导出标签")
    public void exportLabels(HttpServletResponse response, @RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                             @Parameter(description = "分组名称")@PathVariable(value = "name",required = false) String name,
                             @Parameter(description = "通道Id")@PathVariable("channelId") Long channelId
    ) throws IOException {
        List<LabelDTO> list = labelGroupService.exportLabels(channelId, tenantIsolation, name);
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = StringUtils.isEmpty(name) ? channelId.toString() : name;
            fileName = URLEncoder.encode(fileName + "_labels", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), LabelDTO.class).sheet("labels")
                    .doWrite(list);
        } catch (Exception e) {
            // 重置response
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            Map<String, String> map = MapUtils.newHashMap();
            map.put("status", "failure");
            map.put("message", "下载文件失败" + e.getMessage());
            response.getWriter().println(JSON.toJSONString(map));
        }
    }

    @GetMapping({"{channelId}","{channelId}/{searchName}"})
    @Operation(summary = "标签分组及其下属分组")
    public R getLabelGroupTree(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                    @Parameter(description = "通道Id")@PathVariable Long channelId,
                    @Parameter(description = "通道Id")@PathVariable(required = false) String searchName
    ){
        return R.result(labelGroupService.getLabelGroupTree(channelId, tenantIsolation, searchName));
    }


    @GetMapping({"label/tree/{channelId}"})
    @Deprecated
    @Operation(summary = "获取标签树" )
    public R labelAndGroupTree(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                               @Parameter(description = "标签分组名字")String name, @Parameter(description = "通道Id")@PathVariable Long channelId){
        Result<List<LabelGroupDto>> result = labelGroupService.labelAndGroupTree(name,channelId,tenantIsolation,null);
        return R.result(result);
    }

    @GetMapping({"label/tree/{channelId}/all"})
    @Operation(summary = "获取通道下标签树" )
    @Deprecated
    public R labelAndGroupTreeAll(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                               @Parameter(description = "标签分组名字")String name, @Parameter(description = "通道Id")@PathVariable Long channelId){
        Result<List<LabelGroupDto>> result = labelGroupService.labelAndGroupTree(name,channelId,tenantIsolation,true);
        return R.result(result);
    }


    @PutMapping(value="moveOrCopy")
    @Operation(summary = "移动分组")
    public R moveOrCopyLabel(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                             @RequestBody @Validated MoveOrCopyLabelGroupDTO dto) {
        return R.result(labelGroupService.moveOrCopyLabelGroup(dto,tenantIsolation));
    }

    @PutMapping(value="copy")
    @Operation(summary = "复制分组")
    public R copyLabel(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                             @RequestBody @Validated CopyLabelGroupReq req) {
        if (ObjectUtil.isEmpty(req) || ObjectUtil.isEmpty(req.getSourceLabelGroupId()) || (ObjectUtil.isEmpty(req.getTargetLabelGroupIds()) && ObjectUtil.isEmpty(req.getTargetChannelIds()))) {
            return R.result(ServiceCodeEnum.CODE_PARAM_ERROR);
        }
        return R.result(labelGroupService.copyLabelGroup(req,tenantIsolation));
    }
    
}
