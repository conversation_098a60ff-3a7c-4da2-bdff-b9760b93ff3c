package com.nti56.nlink.product.device.server.domain.thing.enumerate;

import lombok.Getter;

public enum Snap7SpecEnum {
    S200Smart(0, "S200Smart", "S200Smart"),
    S200(1, "S200", "S200"),
    S300(2, "S300", "S300"),
    S400(3, "S400", "S400"),
    S1200(4, "S1200", "S1200"),
    S1500(5, "S1500", "S1500")
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    Snap7SpecEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static Snap7SpecEnum typeOfValue(Integer value){
        Snap7SpecEnum[] values = Snap7SpecEnum.values();
        for (Snap7SpecEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static Snap7SpecEnum typeOfName(String name){
        Snap7SpecEnum[] values = Snap7SpecEnum.values();
        for (Snap7SpecEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static Snap7SpecEnum typeOfNameDesc(String nameDesc){
        Snap7SpecEnum[] values = Snap7SpecEnum.values();
        for (Snap7SpecEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
