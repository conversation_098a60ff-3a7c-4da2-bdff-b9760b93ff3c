package com.nti56.nlink.product.device.server.domain.rule;

import com.nti56.nlink.common.fetcher.CommonFetcher;
import com.nti56.nlink.common.fetcher.Preloader;
import com.nti56.nlink.common.rule.model.RuleParam;
import com.nti56.nlink.common.rule.model.RulePropertyItem;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.client.model.dto.json.ComputeContentField;
import com.nti56.nlink.product.device.client.model.dto.json.compute.ComputeChangeItem;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.ComputeTypeEnum;
import com.nti56.nlink.product.device.server.domain.thing.label.Label;
import com.nti56.nlink.product.device.server.entity.ComputeTaskEntity;
import com.nti56.nlink.product.device.server.entity.LabelBindRelationEntity;
import com.nti56.nlink.product.device.server.entity.LabelEntity;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;

/**
 * 类说明: 规则领域对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-07-21 16:05:34
 * @since JDK 1.8
 */
public class Rule {

    public static final String ruleTaskPrefix = "rule_";
    
    private Long instanceId;

    private String topic;

    private List<RulePropertyItem> rawItemList;

    private List<Label> labelList;

    public static Result<Rule> checkInfo(
        RuleParam ruleParam, 
        CommonFetcher commonFetcher
    ){
        if(ruleParam == null){
            return Result.error("规则参数不能为空"); 
        }

        if(ruleParam.getInstanceId() == null){
            return Result.error("规则实例id不能为空"); 
        }
        
        Rule rule = new Rule();
        rule.instanceId = ruleParam.getInstanceId();
        
        if(ruleParam.getTopic() == null){
            return Result.error("规则实例topic不能为空"); 
        }
        
        rule.topic = ruleParam.getTopic();

        List<RulePropertyItem> rawItemList = ruleParam.getProperties();
        if(rawItemList == null || rawItemList.size() <= 0){
            return Result.error("规则属性不能为空");
        }

        rule.rawItemList = rawItemList;

        //预加载规则涉及的属性的相关信息
        Map<Long, Preloader<LabelBindRelationEntity>> preloaderMap = new HashMap<>();
        rawItemList.stream()
            .collect(Collectors.groupingBy(RulePropertyItem::getDeviceId))
            .entrySet()
            .forEach(entry -> {
                Long deviceId = entry.getKey();
                List<RulePropertyItem> items = entry.getValue();
                List<String> names = items.stream()
                    .map(RulePropertyItem::getPropertyName)
                    .collect(Collectors.toList());
                Preloader<LabelBindRelationEntity> preloader = commonFetcher
                    .preloader( "device_id", deviceId, LabelBindRelationEntity.class)
                    .preload("property_name", names);
                preloaderMap.put(deviceId, preloader);
            });
        
        List<Label> labelList = new ArrayList<>();
        for(RulePropertyItem item:rawItemList){
            //获取属性标签绑定关系
            Long deviceId = item.getDeviceId();
            String propertyName = item.getPropertyName();
            Preloader<LabelBindRelationEntity> preloader = preloaderMap.get(deviceId);
            LabelBindRelationEntity relation = preloader.getFirst("property_name", propertyName);
            if(relation == null){ 
                return Result.error("找不到标签绑定关系，deviceId:" + deviceId + ", propertyName:" + propertyName);
            }
            //获取标签
            Long labelId = relation.getLabelId();
            LabelEntity labelEntity = commonFetcher.get(labelId, LabelEntity.class);
            Result<Label> labelResult = Label.checkInfoToEdgeGateway(labelEntity, commonFetcher);
            if(!labelResult.getSignal()){
                return Result.error(labelResult.getMessage());
            }
            Label label = labelResult.getResult();
            labelList.add(label);
        }

        rule.labelList = labelList;
        return Result.ok(rule);
    } 

    public List<ComputeTaskEntity> generateRuleComputeTask(){
        
        //按照网关+通道分组，每个分组生成一个规则计算任务，
        //每个计算任务包含n个computeItem，每个computeItem类型是标签改变上报
        List<ComputeTaskEntity> taskList = new ArrayList<>();
        Map<Long, List<Label>> gwGroup = labelList.stream()
            .collect(Collectors.groupingBy(Label::getEdgeGatewayId));
        for(Entry<Long, List<Label>> entry:gwGroup.entrySet()){

            Long edgeGatewayId = entry.getKey();
            List<Label> list = entry.getValue();
            Map<Long, List<Label>> channelGroup = list.stream()
                .collect(Collectors.groupingBy(Label::getChannelId));
            for(Entry<Long, List<Label>> channelEntry:channelGroup.entrySet()){
                Long channelId = channelEntry.getKey();
                List<Label> labelList = channelEntry.getValue();
                ComputeTaskEntity task = generateRuleTaskEntity(
                    instanceId,
                    topic, 
                    edgeGatewayId, 
                    channelId,
                    labelList
                );
                taskList.add(task);
            
            }
            
        }
        return taskList;
    }
    
    private ComputeTaskEntity generateRuleTaskEntity(
        Long ruleInstanceId,
        String topic, 
        Long edgeGatewayId,
        Long channelId,
        List<Label> labelList
    ){
        String computeType = ComputeTypeEnum.RULE.getName();
        List<ComputeChangeItem> changeList = new ArrayList<>();
        for(Label label:labelList){
            ComputeChangeItem item = ComputeChangeItem.builder()
                .channelId(label.getChannelId())
                .labelId(label.getId())
                .labelName(label.getName())
                .isArray(label.getIsArray())
                .dataType(label.getDataType().getName())
                .length(label.getLength())
                .stringBytes(label.getStringBytes())
                .build();
            changeList.add(item);
        }
        
        ComputeContentField content = ComputeContentField.builder()
            .computeType(computeType)
            .id(ruleInstanceId)
            .topic(topic)
            .changeList(changeList)
            .build();

        ComputeTaskEntity computeTask = ComputeTaskEntity.builder()
            .name(ruleTaskPrefix + ruleInstanceId)
            .edgeGatewayId(edgeGatewayId)
            .content(content)
            .build();
        return computeTask;
    }

}
