package com.nti56.nlink.product.device.server.controller;

import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.R;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.GraphLabelType;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.InheritTypeEnum;
//import com.nti56.nlink.product.device.server.service.IModelGraphService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 类说明：
 *
 * @ClassName ModelDeviceGraphController
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/11/22 17:23
 * @Version 1.0
 */

@RestController
@RequestMapping("/graph")
@Tag(name = "模型设备继承关系")
public class ModelDeviceGraphController {

//    @Autowired
//    private IModelGraphService modelGraphService;

    @GetMapping("/{id}/{type}")
    @Deprecated
    @Operation(summary = "通过模型或设备ID和类型获取继承关系图", parameters = {
            @Parameter(name = "id", description = "设备ID/模型id", required = true)})
    public R getInheritsGraph(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, @PathVariable Long id, @PathVariable byte type, @RequestParam String modelType) {

        GraphLabelType graphLabelType = GraphLabelType.typeOfValue(modelType);
        InheritTypeEnum inheritType = InheritTypeEnum.typeOfValue(type);
        switch (graphLabelType){
            case THING_MODEL:
                switch (inheritType){
                    case INHERITED:
                        // return R.result(modelGraphService.queryWhichInheritMeByModelIdDeprecated(id));
                    case INHERITED_IN:
                        // return R.result(modelGraphService.queryInheritByModelIdDeprecated(id));
                    default:
                        return R.error("暂不支持的关系类型："+ type);
                }
            case Device:
                switch (inheritType) {
                    case INHERITED:
                        //return R.result(modelGraphService.queryWhichInheritMeByDeviceIdDeprecated(id));
                    case INHERITED_IN:
                        //return R.result(modelGraphService.queryInheritByDeviceIdDeprecated(id));
                    default:
                        return R.error("暂不支持的关系类型：" + type);
                }

            default:
                return R.error("暂不支持的Label类型："+ modelType);
        }

    }

    @GetMapping("/{type}")
    @Deprecated
    @Operation(summary = "通过设备或模型名称和类型获取继承关系图", parameters = {
            @Parameter(name = "id", description = "设备ID/模型id", required = true)})
    public R getInheritsGraphByName(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, @PathVariable byte type, @RequestParam String modelType, @RequestParam String name) {

        GraphLabelType graphLabelType = GraphLabelType.typeOfValue(modelType);
        InheritTypeEnum inheritType = InheritTypeEnum.typeOfValue(type);
        switch (graphLabelType){
            case THING_MODEL:
                switch (inheritType){
                    case INHERITED:
                        //return R.result(modelGraphService.queryWhichInheritMeByNameDeprecated(name, tenantIsolation.getTenantId()));
                    case INHERITED_IN:
                        //return R.result(modelGraphService.queryInheritByNameDeprecated(name, tenantIsolation.getTenantId()));
                    default:
                        return R.error("暂不支持的关系类型："+ type);
                }
            case Device:
                // return R.result(modelGraphService.queryInheritByDeviceNameDeprecated(name, tenantIsolation.getTenantId()));
            default:
                return R.error("暂不支持的Label类型："+ modelType);
        }

    }


    @GetMapping("/preview")
    @Deprecated
    @Operation(summary = "预览模型/设备信息", parameters = {@Parameter(name = "id", description = "设备ID/模型id", required = true)})
    public R getPreview(@RequestParam("id") Long id, @RequestParam("modelType") String modelType){
        GraphLabelType graphLabelType = GraphLabelType.typeOfValue(modelType);
        switch (graphLabelType){
            case THING_MODEL:
                //return R.result(modelGraphService.getModelPreviewDeprecated(id));
            case Device:
                //return R.result(modelGraphService.getDevicePreviewDeprecated(id));
            default:
                return R.error("暂不支持的Label类型："+ modelType);
        }
    }






}
