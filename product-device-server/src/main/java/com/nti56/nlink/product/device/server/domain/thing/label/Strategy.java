package com.nti56.nlink.product.device.server.domain.thing.label;

import com.nti56.nlink.common.util.Result;

/**
 * 类说明: 轮询策略
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-05-19 13:12:22
 * @since JDK 1.8
 */
public class Strategy {
    
    public static final Integer MIN_INTERVAL_MS = 10;
    public static final long MIN_INTERVAL_LONG_MS = 10L;
    public static final Integer MAX_INTERVAL_MS = 24 * 60 * 60 * 1000;

    public static Result<Void> validInterval(Integer interval){
        if(interval == null){
            return Result.error("间隔不能为空");
        }
        if(interval > MAX_INTERVAL_MS){
            return Result.error("间隔不能大于" + MAX_INTERVAL_MS);
        }
        if(interval < MIN_INTERVAL_MS){
            return Result.error("间隔不能小于" + MIN_INTERVAL_MS);
        }
        return Result.ok();
    }
}
