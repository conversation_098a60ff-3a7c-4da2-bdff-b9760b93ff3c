package com.nti56.nlink.product.device.server.controller;

import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.R;
import com.nti56.nlink.product.device.server.model.changeNotice.dto.CreateChangeNoticeDTO;
import com.nti56.nlink.product.device.server.model.changeNotice.dto.EditChangeNoticeDTO;
import com.nti56.nlink.product.device.server.model.changeNotice.dto.QueryChangeNoticeDTO;
import com.nti56.nlink.product.device.server.service.IChangeNoticeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


/**
 * <p>
 * 变动通知 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2023-2-17 11:40:31
 * @since JDK 1.8
 */
@RestController
@RequestMapping("changeNotice")
@Tag(name = "变动通知")
public class ChangeNoticeController {
    @Autowired
    private IChangeNoticeService changeNoticeService;

    @GetMapping("list")
    @Operation(summary = "查询变动通知列表" )
    public R listChangeNotice(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, QueryChangeNoticeDTO queryChangeNoticeDTO){
        return R.result(changeNoticeService.listChangeNotice(queryChangeNoticeDTO,tenantIsolation));
    }

    @PostMapping("")
    @Operation(summary = "新增变动通知")
    public R createChangeNotice(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                           @RequestBody @Validated CreateChangeNoticeDTO createChangeNoticeDTO){
        return R.result(changeNoticeService.createChangeNotice(createChangeNoticeDTO,tenantIsolation));
    }


    @DeleteMapping("{id}")
    @Operation(summary = "逻辑删除变动通知" )
    public R deleteChangeNotice(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@PathVariable Long id){
        return R.result(changeNoticeService.deleteChangeNotice(id,tenantIsolation));
    }

    @PutMapping("{id}")
    @Operation(summary = "修改变动通知")
    public R editChangeNotice(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                                @RequestBody @Validated EditChangeNoticeDTO editChangeNoticeDTO){
        return R.result(changeNoticeService.editChangeNotice(editChangeNoticeDTO,tenantIsolation));
    }
}
