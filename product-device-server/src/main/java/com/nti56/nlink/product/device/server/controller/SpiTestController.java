package com.nti56.nlink.product.device.server.controller;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.R;
import com.nti56.nlink.product.device.server.service.ISpiTestService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 类说明: Spi调试作用
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-07-14 13:32:04
 * @since JDK 1.8
 */
@Slf4j
@RestController
@RequestMapping("/spi")
@Tag( name = "Spi调试作用")
public class SpiTestController {

    @Autowired
    private ISpiTestService spiTestService;

    @GetMapping("connectChannel")
    @Operation(summary = "获取通道分页")
    public R connectChannel(@RequestHeader("ot_headers") TenantIsolation tenantIsolation){
        return  R.result(spiTestService.connectChannel(tenantIsolation));
    }

    @GetMapping("connectLabel")
    @Operation(summary = "获取通道分页")
    public R connectLabel(@RequestHeader("ot_headers") TenantIsolation tenantIsolation){
        return  R.result(spiTestService.connectLabel(tenantIsolation));
    }

    @PostMapping("writeBool")
    @Operation(summary = "获取通道分页")
    public R writeBool(@RequestHeader("ot_headers") TenantIsolation tenantIsolation){
        return  R.result(spiTestService.writeBool(tenantIsolation));
    }

    @PostMapping("writeBoolArray")
    @Operation(summary = "获取通道分页")
    public R writeBoolArray(@RequestHeader("ot_headers") TenantIsolation tenantIsolation){
        return  R.result(spiTestService.writeBoolArray(tenantIsolation));
    }

    @PostMapping("writeShort")
    @Operation(summary = "获取通道分页")
    public R writeShort(@RequestHeader("ot_headers") TenantIsolation tenantIsolation){
        return  R.result(spiTestService.writeShort(tenantIsolation));
    }

    @PostMapping("writeShortArray")
    @Operation(summary = "获取通道分页")
    public R writeShortArray(@RequestHeader("ot_headers") TenantIsolation tenantIsolation){
        return  R.result(spiTestService.writeShortArray(tenantIsolation));
    }

    @PostMapping("writeInt")
    @Operation(summary = "获取通道分页")
    public R writeInt(@RequestHeader("ot_headers") TenantIsolation tenantIsolation){
        return  R.result(spiTestService.writeInt(tenantIsolation));
    }

    @PostMapping("writeIntArray")
    @Operation(summary = "获取通道分页")
    public R writeIntArray(@RequestHeader("ot_headers") TenantIsolation tenantIsolation){
        return  R.result(spiTestService.writeIntArray(tenantIsolation));
    }

    @PostMapping("writeFloat")
    @Operation(summary = "获取通道分页")
    public R writeFloat(@RequestHeader("ot_headers") TenantIsolation tenantIsolation){
        return  R.result(spiTestService.writeFloat(tenantIsolation));
    }

    @PostMapping("writeFloatArray")
    @Operation(summary = "获取通道分页")
    public R writeFloatArray(@RequestHeader("ot_headers") TenantIsolation tenantIsolation){
        return  R.result(spiTestService.writeFloatArray(tenantIsolation));
    }

    @PostMapping("writeString")
    @Operation(summary = "获取通道分页")
    public R writeString(@RequestHeader("ot_headers") TenantIsolation tenantIsolation){
        return  R.result(spiTestService.writeString(tenantIsolation));
    }

    @PostMapping("writeStringArray")
    @Operation(summary = "获取通道分页")
    public R writeStringArray(@RequestHeader("ot_headers") TenantIsolation tenantIsolation){
        return  R.result(spiTestService.writeStringArray(tenantIsolation));
    }

    @PostMapping("writeByte")
    @Operation(summary = "获取通道分页")
    public R writeByte(@RequestHeader("ot_headers") TenantIsolation tenantIsolation){
        return  R.result(spiTestService.writeByte(tenantIsolation));
    }

    @PostMapping("writeByteArray")
    @Operation(summary = "获取通道分页")
    public R writeByteArray(@RequestHeader("ot_headers") TenantIsolation tenantIsolation){
        return  R.result(spiTestService.writeByteArray(tenantIsolation));
    }

    @PostMapping("writeWord")
    @Operation(summary = "获取通道分页")
    public R writeWord(@RequestHeader("ot_headers") TenantIsolation tenantIsolation){
        return  R.result(spiTestService.writeWord(tenantIsolation));
    }

    @PostMapping("writeWordArray")
    @Operation(summary = "获取通道分页")
    public R writeWordArray(@RequestHeader("ot_headers") TenantIsolation tenantIsolation){
        return  R.result(spiTestService.writeWordArray(tenantIsolation));
    }

    @PostMapping("writeDWord")
    @Operation(summary = "获取通道分页")
    public R writeDWord(@RequestHeader("ot_headers") TenantIsolation tenantIsolation){
        return  R.result(spiTestService.writeDWord(tenantIsolation));
    }

    @PostMapping("writeDWordArray")
    @Operation(summary = "获取通道分页")
    public R writeDWordArray(@RequestHeader("ot_headers") TenantIsolation tenantIsolation){
        return  R.result(spiTestService.writeDWordArray(tenantIsolation));
    }

    @PostMapping("writeDouble")
    @Operation(summary = "获取通道分页")
    public R writeDouble(@RequestHeader("ot_headers") TenantIsolation tenantIsolation){
        return  R.result(spiTestService.writeDouble(tenantIsolation));
    }

    @PostMapping("writeDoubleArray")
    @Operation(summary = "获取通道分页")
    public R writeDoubleArray(@RequestHeader("ot_headers") TenantIsolation tenantIsolation){
        return  R.result(spiTestService.writeDoubleArray(tenantIsolation));
    }

    @PostMapping("multiWrite")
    @Operation(summary = "获取通道分页")
    public R multiWrite(@RequestHeader("ot_headers") TenantIsolation tenantIsolation){
        return  R.result(spiTestService.multiWrite(tenantIsolation));
    }

}
