package com.nti56.nlink.product.device.server.domain.thing.enumerate;

import lombok.Getter;

/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName ModelTypeEnum
 * @date 2022/4/29 9:54
 * @Version 1.0
 */
public enum ModelTypeEnum {
    
    THING_MODEL(1,"thing_model",""),
    DEVICE_MODEL(2,"device_model",""),

    FAULT_MODEL(3,"fault_model",""),

    //系统通用模型
    COMMON_MODEL(4,"common_model",""),
    DEVICE_BASE_MODEL(5,"device_base_model","设备基础模型"),
    
    GATEWAY_MODEL(6,"gateway_model","网关"),
    CHANNEL_MODEL(7,"channel_model","通道"),

    ;


    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    ModelTypeEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static ModelTypeEnum typeOfValue(Integer value){
        ModelTypeEnum[] values = ModelTypeEnum.values();
        for (ModelTypeEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static ModelTypeEnum typeOfName(String name){
        ModelTypeEnum[] values = ModelTypeEnum.values();
        for (ModelTypeEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static ModelTypeEnum typeOfNameDesc(String nameDesc){
        ModelTypeEnum[] values = ModelTypeEnum.values();
        for (ModelTypeEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
    
}
