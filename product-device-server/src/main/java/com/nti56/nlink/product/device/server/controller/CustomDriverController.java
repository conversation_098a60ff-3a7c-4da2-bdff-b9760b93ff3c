package com.nti56.nlink.product.device.server.controller;

import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.util.MapUtils;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.BeanUtilsIntensifier;
import com.nti56.nlink.common.util.PageParam;
import com.nti56.nlink.common.util.R;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.nlink.product.device.client.model.dto.CustomDriverExportDTO;
import com.nti56.nlink.product.device.server.model.label.dto.LabelDTO;
import com.nti56.nlink.product.device.server.service.ICustomDriverService;
import com.nti56.nlink.product.device.server.service.IEdgeGatewayService;
import com.nti56.nlink.product.device.server.entity.CustomDriverEntity;
import com.nti56.nlink.product.device.server.feign.IEdgeGatewayCustomProxy;
import com.nti56.nlink.product.device.server.model.custom.CustomDriverBo;
import com.nti56.nlink.product.device.server.model.custom.CustomDriverDto;
import com.nti56.nlink.product.device.server.model.custom.CustomParam;

import io.swagger.annotations.*;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;


/**
 * <p>
 * 自定义协议表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2022-11-18 11:43:27
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/")
@Tag( name = "自定义协议模块")
@Api(protocols = "http,https", tags = "自定义协议表模块")
public class CustomDriverController {

    @Autowired
    ICustomDriverService service;

    @Autowired
    IEdgeGatewayCustomProxy edgeGatewayCustomProxy;

    @Autowired
    IEdgeGatewayService edgeGatewayService;

    @GetMapping("custom-driver/page")
    @Operation(summary = "获取分页")
    public R page(@RequestHeader("ot_headers") TenantIsolation tenant, PageParam pageParam,CustomDriverEntity entity){
        Page<CustomDriverEntity> page = pageParam.toPage(CustomDriverEntity.class);
        Result<Page<CustomDriverBo>> result = service.getPage(tenant, entity, page);
        return R.result(result);
    }

    @GetMapping("custom-driver/list")
    @Operation(summary = "获取列表" )
    public R list(@RequestHeader("ot_headers") TenantIsolation tenant, CustomDriverEntity entity){
        Result<List<CustomDriverEntity>> result = service.list(tenant, entity);
        return R.result(result);
    }

    @PostMapping("custom-driver")
    @Operation(summary = "创建自定义协议")
    public R create(@RequestHeader("ot_headers") TenantIsolation tenant, @ApiParam("对象") @RequestBody CustomDriverDto dto){
        Result<Long> result = service.create(tenant, dto);
        return R.result(result);
    }

    @PutMapping("custom-driver")
    @Operation(summary = "更新自定义协议")
    public R update(@RequestHeader("ot_headers") TenantIsolation tenant, @ApiParam("对象") @RequestBody CustomDriverDto dto){
        Result<Void> result = service.update(tenant, dto);
        return R.result(result);
    }

    @PutMapping("custom-driver/enable/{entityId}")
    @Operation(summary = "启用自定义协议")
    public R enable(@RequestHeader("ot_headers") TenantIsolation tenant, @ApiParam("目标ID") @PathVariable Long entityId){
        Result<Void> result = service.enableById(tenant, entityId);
        return R.result(result);
    }

    @PutMapping("custom-driver/disable/{entityId}")
    @Operation(summary = "停用自定义协议")
    public R disable(@RequestHeader("ot_headers") TenantIsolation tenant, @ApiParam("目标ID") @PathVariable Long entityId){
        Result<Void> result = service.disableById(tenant, entityId);
        return R.result(result);
    }

    @DeleteMapping("custom-driver/{entityId}")
    @Operation(summary = "删除自定义协议")
    public R delete(@RequestHeader("ot_headers") TenantIsolation tenant, @ApiParam("目标ID") @PathVariable Long entityId){
        Result<Void> result = service.deleteById(tenant, entityId);
        return R.result(result);
    }

    @GetMapping("custom-driver/{entityId}")
    @Operation(summary = "获取自定义协议")
    public R get(@RequestHeader("ot_headers") TenantIsolation tenant, @ApiParam("目标ID") @PathVariable Long entityId){
        Result<CustomDriverDto> result = service.getById(tenant, entityId);
        return R.result(result);
    }

    @PostMapping("custom-driver/serialize")
    @Operation(summary = "自定义协议序列化")
    public R postSerialize(
        @RequestHeader("ot_headers") TenantIsolation tenant, 
        @ApiParam("参数") @RequestBody @Validated CustomParam param
    ){
        Result<Boolean> onlineResult = edgeGatewayService.edgeGatewayOnline(tenant.getTenantId(), param.getEdgeGatewayId());
        if(!onlineResult.getSignal()){
            return R.error("网关在线检测异常:" + onlineResult.getMessage());
        }
        if(!onlineResult.getResult()){
            return R.error("操作失败：网关离线");
        }
        Result<String> result = edgeGatewayCustomProxy.customDriverSerialize(
            param.getEdgeGatewayId(), 
            tenant.getTenantId(), 
            param.getCustomDriverName(), 
            param.getMessageName(), 
            param.getVar()
        );
        return R.result(result);
    }

    @PostMapping("custom-driver/deserialize")
    @Operation(summary = "自定义协议反序列化")
    public R postDeserialize(
        @RequestHeader("ot_headers") TenantIsolation tenant, 
        @ApiParam("参数") @RequestBody CustomParam param
    ){
        Result<Boolean> onlineResult = edgeGatewayService.edgeGatewayOnline(tenant.getTenantId(), param.getEdgeGatewayId());
        if(!onlineResult.getSignal()){
            return R.error("网关在线检测异常:" + onlineResult.getMessage());
        }
        if(!onlineResult.getResult()){
            return R.error("操作失败：网关离线");
        }
        Result<Map<String, Object>> result = edgeGatewayCustomProxy.customDriverDeserialize(
            param.getEdgeGatewayId(), 
            tenant.getTenantId(), 
            param.getCustomDriverName(), 
            param.getMessageName(), 
            param.getStr()
        );
        return R.result(result);
    }

    @GetMapping({"custom-driver/export/{driverId}"})
    @Operation(summary = "导出自定义协议配置")
    public void exportCustomDriver(HttpServletResponse response, @RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                                   @PathVariable("driverId") Long driverId) throws IOException {

        try (OutputStream out = response.getOutputStream()){
            Result<CustomDriverExportDTO> result = service.exportCustomDriver(tenantIsolation, driverId);
            if(result.getSignal()) {
                CustomDriverExportDTO customDriverDto = result.getResult();
                String jsonStr = JSONUtil.toJsonStr(customDriverDto);
                byte[] bytes = jsonStr.getBytes(StandardCharsets.UTF_8);
                response.setContentType("application/octet-stream;charset=UTF-8");
                String fileName = URLEncoder.encode(customDriverDto.getCustomDriverDTO().getDriverName() + "_config", "UTF-8").replaceAll("\\+", "%20");
                response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".cpc");
                out.write(bytes,0,bytes.length);
            }else{
                errorExport(response, result.getMessage());
            }
        } catch (Exception e) {
            errorExport(response, e.getMessage());
        }

    }

    @PostMapping({"custom-driver/import"})
    @Operation(summary = "导入自定义协议配置")
    public R importCustomDriver(HttpServletResponse response, @RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                                   MultipartFile file,String driverName) throws IOException {

        return R.result(service.importCustomDriver(tenantIsolation, file, driverName));

    }

    private void errorExport(HttpServletResponse response, String message) throws IOException {
        response.reset();
        response.setContentType("application/json");
        response.setCharacterEncoding("utf-8");
        Map<String, String> map = MapUtils.newHashMap();
        map.put("status", "failure");
        map.put("message", "导出失败" + message);
        response.getWriter().println(JSON.toJSONString(map));
    }
}
