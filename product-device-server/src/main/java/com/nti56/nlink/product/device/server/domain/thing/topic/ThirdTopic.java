package com.nti56.nlink.product.device.server.domain.thing.topic;

import com.nti56.nlink.product.device.server.domain.thing.enumerate.MqttTopicEnum;

public class ThirdTopic {
    
    public static String createSendTopicPrefix(Long tenantId, Long edgeGatewayId, Long channelId) {
        return MqttTopicEnum.THIRD_CUSTOM.getPrefix()
            + tenantId + "/"
            + edgeGatewayId + "/"
            + channelId + "/" 
            + "sendMessage" + "/" 
            ;
    }
    
    /**
     * third/custom/1527470820947841026/1364785558069248/123/sendMessage/?requestId=1
     */
    public static String createSubscribeSendTopic(Long tenantId, Long edgeGatewayId, Long channelId){
        return MqttTopicEnum.THIRD_CUSTOM.getPrefix()
            + tenantId + "/"
            + edgeGatewayId + "/"
            + channelId + "/" 
            + "sendMessage" + "/#"
            ;
    }
    
    /**
     * gw/custom/1527470820947841026/1364785558069248/123/receiveMessage
     */
    public static String createSubscribeReceiveTopic(Long tenantId, Long edgeGatewayId, Long channelId){
        return MqttTopicEnum.GW_CUSTOM.getPrefix()
            + tenantId + "/"
            + edgeGatewayId + "/"
            + channelId + "/" 
            + "receiveMessage" + "/#"
            ;
    }

    /**
     * gw/custom/1527470820947841026/1364785558069248/123/autoResponseMessage
     */
    public static String createSubscribeAutoResponseTopic(Long tenantId, Long edgeGatewayId, Long channelId){
        return MqttTopicEnum.GW_CUSTOM.getPrefix()
            + tenantId + "/"
            + edgeGatewayId + "/"
            + channelId + "/" 
            + "autoResponseMessage" + "/#"
            ;
    }
    

}
