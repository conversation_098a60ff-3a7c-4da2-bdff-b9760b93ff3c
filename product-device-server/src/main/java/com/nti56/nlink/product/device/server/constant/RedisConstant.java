package com.nti56.nlink.product.device.server.constant;

/**
 * 类说明: redis key前缀、过期时间等统一管理
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-08 10:38:47
 * @since JDK 1.8
 */
public class RedisConstant {

    public static final String DING_DING_FIRST_SEND_TIMESTAMP = "notify:ding_ding_first_send_timestamp:";

    public static final String DING_DING_SEND_TIMES = "notify:ding_ding_send_times:";

    public static final String DING_DING_SEND_NOTIFY = "notify:ding_ding_send_notify:";
    public static final String DING_DING_RETRY_SEND_NOTIFY = "notify:ding_ding_retry_send_notify:";

    public static final String EDIT_PASSWORD_VERIFICATION_CODE = "notify:edit_password_verification_code:";

    public static final String BIND_PHONE_VERIFICATION_CODE = "notify:bind_phone_verification_code:";

    public static final String LOGIN_VERIFICATION_CODE = "notify:login_verification_code:";

    public static final String VERIFY_PHONE_VERIFICATION_CODE = "notify:verify_phone_verification_code:";

    public static final String REGISTER_VERIFICATION_CODE = "notify:register_verification_code:";
    public static final String DING_CHANNEL_SEMAPHORE = "notify:semaphore:ding:%s";

    public final static String EDGE_GATEWAY_HEARTBEAT_PREFIX = "pd_edge_gateway_task_heartbeat_";
    public final static String DEVICE_NOCHANGE_PREFIX = "pd_device_nochange_";
    public final static String GATHER_LABEL_COUNT_PREFIX = "pd_gather_label_count_";
    public final static String COMPUTE_TASK_COUNT_PREFIX = "pd_compute_task_count_";
    public final static String GATHERING_PREFIX = "pd_gathering_";
    public final static String EDGE_GATEWAY_CHANNEL_STATUS_PREFIX = "pd_edge_gateway_channel_status_";
    public final static String EDGE_CHANNEL_STATUS_CACHE_PREFIX = "pd_edge_channel_status_cache";
    
    public final static String GATHER_TASK_HEARTBEAT_PREFIX = "pd_gather_task_heartbeat_";

    public final static String EDGE_GATEWAY_STATUS = "pd_edge_gateway_status_";
    
    public final static String EDGE_CHANNEL_STATUS = "pd_channel_status_";

    public final static String DEVICE_STATUS = "pd_device_status_";

    public final static String DEVICE_OFFLINE_TIMES = "pd_device_offline_times_";

    public final static String DEVICE_ENABLE = "pd:device:enable:%s";
    public final static String DEVICE_BIND = "pd:device:bind:%s";

    public final static String CURRENT_VERSION_PREFIX = "pd_current_version_";
    
    public final static String DOWNLOAD_STATUS = "pd_download_status_";
    
    public final static String DOWNLOAD_UPGRADE_VERSION = "pd_download_upgrade_version_";
    
    public final static String INSTANCE = "pd_instance_";
    
    public final static String SYNCING_PREFIX = "syncing_prefix_";

    public static final String FAULT_EVENT_PREFIX = "FE:";
    
    public static final String TRIGGER_EVENT_PREFIX = "TRIGGER_EVENT:";
    
    public static final String NO_CHANGE_EVENT_PREFIX = "NO_CHANGE_EVENT:";
    /**
     * key规则：前缀加资源关系表Id，存为Hash
     * DEVICE_RUNTIME_PREFIX + resourceId
     */
    public final static String DEVICE_RUNTIME_PREFIX = "pd_device_runtime_";


    /**
     * pd:device:twin:actual:{deviceId} ->[Hash] 设备孪生数据
     * pd:device:twin:changeTime:{deviceId} ->[Hash] 设备属性改变事件（时间戳）
     */
    public final static String DEVICE_TWIN_PREFIX = "pd:device:twin";

    /**
     * pd:groupMapping:{edgeGatewayId}:{channel}:{group} -> [Hash]{device:[A,B,C]}
     */
    public final static String GROUP_DEVICE_MAPPING_PREFIX = "pd:groupMapping";

    /**
     * pd:edgeGateway:change:state:{edgeGatewayId} -> [String]{isEdgeGatewayChange:true,changeTime:}
     */
    public final static String EDGE_GATEWAY_CHANGE_STATE = "pd:edgeGateway:change:state:%s";
    public final static String OT_GATEWAY_CHANGE_STATE = "pd:otGateway:change:state:%s";


    /**
     * pd:device:log:service ->[set] 设备服务日志
     */
    public final static String DEVICE_SERVICE_LOG = "pd:device:log:service";


    /**
     * pd:device:log:service:lock ->[string] 设备服务日志插入锁
     */
    public final static String DEVICE_SERVICE_LOG_LOCK = "pd:device:log:service:lock";

    public final static Integer HEARTBEAT_TTL = 15;

    public final static Integer CHANNEL_STATUS_TTL = 15;

    public final static Integer SYNCING_TTL = 30;
    
    /**
     * 物服务脚本每日调用次数 tenantId yyyyMMdd
     */
    public final static String THING_SERVICE_DAILY_CALLED_COUNT_PREFIX = "pd:thing:service:called:count:%s:%s";

    public final static String ASSEMBLE = "product:assemble:";

    /**
     * pd:groupMapping:{edgeGatewayId}:{channel}:{group}
     */
    public final static String GROUP_DEVICE_MAPPING = "pd:groupMapping:%s:%s:%s";

    /**
     * pd:event:{deviceId} -> [{event1},{event2}...]
     */
    public final static String EVENT_REGISTRY = "pd:event:%s";



    /**
     * pd:label:property:{deviceId}
     */
    public final static String LABEL_PROPERTY_MAPPING = "pd:label:property:%s";

    public final static String SUBSCRIPTION_REGISTRY = "product:subscription:registry:%s:%s";
    
    /**
     * 订阅注册表索引键，用于快速获取设备的所有订阅注册表键，避免scan操作
     * pd:subscription:index:{deviceId}
     */
    public final static String SUBSCRIPTION_INDEX = "pd:subscription:index:%s";
    
    public final static String SUBSCRIPTION_EDGE_GATEWAY_REGISTRY = "product:subscription:edgeGateway:registry:%s:%s";
    
    public final static String SUBSCRIPTION_CHANNEL_REGISTRY = "product:subscription:channel:registry:%s:%s";
    
    
    /**
     * product:subscription:enable:{tenantId}:{deviceId}
     */
    public final static String SUBSCRIPTION_ENABLE = "product:subscription:enable:%s:%s";

    public final static String SUBSCRIPTION_CODE = "product:subscription:code:%s";

    public static final String DEVICE_TENANT = "pd:tenant:%s";

    // fault eventName deviceId
    public static final String DEVICE_FAULT_STATUS = "pd:device:fault:%s:%s:status:%s";

    // deviceId
    public static final String DEVICE_FAULT_INSTANCE_CACHE = "pd:device:fault:%s:instance:%s";

    // 当前故障设备数 tenant
    public static final String DEVICE_FAULT_COUNT = "pd:device:fault:%s:count";

    // 当前故障设备IDS tenant
    public static final String TENANT_DEVICE_ON_FAULT = "pd:device:fault:%s:deviceIds";



    // deviceId  hash  key -- eventName
    public static final String DEVICE_FAULT_EVENT_INSTANCE = "pd:device:fault:%s:eventInstance:%s";

    //tenantId  businessId
    public static final String SERVICE_CRON_JOB = "pd:service:corn:%s:%s";

    public static final String ACTIVE_FLOW_CONFIG_KEY= "r_e:flows:activeFlowConfig";
    public static final String REDIRECT_INVOKE_TIMES= "redirect:invoke:%s";
    public static final String REDIRECT_INVOKE_TIMES_DAILY= "redirect:invoke:day:%s:$s";
    public static final String REDIRECT_INVOKE_LAST_TIME= "redirect:invoke:time:%s";
    public static final String REDIRECT_INSTANCE_CACHE = "redirect:instance:%s";
    public static final String REDIRECT_REFERENCE_CACHE = "redirect:reference:%s";

    public static final String NOTIFICATION_READ_MAP = "pd:notification:%s";

    //deviceId eventName
    public static final String DEVICE_FAULT_EVENT_STATUS = "pd:device:fault:status:%s:%s";
    public static final String DEVICE_FAULT_CACHE = "pd:device:fault:lockKey:%s:%s-cache";
    public static final String DEVICE_FAULT_LOCK_STATE = "pd:device:fault:lock:state:%s:%s";
    public static final String DEVICE_FAULT_LOCK = "pd:device:fault:lockKey:%s:%s";
    
    //deviceId eventName level
    public static final String DEVICE_FAULT_LEVEL_LOCK_START = "pd:device:fault:level:lock:start:%s:%s:%s";
    public static final String DEVICE_FAULT_LEVEL_LOCK_END = "pd:device:fault:level:lock:end:%s:%s:%s";
    
    public static final String JOB_PARAM_CACHE = "pd:service:task:params:%s";
}
