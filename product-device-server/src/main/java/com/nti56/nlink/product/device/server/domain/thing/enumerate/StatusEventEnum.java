package com.nti56.nlink.product.device.server.domain.thing.enumerate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Getter;

/**
 * 类说明: 状态枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023-07-15 13:33:25
 * @since JDK 1.8
 */
public enum StatusEventEnum {
    OFFLINE("offline", "offline", "离线"),
    ONLINE("online", "online", "上线"),
    CONNECT("connect", "connect", "连接"),
    DISCONNECT("disconnect", "disconnect", "断开"),
    MEMORY("memory","memory","内存"),
    SPACE("space","space","空间"),
    ;

    @Getter
    private String value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    StatusEventEnum(String value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static StatusEventEnum typeOfValue(Integer value){
        StatusEventEnum[] values = StatusEventEnum.values();
        for (StatusEventEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static StatusEventEnum typeOfName(String name){
        StatusEventEnum[] values = StatusEventEnum.values();
        for (StatusEventEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static StatusEventEnum typeOfNameDesc(String nameDesc){
        StatusEventEnum[] values = StatusEventEnum.values();
        for (StatusEventEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }


    public static List toList(){
        List<Map> result = new ArrayList<>();
        StatusEventEnum[] values = StatusEventEnum.values();
        Map<String,Object> map ;
        for (StatusEventEnum v : values) {
            map = new HashMap<>();
            map.put("name",v.name);
            map.put("value",v.value);
            map.put("nameDesc",v.nameDesc);
            result.add(map);
        }
        return result;
    }
}
