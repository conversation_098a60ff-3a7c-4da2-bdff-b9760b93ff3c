package com.nti56.nlink.product.device.server.controller;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.*;
import com.nti56.nlink.product.device.server.service.ISubscriptionService;
import com.nti56.nlink.product.device.server.service.IWorkConsoleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


@RestController
@RequestMapping("workConsole")
@Tag(name = "控制台")
@Slf4j
public class WorkConsoleController {

    @Autowired
    private IWorkConsoleService workConsoleService;
    @Autowired
    private ISubscriptionService subscriptionService;


    @GetMapping("/workConsoleData")
    @Operation(summary = "控制台网关数据" )
    public R getWorkConsoleData(@RequestHeader("ot_headers") TenantIsolation tenantIsolation){
        return R.ok(workConsoleService.getWorkConsoleData(tenantIsolation));
    }
    @GetMapping("/testRedirect")
    public R getWorkConsoleData(){
        JSONObject object = new JSONObject();
        object.put("payload","abcde");
        subscriptionService.execRedirectWithPayload(1561632766420258818L,1377362801762304L, JSONUtil.toJsonStr(object));
        return R.ok();
    }

}
