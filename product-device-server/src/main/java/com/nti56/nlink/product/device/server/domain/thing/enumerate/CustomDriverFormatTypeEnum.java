package com.nti56.nlink.product.device.server.domain.thing.enumerate;

import lombok.Getter;

public enum CustomDriverFormatTypeEnum {
    FIX_HEADER(1, "fixHeader", "固定头"),
    FIX_HEADER_TAIL(2, "fixHeaderTail", "固定报文头报文尾")
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    CustomDriverFormatTypeEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static CustomDriverFormatTypeEnum typeOfValue(Integer value){
        CustomDriverFormatTypeEnum[] values = CustomDriverFormatTypeEnum.values();
        for (CustomDriverFormatTypeEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static CustomDriverFormatTypeEnum typeOfName(String name){
        CustomDriverFormatTypeEnum[] values = CustomDriverFormatTypeEnum.values();
        for (CustomDriverFormatTypeEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static CustomDriverFormatTypeEnum typeOfNameDesc(String nameDesc){
        CustomDriverFormatTypeEnum[] values = CustomDriverFormatTypeEnum.values();
        for (CustomDriverFormatTypeEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
