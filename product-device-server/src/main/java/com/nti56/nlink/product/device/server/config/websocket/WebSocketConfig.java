package com.nti56.nlink.product.device.server.config.websocket;

import com.nti56.nlink.product.device.server.handler.websocket.LabelCollectingHandler;
import com.nti56.nlink.product.device.server.handler.websocket.PropertyCollectingHandler;
import com.nti56.nlink.product.device.server.websocket.CollectingInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;

/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName WebSocketConfig
 * @date 2022/9/19 16:01
 * @Version 1.0
 */
@Configuration
@EnableWebSocket
public class WebSocketConfig implements WebSocketConfigurer {

    @Autowired
    LabelCollectingHandler labelCollectingHandler;

    @Autowired
    PropertyCollectingHandler propertyCollectingHandler;

    @Autowired
    CollectingInterceptor collectingInterceptor;

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry webSocketHandlerRegistry) {
        webSocketHandlerRegistry
            .addHandler(labelCollectingHandler, "/api/websocket/label-collecting")
            .addHandler(propertyCollectingHandler, "/api/websocket/property-collecting")
            .addInterceptors(collectingInterceptor)
            .setAllowedOrigins("*");
    }

}
