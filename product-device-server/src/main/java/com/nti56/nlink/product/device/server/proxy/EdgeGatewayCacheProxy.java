package com.nti56.nlink.product.device.server.proxy;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.client.model.dto.json.device.AccessElm;
import com.nti56.nlink.product.device.client.model.dto.json.device.ChannelElm;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.MqttTopicEnum;
import com.nti56.nlink.product.device.server.feign.IEdgeGatewayCacheProxy;
import com.nti56.nlink.product.device.server.model.ConnectResult;
import com.nti56.nlink.product.device.server.util.MqttProxyEventBusUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 类说明: 边缘网关缓存代理 - EventBus实现
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024-01-01 00:00:00
 * @since JDK 1.8
 */
@Component
public class EdgeGatewayCacheProxy implements IEdgeGatewayCacheProxy {

    @Autowired
    private MqttProxyEventBusUtil mqttProxyEventBusUtil;

    @Override
    public Result<List<ConnectResult>> labelValue(Long edgeGatewayId, Long tenantId, List<AccessElm> labelList) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("labelList", labelList);
            String body = JSONObject.toJSONString(params);
            
            CompletableFuture<String> future = mqttProxyEventBusUtil.sendAssignRequest(
                MqttTopicEnum.OT_CACHE, 
                "request", 
                tenantId.toString(), 
                edgeGatewayId.toString(), 
                "labelValue", 
                body
            );
            
            String response = future.get();
            return JSONObject.parseObject(response, new TypeReference<Result<List<ConnectResult>>>(){});
        } catch (Exception e) {
            return Result.error("获取标签值失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<ConnectResult>> channelConnection(Long edgeGatewayId, Long tenantId, List<ChannelElm> channelList) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("channelList", channelList);
            String body = JSONObject.toJSONString(params);
            
            CompletableFuture<String> future = mqttProxyEventBusUtil.sendAssignRequest(
                MqttTopicEnum.OT_CACHE, 
                "request", 
                tenantId.toString(), 
                edgeGatewayId.toString(), 
                "channelConnection", 
                body
            );
            
            String response = future.get();
            return JSONObject.parseObject(response, new TypeReference<Result<List<ConnectResult>>>(){});
        } catch (Exception e) {
            return Result.error("获取通道连接状态失败: " + e.getMessage());
        }
    }
} 