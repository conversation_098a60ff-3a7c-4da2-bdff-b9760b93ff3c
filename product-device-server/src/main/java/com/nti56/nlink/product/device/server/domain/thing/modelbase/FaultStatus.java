package com.nti56.nlink.product.device.server.domain.thing.modelbase;

import java.util.HashMap;
import java.util.Map;

import com.nti56.nlink.common.util.CurrentTimeMillisClock;
import com.nti56.nlink.product.device.client.model.dto.json.enumerate.WarningLevelEnum;

import lombok.Data;

@Data
public class FaultStatus {

    private Boolean status; //true-激活，false-停止
    private Long begin; //开始时间戳
    private Long end; //结束时间戳
    private Integer triggerCount;
    private Integer notTriggerCount;

    private Map<Integer, Long> beginMap = new HashMap<>();
    // private Map<Integer, Long> endMap = new HashMap<>();
    private Map<Integer, Integer> triggerCountMap = new HashMap<>();
    // private Map<Integer, Integer> notTriggerCountMap = new HashMap<>();

    public FaultStatus() {
        this.status = false;
        this.triggerCount = 0;
        this.notTriggerCount = 0;
        this.begin = 0L;
        this.end = 0L;
        WarningLevelEnum[] values = WarningLevelEnum.values();
        for (WarningLevelEnum value : values) {
            triggerCountMap.put(value.getCode(), 0);
            // notTriggerCountMap.put(value.getCode(), 0);
            beginMap.put(value.getCode(), 0L);
            // endMap.put(value.getCode(), 0L);
        }
    }

    public void clearTrigger() {
        triggerCount = 0;
    }

    public void clearNotTrigger() {
        notTriggerCount = 0;
    }

    public Integer addAndGetTrigger() {
        triggerCount++;
        return triggerCount;
    }

    public Integer addAndGetNotTrigger() {
        notTriggerCount++;
        return notTriggerCount;
    }

    public void beginFault() {
        this.status = true;
        this.begin = CurrentTimeMillisClock.getInstance().now();
        this.notTriggerCount = 0;
    }

    public void endFault() {
        this.status = false;
        this.end = CurrentTimeMillisClock.getInstance().now();
        this.triggerCount = 0;
    }

    public Integer getLevelTriggerCount(int level){
        return this.triggerCountMap.get(level);
    }

    // public Integer getLevelNotTriggerCount(int level){
    //     return this.notTriggerCountMap.get(level);
    // }

    public Long getLevelBegin(int level){
        return this.beginMap.get(level);
    }

    // public Long getLevelEnd(int level){
    //     return this.endMap.get(level);
    // }

    public Integer addAndGetTrigger(int level) {
        Integer triggerCount = this.triggerCountMap.get(level) + 1;
        this.triggerCountMap.put(level, triggerCount);
        return triggerCount;
    }

    public void beginFault(int level) {
        this.status = true;
        this.beginMap.put(level, CurrentTimeMillisClock.getInstance().now());
        this.notTriggerCount = 0;
    }

    public void endAllFault() {
        this.status = false;
        this.end = CurrentTimeMillisClock.getInstance().now();
        // this.triggerCount = 0;
        for (int level : this.triggerCountMap.keySet()) {
            this.triggerCountMap.put(level, 0);
        }
    }

    public void clearLevelTriggerCount(int level) {
        this.triggerCountMap.put(level, 0);
        this.beginMap.put(level, 0L);
    }


}
