package com.nti56.nlink.product.device.server.domain.thing.enumerate;

import com.nti56.nlink.product.device.server.domain.thing.channel.*;

import lombok.Getter;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 类说明: 驱动枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 14:05:02
 * @since JDK 1.8
 */
public enum DriverEnum {
    SNAP7(1, "Snap7", "Siemens TCP/IP Ethernet", "西门子", Snap7ChannelParam.requiredParam),
    MODBUS(2, "Modbus", "Modbus TCP/IP","modbus",ModbusChannelParam.requiredParam),
    OPCUA(3, "OPCUA", "OPC UA","opc-ua", OpcUaChannelParam.requiredParam),
//    ZIG_BEE(4, "ZigBee", "ZigBee",null,null),
//    BLE(5, "BLE", "BLE",null,null)
    ETHER_IP(6, "ABEtherIp", "AllenBrandly EtherNet/IP","罗克韦尔", EtherIpChannelParam.requiredParam),
    MELSEC(7, "MELSEC", "MELSEC TCP/IP Ethernet","三菱", MelsecChannelParam.requiredParam),
    KEYENCE_NANO(8, "KEYENCE_NANO", "KEYENCE_NANO","基恩士", KeyenceChannelParam.requiredParam),
    ModbusRtuOverTcp(9, "ModbusRtuOverTcp", "ModbusRtuOverTcp","modbus",ModbusChannelParam.requiredParam),
    ModbusRtu(10, "ModbusRtu", "ModbusRtu","modbus",ModbusRtuChannelParam.requiredParam),
    BACNET_IP(11, "BACnet/IP", "BACnet/IP","楼宇自动化与控制网络",BacnetIpChannelParam.requiredParam),
    ADS(12, "ADS", "Beckhoff/ADS","倍福",AdsChannelParam.requiredParam),
    IEC104(13, "IEC104", "IEC104","iec104",Iec104ChannelParam.requiredParam),
    Panasonic(14, "Panasonic", "Panasonic","panasonic",PanasonicChannelParam.requiredParam),
    FINS_TCP(16, "FinsTcp", "FinsTcp","finstcp",FinsChannelParam.requiredParam),
    INOVANCE_TCP(17, "InovanceTcp", "InovanceTcp","inovancetcp",InovanceChannelParam.requiredParam),
    CVC600(18, "CVC600", "CVC600","cvc600",CVC600ChannelParam.requiredParam),
    DLT645(19, "DLT645", "DLT645","dlt645",DLT645ChannelParam.requiredParam),
    HTTP(20, "HTTP", "HTTP","http",HttpChannelParam.requiredParam),
    CUSTOM(100, "Custom", "自定义协议","自定义协议",CustomChannelParam.requiredParam)
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    @Getter
    private String readmeUrl;

    @Getter
    private String[] requiredParam;

    DriverEnum(Integer value, String name, String nameDesc, String readmeUrl, String[] requiredParam) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
        this.readmeUrl = readmeUrl;
        this.requiredParam = requiredParam;
    }

    public static DriverEnum typeOfValue(Integer value){
        DriverEnum[] values = DriverEnum.values();
        for (DriverEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static String getNameByValue(Integer value){
        DriverEnum[] values = DriverEnum.values();
        for (DriverEnum v : values) {
            if (v.value.equals(value)) {
                return v.name;
            }
        }
        return null;
    }

    public static DriverEnum typeOfName(String name){
        DriverEnum[] values = DriverEnum.values();
        for (DriverEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static DriverEnum typeOfNameDesc(String nameDesc){
        DriverEnum[] values = DriverEnum.values();
        for (DriverEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }

    public static String[] getRequireParamByValue(Integer value){

        DriverEnum[] values = DriverEnum.values();
        for (DriverEnum v : values) {
            if (v.value.equals(value)){
                return v.getRequiredParam();
            }
        }
        return null;
    }

    public static List<Map<String, Object>> toList(){
        List<Map<String, Object>> result = new ArrayList<>();
        DriverEnum[] values = DriverEnum.values();
        Map<String,Object> map ;
        for (DriverEnum v : values) {
            if(DriverEnum.CUSTOM.equals(v)){
                continue;
            }
            map = new HashMap<>();
            map.put("name",v.name);
            map.put("nameDesc",v.nameDesc);
            map.put("value",v.value);
            map.put("readmeUrl",v.readmeUrl);
            map.put("requiredChannelParams",v.requiredParam);
            result.add(map);
        }
        List<Map<String, Object>> list = result.stream().sorted(Comparator.comparing(d -> (String) d.get("name"), Comparator.naturalOrder())).collect(Collectors.toList());

        map = new HashMap<>();
        map.put("name",DriverEnum.CUSTOM.name);
        map.put("nameDesc",DriverEnum.CUSTOM.nameDesc);
        map.put("value",DriverEnum.CUSTOM.value);
        map.put("readmeUrl",DriverEnum.CUSTOM.readmeUrl);
        map.put("requiredChannelParams",DriverEnum.CUSTOM.requiredParam);
        map.put("serverRequiredChannelParams", CustomChannelParam.serverRequiredParam);
        list.add(map);

        return list;
    }
}
