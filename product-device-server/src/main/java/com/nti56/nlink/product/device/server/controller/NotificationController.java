package com.nti56.nlink.product.device.server.controller;

import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.PageParam;
import com.nti56.nlink.common.util.R;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.model.notification.dto.NotificationDTO;
import com.nti56.nlink.product.device.server.service.INotificationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


@RestController
@RequestMapping("notification")
@Tag(name = "系统通知")
public class NotificationController {

    @Autowired
    private INotificationService notificationService;


    @GetMapping("page")
    @Operation(summary = "系统通知列表分页")
    public R pageList(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, PageParam pageParam, NotificationDTO queryDTO) {
        return R.result(notificationService.pageList(pageParam, queryDTO));
    }

    @PostMapping("")
    @Operation(summary = "新增系统通知")
    public R create(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, @RequestBody NotificationDTO notificationDTO) {
        return R.result(notificationService.createNew(notificationDTO));
    }

    @PutMapping("{id}")
    @Operation(summary = "更新系统通知")
    public R update(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, @RequestBody NotificationDTO notificationDTO,
                    @PathVariable("id") Long id ){
        return R.result(notificationService.updateNotification(tenantIsolation,notificationDTO,id));
    }

    @PutMapping("read/{id}")
    @Operation(summary = "已读")
    public R read(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, @PathVariable("id") Long id) {
        return R.result(notificationService.read(tenantIsolation, id));
    }

    @GetMapping("listLatest4")
    @Operation(summary = "用户最新4条")
    public R listLatest4(@RequestHeader("ot_headers") TenantIsolation tenantIsolation) {
        return R.result(notificationService.listLatest4(tenantIsolation));
    }

    @GetMapping("{id}")
    @Operation(summary = "获取通知明细")
    public R getNotification(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, @PathVariable("id") Long id) {
        return R.result(Result.ok(notificationService.getById(id)));
    }

    @DeleteMapping("{id}")
    @Operation(summary = "删除通知")
    public R delete(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, @PathVariable("id") Long id){
        return R.result(notificationService.deleteById(id));
    }
}
