package com.nti56.nlink.product.device.server.controller;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.*;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.ThingServiceCallTypeEnum;
import com.nti56.nlink.product.device.server.entity.DeviceEntity;
import com.nti56.nlink.product.device.server.entity.DeviceServiceLogEntity;
import com.nti56.nlink.product.device.server.exception.BizException;
import com.nti56.nlink.product.device.server.model.*;
import com.nti56.nlink.product.device.server.model.device.dto.CreateByAssembleInfoDTO;
import com.nti56.nlink.product.device.server.model.device.vo.DeviceOnlineStatusVO;
import com.nti56.nlink.product.device.server.model.deviceModel.dto.AssembleByLabelGroupIdsDTO;
import com.nti56.nlink.product.device.server.openapi.domain.request.DoServiceTaskRequest;
import com.nti56.nlink.product.device.server.openapi.domain.request.ListTagRequest;
import com.nti56.nlink.product.device.server.service.IDeviceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * 类说明: 设备controller
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 13:32:04
 * @since JDK 1.8
 */
@RestController
@RequestMapping("device")
@Tag(name = "设备模块")
@Slf4j
public class DeviceController {

    @Autowired
    IDeviceService deviceService;

    @GetMapping("page")
    @Operation(summary = "获取设备分页")
    public R pageDevice(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,PageParam pageParam, DeviceDto device) {
        BeanUtilsIntensifier.propertyInjection(tenantIsolation,device);
        Page<DeviceDto> page = pageParam.toPage(DeviceDto.class);
        Result<Page<DeviceDto>> result = deviceService.getDevicePage(device, page);
        return R.result(result);
    }

    @PostMapping("exportDevice")
    @Operation(summary = "导出设备")
    public void exportDevice(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@RequestBody DeviceDto device, HttpServletResponse response) {
        BeanUtilsIntensifier.propertyInjection(tenantIsolation, device);
        deviceService.exportDevice(device, response);
    }

    @GetMapping("list")
    @Operation(summary = "获取设备列表")
    public R listDevice(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, DeviceDto device) {
        DeviceEntity entity = DeviceEntity.builder().build();
        BeanUtilsIntensifier.propertyInjection(tenantIsolation,entity);
        Result<List<DeviceEntity>> result = deviceService.listDevice(entity);
        return R.result(result);
    }

    @PostMapping("tags/list")
    @Operation(summary = "根据标记获取设备列表")
    public R listByTags(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,DeviceDto dto) {
        BeanUtilsIntensifier.propertyInjection(tenantIsolation,dto);
        Result<List<DeviceBo>> result = deviceService.listDeviceByTag(dto);
        return R.result(result);
    }

    @GetMapping("list/{edgeGatewayId}")
    @Operation(summary = "根据网关获取设备列表")
    public R listDevice(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@Parameter(description = "网关ID") @PathVariable Long edgeGatewayId) {
        DeviceEntity entity = DeviceEntity.builder().edgeGatewayId(edgeGatewayId).build();
        BeanUtilsIntensifier.propertyInjection(tenantIsolation,entity);
        Result<List<DeviceEntity>> result = deviceService.listDevice(entity);
        return R.result(result);
    }

    @PostMapping("")
    @Operation(summary = "创建设备", description = "创建一个设备")
    public R createDevice(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@Parameter(description = "设备对象") @RequestBody DeviceDto device) {
        return checkDeviceParamAndDoSomething(tenantIsolation,device, deviceService::createDevice);
    }

    @PutMapping("")
    @Operation(summary = "更新设备", description = "更新设备")
    public R updateDevice(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@Parameter(description = "设备对象") @RequestBody DeviceDto device) {
        if (BeanUtilsIntensifier.checkBeanAndProperties(device, DeviceDto::getId)) {
            return R.error(ServiceCodeEnum.CODE_PARAM_ERROR);
        }
        return checkDeviceParamAndDoSomething(tenantIsolation,device, deviceService::updateDevice);
    }

    @DeleteMapping("{deviceId}")
    @Operation(summary = "删除设备",
            parameters = {
                    @Parameter(name = "deviceId", description = "设备对象", required = true)
            })
    public R deleteDevice(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@PathVariable Long deviceId) {
        Result result = deviceService.deleteDeviceById(tenantIsolation,deviceId);
        return R.result(result);
    }

    @DeleteMapping("batch/delete")
    @Operation(summary = "按条件批量删除设备")
    public R deviceBatchDelete(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@RequestBody DeviceRequestBo requestBo) {
        Result<List<DeviceRespondBo>> result = deviceService.deviceBatchDelete(tenantIsolation,requestBo.getIds());
        return R.result(result);
    }

    @GetMapping("{deviceId}")
    @Operation(summary = "获取设备", description = "通过ID一个设备",
            parameters = {
                    @Parameter(name = "deviceId", description = "设备ID", required = true)
            })
    public R getDevice(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@PathVariable Long deviceId) {
        DeviceEntity build = DeviceEntity.builder().id(deviceId).build();
        BeanUtilsIntensifier.propertyInjection(tenantIsolation,build);
        Result<DeviceDto> result = deviceService.getDevice(build);
        return R.result(result);
    }

    @GetMapping("property/{deviceId}")
    @Operation(summary = "通过设备获取属性",
    parameters = {
            @Parameter(name = "deviceId", description = "设备ID", required = true)
    })
    public R getDeviceProperties(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@PathVariable Long deviceId) {
        return R.result(deviceService.getDeviceProperties(tenantIsolation.getTenantId(),deviceId));
    }

    @PutMapping("property")
    @Operation(summary = "获取设备事件关联属性")
    public R getDeviceEventProperties(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@RequestBody List<EventVo> eventVos) {
            return R.result(deviceService.getDeviceEventProperties(tenantIsolation.getTenantId(),eventVos));
    }

    @PostMapping({"{deviceId}/do-service/{serviceName}","{deviceId}/do-service/{serviceName}/{instanceId}/{instanceName}"})
    @Operation(summary = "服务调用")
    public R doServiceTask(
            @RequestHeader("ot_headers") TenantIsolation tenantIsolation,
            @PathVariable("deviceId") Long deviceId,
            @PathVariable("serviceName") String serviceName,
            @PathVariable(name = "instanceId",required = false) Long instanceId,
            @PathVariable(name = "instanceName",required = false) String instanceName,
            @RequestBody Map<String, Object> input) {
        DeviceServiceLogEntity logEntity = DeviceServiceLogEntity.builder().createTime(LocalDateTime.now()).deviceId(deviceId).serviceName(serviceName).creatorId(instanceId).creator(instanceName).build();
        if (ObjectUtil.isEmpty(instanceId)) {
            logEntity.setCallType(ThingServiceCallTypeEnum.USER_INVOKE.getValue());
        }else{
            logEntity.setCallType(ThingServiceCallTypeEnum.SCENE_INVOKE.getValue());
        }
        return deviceService.doServiceTask(tenantIsolation.getTenantId(),deviceId, serviceName, input, logEntity,true);
    }

    private R checkDeviceParamAndDoSomething(TenantIsolation tenantIsolation,DeviceDto dto, Function<DeviceDto, Result> func) {
        log.info("创建或更新设备详情，租户：{}，入参：{}",tenantIsolation.getTenantId(), JSON.toJSONString(dto));
        if (BeanUtilsIntensifier.checkBeanAndProperties(tenantIsolation, TenantIsolation::getTenantId,TenantIsolation::getEngineeringId,TenantIsolation::getModuleId,TenantIsolation::getSpaceId)) {
            throw new BizException(ServiceCodeEnum.CODE_PARAM_ERROR);
        }
        if (BeanUtilsIntensifier.checkBeanAndProperties(dto, DeviceDto::getName, DeviceDto::getEdgeGatewayId)) {
            throw new BizException(ServiceCodeEnum.CODE_PARAM_ERROR);
        }
        BeanUtilsIntensifier.propertyInjection(tenantIsolation,dto);
        Result result = func.apply(dto);
        return R.result(result);
    }

    @PostMapping("create-by-label-ids")
    @Operation(summary = "批量标签创建设备模型、设备、并绑定")
    public R createByLabelIds(@RequestHeader("ot_headers") TenantIsolation tenant,@Validated @RequestBody BatchLabelCreateDeviceParam param){
        return R.result(deviceService.createByLabelIds(tenant, param));
    }


    @PostMapping("assemble-by-label-group-ids")
    @Operation(summary = "批量标签组装设备模型、设备、并绑定")
    public R assembleByLabelGroupIds(@RequestHeader("ot_headers") TenantIsolation tenant,@Validated @RequestBody AssembleByLabelGroupIdsDTO dto){
        return R.result(deviceService.assembleByLabelGroupIds(dto,tenant));
    }


    @PostMapping("create-by-assemble-info")
    @Operation(summary = "根据组装信息创建设备")
    public R createByAssembleInfo(@RequestHeader("ot_headers") TenantIsolation tenant, @Validated @RequestBody CreateByAssembleInfoDTO dto){
        return R.result(deviceService.createByAssembleInfo(dto,tenant));
    }

    @GetMapping("fault/data")
    @Operation(summary = "获取租户设备故障统计信息信息")
    public R getDeviceFaultData(@RequestHeader("ot_headers") TenantIsolation tenantIsolation) {
        return R.result(deviceService.getDeviceFaultData(tenantIsolation.getTenantId()));
    }

    @PostMapping("onlineStatus")
    @Operation(summary = "获取设备状态实时信息" )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200",description = "返回值",
                    content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = DeviceOnlineStatusVO.class))})
    })
    public R onlineStatus(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@RequestBody GateWayDeviceListDTO dto){
        return R.ok(deviceService.listDeviceOnlineStatus(dto,tenantIsolation));
    }
    
    @PostMapping("writePropertyValue")
    @Operation(summary = "写属性值" )
    public R writePropertyValue(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@RequestBody WritePropertyValueDTO dto){
        return R.ok(deviceService.writePropertyValue(dto,tenantIsolation));
    }


    @PostMapping("job/execute")
    @Operation(summary = "定时器执行测试" )
    public R jobExecute(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@RequestBody List<DoServiceTaskRequest> doServiceTaskRequests){
        return R.ok(deviceService.jobExecute(doServiceTaskRequests,tenantIsolation));
    }


    @PostMapping("listDeviceByTag")
    @Operation(summary = "根据标签获取设备列表" )
    public R listDeviceByTag(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@RequestBody List<ListTagRequest> requestList){
        return R.ok(deviceService.listDeviceByTag(tenantIsolation.getTenantId(),requestList));
    }


    @PostMapping("batchTags")
    @Operation(summary = "批量打标记", description = "批量打标记")
    public R batchTags(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@Parameter(description = "设备对象") @RequestBody BatchDeviceTagDto batchDeviceTagDto) {
        return R.ok(deviceService.batchTags(tenantIsolation.getTenantId(),batchDeviceTagDto));
    }
    
}
