package com.nti56.nlink.product.device.server.controller;

import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.R;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.client.model.dto.json.ChannelRuntimeInfoField;
import com.nti56.nlink.product.device.client.model.dto.json.GatherParamField;
import com.nti56.nlink.product.device.server.model.ComputeTaskBo;
import com.nti56.nlink.product.device.server.model.GatherInfoBo;
import com.nti56.nlink.product.device.server.service.ITaskService;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;



/**
 * 类说明: 任务controller
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 13:33:05
 * @since JDK 1.8
 */
@Slf4j
@RestController
@RequestMapping("/task")
@Tag(name = "任务模块")
public class TaskController {

    @Autowired
    ITaskService taskService;

    @GetMapping("/gather-task/list-internal")
    @Operation(summary = "查询采集任务",
    parameters = {
            @Parameter(name = "edgeGatewayId", description = "边缘网关id",
                    required = false)
    })
    @Hidden
    public GatherInfoBo listGatherTaskInternal(
        @RequestHeader("ot_headers") TenantIsolation tenant,
        Long edgeGatewayId
    ){
        log.info("listGatherTaskInternal edgeGatewayId: {}, tenant: {}", edgeGatewayId, tenant);
        Result<List<GatherParamField>> result = taskService.listGatherParam(tenant, edgeGatewayId);
        if(!result.getSignal()){
            return null;
        }
        Result<List<ChannelRuntimeInfoField>> result2 = taskService.listChannelRuntimeInfo(tenant, edgeGatewayId);
        if(!result2.getSignal()){
            return null;
        }
        GatherInfoBo info = new GatherInfoBo();

        List<GatherParamField> gatherParamList = result.getResult();
        info.setGatherParamList(gatherParamList);
        List<ChannelRuntimeInfoField> channelRuntimeInfoList = result2.getResult();
        info.setChannelRuntimeInfoList(channelRuntimeInfoList);

        return info;
    }

    @GetMapping("/compute-task/list-internal")
    @Operation(summary = "查询计算任务" ,
    parameters = {
            @Parameter(name = "edgeGatewayId", description = "边缘网关id",
                    required = false)
    })
    @Hidden
    public List<ComputeTaskBo> listComputeTaskInternal(
        @RequestHeader("ot_headers") TenantIsolation tenant,
        Long edgeGatewayId
    ){
        log.info("listComputeTaskInternal edgeGatewayId: {}, tenant: {}", edgeGatewayId, tenant);
        Result<List<ComputeTaskBo>> result = taskService.listComputeTask(tenant, edgeGatewayId);
        if(!result.getSignal()){
            return null;
        }
        List<ComputeTaskBo> records = result.getResult();
        return records;
    }

    @GetMapping("/sync-task")
    @Operation(summary = "同步所有任务")
    public R syncAllTask(@RequestHeader("ot_headers") TenantIsolation tenant){
        log.info("syncAllTask edgeGatewayId: {}, tenant: {}", tenant);
        Result<Void> result = taskService.syncAllTask(tenant);
        if(!result.getSignal()){
            return null;
        }
        return R.ok();
    }
    
    @GetMapping("/sync-task/{edgeGatewayId}")
    @Operation(summary = "同步任务" ,
    parameters = {
            @Parameter(name = "edgeGatewayId", description = "边缘网关id",
                    required = false)
    })
    public R syncTask(@RequestHeader("ot_headers") TenantIsolation tenant, 
                    @PathVariable Long edgeGatewayId
    ){
        log.info("syncTask edgeGatewayId: {}, tenant: {}", edgeGatewayId, tenant);
        Result<Void> result = taskService.syncTask(tenant, edgeGatewayId);
        if(!result.getSignal()){
            return null;
        }
        return R.ok();
    }

    
}

