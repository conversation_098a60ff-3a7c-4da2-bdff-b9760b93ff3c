package com.nti56.nlink.product.device.server.domain.thing.thingmodel;

import cn.hutool.core.collection.CollectionUtil;

import com.alibaba.fastjson.JSONObject;
import com.nti56.nlink.common.fetcher.CommonFetcher;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.client.model.dto.json.ModelField;
import com.nti56.nlink.product.device.server.constant.GlobalVariables;
import com.nti56.nlink.product.device.server.domain.thing.dpo.ModelDpo;
import com.nti56.nlink.product.device.server.domain.thing.modelbase.Event;
import com.nti56.nlink.product.device.server.domain.thing.modelbase.Property;
import com.nti56.nlink.product.device.server.domain.thing.modelbase.Service;
import com.nti56.nlink.product.device.server.domain.thing.modelbase.Subscription;
import com.nti56.nlink.product.device.server.entity.SubscriptionEntity;
import com.nti56.nlink.product.device.server.entity.ThingModelEntity;
import com.nti56.nlink.product.device.server.entity.ThingModelInheritEntity;
import com.nti56.nlink.product.device.server.entity.ThingServiceEntity;
import com.nti56.nlink.product.device.server.model.device.dto.DeviceCheckInfoContext;
import com.nti56.nlink.product.device.server.model.inherit.*;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.codec.digest.DigestUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 类说明: 物模型继承领域对象
 * 
 * 融合继承的多个模型为1个
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-04-15 09:55:16
 * @since JDK 1.8
 */
@Getter
@Slf4j
public class ThingModelInherit {
    
    private List<ThingModel> thingModelList;
    
    public ThingModelOfInherit getInheritPartModel() {
        ThingModelOfInherit model = new ThingModelOfInherit();

        List<Property> properties = getProperties();
        if(properties != null && properties.size() >0){
            List<PropertyOfInherit> list = properties.stream().map(Property::toPropertyOfInherit).collect(Collectors.toList());
            model.setProperties(list);
        }

        List<Event> events = getEvents();
        if(events != null && events.size() >0){
            List<EventOfInherit> list = events.stream().map(Event::toEventOfInherit).collect(Collectors.toList());
            model.setEvents(list);
        }

        List<Service> services = getServices();
        if(services != null && services.size() >0){
            List<ServiceOfInherit> list = services.stream().map(Service::toServiceOfInherit).collect(Collectors.toList());
            model.setServices(list);
        }

        List<Subscription> subscriptions = getSubscriptions();
        if(subscriptions != null && subscriptions.size() >0){
            List<SubscriptionOfInherit> list = subscriptions.stream().map(Subscription::toSubscriptionOfInherit).collect(Collectors.toList());
            model.setSubscriptions(list);
        }

        return model;
    }

    public List<Long> listAllThingModelId() {
        if(thingModelList == null || thingModelList.size() <= 0){
            return null; 
        }
        List<Long> list = new ArrayList<>();
        for(ThingModel item:thingModelList){
            list.addAll(item.listAllThingModelId());
        }
        return list;
    }

    public Set<ThingModelInheritEntity> listAllThingModelInheritEntity(){
        if (CollectionUtil.isEmpty(thingModelList)) {
            return new HashSet<>();
        }
        Set<ThingModelInheritEntity> set = new HashSet<>();
        thingModelList.forEach(thingModel -> set.addAll(thingModel.listAllThingModelInheritEntity()));
        return set;
    }

    public List<Property> getProperties() {
        if(thingModelList == null || thingModelList.size() <= 0){
            return null; 
        }
        List<Property> properties = new ArrayList<>();
        for(ThingModel thingModel:thingModelList){
            List<Property> p = thingModel.getProperties();
            if(p != null && p.size() > 0){
                properties.addAll(p);
            }
        }
        return properties;
    }

    public List<Event> getEvents() {
        if(thingModelList == null || thingModelList.size() <= 0){
            return null; 
        }
        List<Event> events = new ArrayList<>();
        for(ThingModel thingModel:thingModelList){
            List<Event> e = thingModel.getEvents();
            if(e != null && e.size() > 0){
                events.addAll(e);
            }
        }
        return events;
    }

    public List<Service> getServices() {
        if(thingModelList == null || thingModelList.size() <= 0){
            return null; 
        }
        List<Service> services = new ArrayList<>();
        for(ThingModel thingModel:thingModelList){
            List<Service> s = thingModel.getServices();
            if(s != null && s.size() > 0){
                services.addAll(s);
            }
        }
        return services;
    }

    public List<Subscription> getSubscriptions() {
        if(thingModelList == null || thingModelList.size() <= 0){
            return null;
        }
        List<Subscription> subscriptions = new ArrayList<>();
        for(ThingModel thingModel:thingModelList){
            List<Subscription> s = thingModel.getSubscriptions();
            if(s != null && s.size() > 0){
                subscriptions.addAll(s);
            }
        }
        return subscriptions;
    }

    public static Result<ThingModelInherit> checkInfo(
        List<Long> inheritThingModelIdList,
        CommonFetcher commonFetcher
    ){
        return checkInfo(
            inheritThingModelIdList,
            commonFetcher,
            ThingModel.THING_MODEL_ID_PATH_SPLITER
        );
    }

    public static Result<ThingModelInherit> checkInfo(
        List<Long> inheritThingModelIdList,
        CommonFetcher commonFetcher,
        String thingModelIdPath
    ){
        ThingModelInherit inherit = new ThingModelInherit();

        if(inheritThingModelIdList == null || inheritThingModelIdList.size() <= 0){
            return Result.ok(inherit);
        }

        Set<Long> thingModelIdSet = new HashSet<>();
        List<ThingModel> thingModelList = new ArrayList<>();
        for(Long thingModelId:inheritThingModelIdList){
            if(thingModelId == null){
                return Result.error("继承物模型id不能为空");
            }
            if(thingModelIdSet.contains(thingModelId)){
                return Result.error("同层级不允许重复继承同一个物模型, inheritThingModelId:" + thingModelId);
            }
            thingModelIdSet.add(thingModelId);
            ThingModelEntity thingModelEntity = commonFetcher.get(thingModelId, ThingModelEntity.class);
            if(thingModelEntity == null && thingModelId == -1L){
                thingModelEntity = GlobalVariables.getDefaultCommonType();
            }
            if (thingModelEntity == null) {
                return Result.error("找不到继承物模型");
            }
            //不允许循环继承
            String[] split = thingModelIdPath.split(ThingModel.THING_MODEL_ID_PATH_SPLITER);
            Set<String> splitSet = new HashSet<>(Arrays.asList(split));
            if(splitSet.contains(thingModelId.toString())){
                return Result.error("物模型不能循环继承, inheritThingModelId:" + thingModelId);
            }
            Result<ThingModel> thingModelResult = ThingModel.checkInfo(
                thingModelEntity, 
                commonFetcher,
                thingModelIdPath
            );
            if(!thingModelResult.getSignal()){
                return Result.error(thingModelResult.getMessage() + ", inheritThingModelId:" + thingModelId);
            }
            thingModelList.add(thingModelResult.getResult());
        }

        inherit.thingModelList = thingModelList;
        //检查属性重复
        List<Property> inheritProperties = inherit.getProperties();
        Result<List<Property>> propertiesResult = Property.checkRepeat(
            inheritProperties, null
        );
        if(!propertiesResult.getSignal()){
            return Result.error(propertiesResult.getMessage() + ", thingModelIdPath:" + thingModelIdPath);
        }
        //检查事件重复
        List<Event> inheritEvents = inherit.getEvents();
        Result<List<Event>> eventsResult = Event.checkRepeat(
            inheritEvents, null
        );
        if(!eventsResult.getSignal()){
            return Result.error(eventsResult.getMessage() + ", thingModelIdPath:" + thingModelIdPath);
        }
        //检查继承的物模型中是否服务重名
        List<Service> inheritServices = inherit.getServices();
        Result<List<Service>> servicesNameCheckResult = Service.checkRepeat(inheritServices);
        if(!servicesNameCheckResult.getSignal()){
            return Result.error(servicesNameCheckResult.getMessage() + ", thingModelIdPath:" + thingModelIdPath);
        }
        //检查自己的物模型中是否订阅重名
        Result<List<Subscription>> subscriptionResult = Subscription.checkRepeat(inherit.getSubscriptions(),null);
        if(!subscriptionResult.getSignal()){
            return Result.error(subscriptionResult.getMessage() + ", thingModelIdPath:" + thingModelIdPath);
        }
        return Result.ok(inherit);
    }

     public static Result<ThingModelInherit> checkInfo2(
        List<ThingModelEntity> inheritThingModelList,
        DeviceCheckInfoContext context,
        String thingModelIdPath
    ){
        ThingModelInherit inherit = new ThingModelInherit();

        if(inheritThingModelList == null || inheritThingModelList.size() <= 0){
            return Result.ok(inherit);
        }

        Set<Long> thingModelIdSet = new HashSet<>();
        List<ThingModel> thingModelList = new ArrayList<>();
        for(ThingModelEntity thingModel:inheritThingModelList){
            if(thingModelIdSet.contains(thingModel.getId())){
                return Result.error("同层级不允许重复继承同一个物模型, inheritThingModelId:" + thingModel.getId());
            }
            thingModelIdSet.add(thingModel.getId());
            
            if(thingModel == null && thingModel.getId() == -1L){
                thingModel = GlobalVariables.getDefaultCommonType();
            }
            if (thingModel == null) {
                return Result.error("找不到继承物模型");
            }
            //不允许循环继承
            String[] split = thingModelIdPath.split(ThingModel.THING_MODEL_ID_PATH_SPLITER);
            Set<String> splitSet = new HashSet<>(Arrays.asList(split));
            if(splitSet.contains(thingModel.getId().toString())){
                return Result.error("物模型不能循环继承, inheritThingModelId:" + thingModel.getId());
            }
            Result<ThingModel> thingModelResult = ThingModel.checkInfo2(
                thingModel, 
                context,
                thingModelIdPath
            );
            if(!thingModelResult.getSignal()){
                return Result.error(thingModelResult.getMessage() + ", inheritThingModelId:" + thingModel.getId());
            }
            thingModelList.add(thingModelResult.getResult());
        }

        inherit.thingModelList = thingModelList;
        //检查属性重复
        List<Property> inheritProperties = inherit.getProperties();
        Result<List<Property>> propertiesResult = Property.checkRepeat(
            inheritProperties, null
        );
        if(!propertiesResult.getSignal()){
            return Result.error(propertiesResult.getMessage() + ", thingModelIdPath:" + thingModelIdPath);
        }
        //检查事件重复
        List<Event> inheritEvents = inherit.getEvents();
        Result<List<Event>> eventsResult = Event.checkRepeat(
            inheritEvents, null
        );
        if(!eventsResult.getSignal()){
            return Result.error(eventsResult.getMessage() + ", thingModelIdPath:" + thingModelIdPath);
        }
        //检查继承的物模型中是否服务重名
        List<Service> inheritServices = inherit.getServices();
        Result<List<Service>> servicesNameCheckResult = Service.checkRepeat(inheritServices);
        if(!servicesNameCheckResult.getSignal()){
            return Result.error(servicesNameCheckResult.getMessage() + ", thingModelIdPath:" + thingModelIdPath);
        }
        //检查自己的物模型中是否订阅重名
        Result<List<Subscription>> subscriptionResult = Subscription.checkRepeat(inherit.getSubscriptions(),null);
        if(!subscriptionResult.getSignal()){
            return Result.error(subscriptionResult.getMessage() + ", thingModelIdPath:" + thingModelIdPath);
        }
        return Result.ok(inherit);
    }

    /**
     * @return 如果不同返回 true
     */
    public static boolean checkInheritDiff(Set<Long> oldSet, Set<Long> newSet){
        if(oldSet == null || oldSet.size() <= 0){
            if(newSet == null || newSet.size() <= 0){
                return false;
            }
            return true;
        }
        if(newSet == null || newSet.size() <= 0){
            return true;
        }
        if(newSet.containsAll(oldSet) && oldSet.containsAll(newSet)){
            return false;
        }
        return true;
    }

    /**
     * @return 不同返回true
     */
    public static Boolean checkModelDiff(ModelField oldModel, ModelField newModel) {
        if(oldModel == null){
            if(newModel == null){
                return false;
            }
            return true;
        }
        if(newModel == null){
            return true;
        }
        String oldMd5 = DigestUtils.md5Hex(JSONObject.toJSONString(oldModel));
        String newMd5 = DigestUtils.md5Hex(JSONObject.toJSONString(newModel));
        return !oldMd5.equals(newMd5);
    }

    public List<ModelDpo> toDpo(){
        List<ModelDpo> list = new ArrayList<>();
        if (thingModelList != null && thingModelList.size() > 0) {
            for (ThingModel thingModel : thingModelList) {
                ModelDpo dpo = thingModel.toDpo();
                list.add(dpo);
            }
        }
        return list;
    }

    public Set<ThingModelEntity> listAllThingModelEntity() {
        Set<ThingModelEntity> set = new HashSet<>();
        if(thingModelList == null || thingModelList.size() <= 0){
            return set;
        }
        for(ThingModel item:thingModelList){
            set.addAll(item.listAllThingModelEntity());
        }
        return set;
    }

    public static Result<ThingModelInherit> checkInfoNew(
            List<Long> inheritThingModelIdList,
            Map<Long,ThingModelEntity> thingModelEntityMap,//根据继承modelId获取到的map
            Map<Long,List<ThingModelInheritEntity>> thingModelInheritEntityMap, //根据继承modelId获取到的map
            Map<Long,List<ThingServiceEntity>> thingServiceEntityMap,   //根据继承modelId获取到的map
            Map<Long,List<SubscriptionEntity>> subscriptionEntityMap //根据继承modelId获取到的map
    ){
        return checkInfoNew(
                inheritThingModelIdList,
                ThingModel.THING_MODEL_ID_PATH_SPLITER,
                thingModelEntityMap,
                thingModelInheritEntityMap, //根据继承modelId获取到的map
                thingServiceEntityMap,   //根据继承modelId获取到的map
                subscriptionEntityMap
        );
    }

    public static Result<ThingModelInherit> checkInfoNew(
            List<Long> inheritThingModelIdList,
            String thingModelIdPath,
            Map<Long,ThingModelEntity> thingModelEntityMap,
            Map<Long,List<ThingModelInheritEntity>> thingModelInheritEntityMap, //根据继承modelId获取到的map
            Map<Long,List<ThingServiceEntity>> thingServiceEntityMap,   //根据继承modelId获取到的map
            Map<Long,List<SubscriptionEntity>> subscriptionEntityMap
    ){
        ThingModelInherit inherit = new ThingModelInherit();

        if(inheritThingModelIdList == null || inheritThingModelIdList.size() <= 0){
            return Result.ok(inherit);
        }

        Set<Long> thingModelIdSet = new HashSet<>();
        List<ThingModel> thingModelList = new ArrayList<>();
        for(Long thingModelId:inheritThingModelIdList){
            if(thingModelId == null){
                return Result.error("继承物模型id不能为空");
            }
            if(thingModelIdSet.contains(thingModelId)){
                return Result.error("同层级不允许重复继承同一个物模型, inheritThingModelId:" + thingModelId);
            }
            thingModelIdSet.add(thingModelId);

            ThingModelEntity thingModelEntity = thingModelEntityMap.get(thingModelId);
            if(thingModelEntity == null){
                return Result.error("找不到继承物模型");
            }

            //不允许循环继承
            String[] split = thingModelIdPath.split(ThingModel.THING_MODEL_ID_PATH_SPLITER);
            Set<String> splitSet = new HashSet<>(Arrays.asList(split));
            if(splitSet.contains(thingModelId.toString())){
                return Result.error("物模型不能循环继承, inheritThingModelId:" + thingModelId);
            }

            Result<ThingModel> thingModelResult = ThingModel.checkInfoNew(
                    thingModelEntity,
                    thingModelIdPath,
                    thingModelEntityMap,
                    thingModelInheritEntityMap,
                    thingServiceEntityMap,
                    subscriptionEntityMap
            );
            if(!thingModelResult.getSignal()){
                return Result.error(thingModelResult.getMessage() + ", inheritThingModelId:" + thingModelId);
            }
            thingModelList.add(thingModelResult.getResult());
        }

        inherit.thingModelList = thingModelList;

        //检查属性重复
        List<Property> inheritProperties = inherit.getProperties();
        Result<List<Property>> propertiesResult = Property.checkRepeat(
                inheritProperties, null
        );
        if(!propertiesResult.getSignal()){
            return Result.error(propertiesResult.getMessage() + ", thingModelIdPath:" + thingModelIdPath);
        }

        //检查事件重复
        List<Event> inheritEvents = inherit.getEvents();
        Result<List<Event>> eventsResult = Event.checkRepeat(
                inheritEvents, null
        );
        if(!eventsResult.getSignal()){
            return Result.error(eventsResult.getMessage() + ", thingModelIdPath:" + thingModelIdPath);
        }

        //检查继承的物模型中是否服务重名
        List<Service> inheritServices = inherit.getServices();
        Result<List<Service>> servicesNameCheckResult = Service.checkRepeat(inheritServices);
        if(!servicesNameCheckResult.getSignal()){
            return Result.error(servicesNameCheckResult.getMessage() + ", thingModelIdPath:" + thingModelIdPath);
        }

        //检查自己的物模型中是否订阅重名
        Result<List<Subscription>> subscriptionResult = Subscription.checkRepeat(inherit.getSubscriptions(),null);
        if(!subscriptionResult.getSignal()){
            return Result.error(subscriptionResult.getMessage() + ", thingModelIdPath:" + thingModelIdPath);
        }

        return Result.ok(inherit);
    }
}
