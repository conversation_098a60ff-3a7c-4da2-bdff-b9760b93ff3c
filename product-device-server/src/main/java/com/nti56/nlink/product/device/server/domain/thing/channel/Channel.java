package com.nti56.nlink.product.device.server.domain.thing.channel;

import cn.hutool.core.collection.CollectionUtil;

import cn.hutool.core.util.ObjectUtil;
import com.nti56.nlink.common.base.FieldValue;
import com.nti56.nlink.common.base.UniqueConstraint.Unique;
import com.nti56.nlink.common.fetcher.CommonFetcher;
import com.nti56.nlink.common.fetcher.Preloader;
import com.nti56.nlink.common.util.BeanUtilsIntensifier;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.nlink.product.device.client.model.dto.json.ChannelRuntimeInfoField;
import com.nti56.nlink.product.device.client.model.dto.json.device.AccessElm;
import com.nti56.nlink.product.device.client.model.dto.json.device.ChannelElm;
import com.nti56.nlink.product.device.server.domain.thing.custom.CustomDriver;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.ChannelStatusEnum;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.DriverEnum;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.ModelTypeEnum;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.SubscriptionEventTypeEnum;
import com.nti56.nlink.product.device.server.domain.thing.label.Label;
import com.nti56.nlink.product.device.server.domain.thing.label.LabelGroup;
import com.nti56.nlink.product.device.server.domain.thing.modelbase.Subscription;
import com.nti56.nlink.product.device.server.entity.*;
import com.nti56.nlink.product.device.server.model.channel.vo.ChannelDebugInfoVo;
import com.nti56.nlink.product.device.server.model.channel.vo.ChannelParamVO;
import com.nti56.nlink.product.device.server.model.channel.vo.QueryChannelByIdVO;
import com.nti56.nlink.product.device.server.model.custom.CustomDriverBo;

import lombok.Getter;
import org.apache.commons.collections.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;

/**
 * 类说明: 通道领域对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-05-09 15:57:26
 * @since JDK 1.8
 */
@Getter
public class Channel {
    
    private Long id;

    private String name;

    private String descript;

    private ChannelStatusEnum status;

    private Long edgeGatewayId;

    private DriverEnum driver;

    private String customDriverName;

    private Boolean isServer;

    private ChannelParam channelParam;

    private List<LabelGroup> labelGroups;

    private Integer intervalMs;

    private LocalDateTime createTime;
    
    private List<Subscription> subscriptions;
    
    public static Result<Channel> checkInfo(
        ChannelEntity entity,
        CommonFetcher commonFetcher
    ){
        
        if(entity == null){
            return Result.error("通道不能为空");
        }

        if(entity.getId() == null){
            return Result.error("通道id不能为空");
        }

        if(entity.getEdgeGatewayId() == null){
            return Result.error("通道所属网关不能为空");
        }

        Channel channel = new Channel();
        channel.id = entity.getId();
        channel.name = entity.getName();
        channel.edgeGatewayId = entity.getEdgeGatewayId();
        channel.intervalMs = entity.getIntervalMs();
        channel.descript = entity.getDescript();
        channel.createTime = entity.getCreateTime();

        Integer statusInt = entity.getStatus();
        ChannelStatusEnum status = ChannelStatusEnum.typeOfValue(statusInt);
        channel.status = status;

        DriverEnum driver = DriverEnum.typeOfValue(entity.getDriver());
        if(driver == null){
            return Result.error(
                "分组的通道驱动类型暂不支持，channelId:" + entity.getId()
                        + ", driver:" + entity.getDriver()
            );
        }
        channel.driver = driver;

        //自定义协议
        if(DriverEnum.CUSTOM.equals(driver)){
            String customDriverName = entity.getCustomDriverName();
            if(customDriverName == null || "".equals(customDriverName)){
                return Result.error("自定义协议需要选择协议");
            }
            channel.customDriverName = customDriverName;

            Boolean isServer = entity.getIsServer();
            if(isServer == null){
                isServer = false;
            }
            channel.isServer = isServer;
        }

        List<ChannelParamEntity> channelParamList = commonFetcher.list("channel_id", entity.getId(), ChannelParamEntity.class);
        
        Result<ChannelParam> paramResult = ChannelParam.checkParam(driver, channel.isServer, channelParamList);
        if (!paramResult.getSignal()){
            return Result.error(paramResult.getMessage());
        }
        channel.channelParam = paramResult.getResult();
    
        //自己的订阅
        List<SubscriptionEntity> subscriptionEntities = commonFetcher.preloader("directly_model_id", entity.getEdgeGatewayId(), SubscriptionEntity.class)
            .list().stream().filter(s -> s.getModelType() == ModelTypeEnum.CHANNEL_MODEL.getValue() && s.getFromId().contains(String.valueOf(entity.getId()))).collect(Collectors.toList());

        Result<List<Subscription>> selfSubscriptionsResult = Subscription.batchCheckInfo(entity.getId(),entity.getName(),subscriptionEntities);
        if(!selfSubscriptionsResult.getSignal()){
            return Result.error(selfSubscriptionsResult.getMessage() + ", thingModelId:" + entity.getId());
        }
        List<Subscription> selfSubscriptions = selfSubscriptionsResult.getResult();
    
        //检查自己的物模型中是否订阅重名
        Result<List<Subscription>> subscriptionResult = Subscription.checkRepeat(null,selfSubscriptions);
        if(!subscriptionResult.getSignal()){
            return Result.error(subscriptionResult.getMessage() + ", 模型ID:" + entity.getId());
        }
        channel.subscriptions = subscriptionResult.getResult();
        
        return Result.ok(channel);
    }


    public Result<ChannelDebugInfoVo> checkDebugInfo(CommonFetcher commonFetcher) {
        ChannelDebugInfoVo info = new ChannelDebugInfoVo();
        info.setId(id);
        info.setName(name);
        info.setStatus(status.getValue());
        info.setDescript(descript);
        info.setDriver(driver.getValue());
        info.setDriverName(driver.getName());
        info.setEdgeGatewayId(edgeGatewayId);
        if(DriverEnum.CUSTOM.equals(driver)){
            CustomChannelParam customChannelParam = (CustomChannelParam)channelParam;
            info.setIp(customChannelParam.getIp());
            info.setPort(customChannelParam.getPort());
            info.setMode(customChannelParam.getMode().getName());

            Unique unique = CustomDriver.driverNameUniqueConstraint.buildUnique(
                new FieldValue(customDriverName)
            );
            CustomDriverEntity customDriverEntity = commonFetcher.get(unique, CustomDriverEntity.class);
            Result<CustomDriver> customDriverResult = CustomDriver.checkInfoToMessage(customDriverEntity, commonFetcher);
            if(!customDriverResult.getSignal()){
                return Result.error(customDriverResult.getMessage());
            }
            CustomDriver customDriver = customDriverResult.getResult();
            CustomDriverBo bo = new CustomDriverBo();
            bo.setDriverEntity(customDriver.toEntity());
            bo.setMessageEntityList(customDriver.toMessageEntityList());
            info.setCustomDriver(bo);
        }

        return Result.ok(info);
    }


    public Result<Void> checkLabelInfo( CommonFetcher commonFetcher){
        boolean haveOneLabelBinding = false;
        StringBuilder labelErrorMsg = new StringBuilder();
        HashSet<String> labelErrorMsgSet = new HashSet<>();

        List<LabelGroupEntity> labelGroupList = commonFetcher.list("channel_id", id, LabelGroupEntity.class);
        for (LabelGroupEntity labelGroup : labelGroupList) {
            List<LabelEntity> labelList = commonFetcher.list("label_group_id", labelGroup.getId(), LabelEntity.class);
            if (CollectionUtils.isNotEmpty(labelList)){
                for (LabelEntity label : labelList) {
                    Result<Label> labelResult = Label.checkInfoToChannel(label, commonFetcher);
                    if (!labelResult.getSignal() && labelErrorMsgSet.add(labelResult.getMessage())){
                        labelErrorMsg.append(labelResult.getMessage()).append(";\n");
                    }
                    if (!haveOneLabelBinding) {
                        List<LabelBindRelationEntity> labelBindRelationList = commonFetcher.list("label_id", label.getId(), LabelBindRelationEntity.class);
                        if (CollectionUtils.isNotEmpty(labelBindRelationList)) {
                            haveOneLabelBinding = true;
                        }
                    }
                }
            }

        }

        if (labelErrorMsg.length() > 0){
            return Result.error(labelErrorMsg.toString());
        }

        if (!haveOneLabelBinding){
            return Result.error(ServiceCodeEnum.CHANNEL_NO_LABEL_BINDING.getCode(),"通道【"+name+"】的所有标签都未绑定属性");
        }

        return Result.ok();
    }


    public ChannelRuntimeInfoField createRuntimeInfo() {
        ChannelRuntimeInfoField info = new ChannelRuntimeInfoField();
        info.setChannelId(id);
        info.setDriver(driver.getName());
        info.setCustomDriverName(customDriverName);
        info.setIsServer(isServer);
        channelParam.processRuntimeInfo(info);
        return info;
    }

    public static List<AccessElm> accessElmList(ChannelEntity entity, CommonFetcher commonFetcher){
        List<AccessElm> accessElmList = new ArrayList<>();
        List<LabelGroupEntity> labelGroupEntity = commonFetcher.list("channel_id",entity.getId(), LabelGroupEntity.class);
        List<Long> collect = labelGroupEntity.stream().map(LabelGroupEntity::getId).collect(Collectors.toList());
        List<LabelEntity> labelList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(collect)) {
            Preloader<LabelEntity> preloader = commonFetcher.preloader(LabelEntity.class).preload("label_group_id", collect);
            List<LabelEntity> list = preloader.list();
            labelList.addAll(list);
        }

        if (!labelList.isEmpty()) {
            Label.label2Access(accessElmList,labelList,entity.getId());
        }
        return accessElmList;
    }

    public static List<ChannelElm> getUsefullyChannel(List<ChannelEntity> channelEntities, CommonFetcher commonFetcher){
        List<ChannelElm> channelElms = new ArrayList<>();
        Optional.ofNullable(channelEntities).orElse(new ArrayList<>()).forEach(channelEntity -> {
            ChannelElm channelElm = getUsefullyChannel(channelEntity, commonFetcher);
            if (Optional.ofNullable(channelElm).isPresent()) {
                channelElms.add(channelElm);
            }
        });
        return channelElms;
    }

    public static ChannelElm getUsefullyChannel(ChannelEntity channelEntity,CommonFetcher commonFetcher){
        Result<Channel> channelResult = checkInfo(channelEntity, commonFetcher);
        if (channelResult.getSignal()) {
            return BeanUtilsIntensifier.copyBean(channelResult.getResult().createRuntimeInfo(), ChannelElm.class);
        }
        return null;
    }

    public static Channel getWithFlatLabelGroup(ChannelEntity entity, CommonFetcher commonFetcher){
        Channel channel = new Channel();
        channel.id = entity.getId();
        channel.name = entity.getName();
        List<LabelGroup> labelGroups = new ArrayList<>();
        channel.labelGroups = labelGroups;
        List<LabelGroupEntity> labelGroupEntities = commonFetcher.list("channel_id", entity.getId(), LabelGroupEntity.class);
        Optional.ofNullable(labelGroupEntities).orElse(new ArrayList<>()).stream()
                .sorted(Comparator.comparing(LabelGroupEntity::getName))
                .forEach(labelGroupEntity -> {
            labelGroups.add(LabelGroup.getFlatBean(labelGroupEntity));
        });
        return channel;
    }

    public static List<LabelGroup> getWithLabelGroup(Long id, CommonFetcher commonFetcher, String search){
        List<LabelGroupEntity> labelGroupEntities = commonFetcher.list("channel_id", id, LabelGroupEntity.class);
        labelGroupEntities.sort(Comparator.comparing(LabelGroupEntity::getName));
        return LabelGroup.getBeans(labelGroupEntities, commonFetcher,search);
    }


    public QueryChannelByIdVO toVo() {
        QueryChannelByIdVO vo = new QueryChannelByIdVO();
        vo.setId(id);
        vo.setName(name);
        if(status != null){
            vo.setStatus(status.getValue());
        }
        vo.setCustomDriverName(customDriverName);
        vo.setIsServer(isServer);
        vo.setDriver(driver.getValue());
        vo.setIntervalMs(intervalMs);
        vo.setDriverName(driver.getName());
        vo.setDescript(descript);
        vo.setCreateTime(createTime);

        List<ChannelParamVO> channelParamList = channelParam.toVoList();
        vo.setChannelParamList(channelParamList);

        return vo;
    }

}
