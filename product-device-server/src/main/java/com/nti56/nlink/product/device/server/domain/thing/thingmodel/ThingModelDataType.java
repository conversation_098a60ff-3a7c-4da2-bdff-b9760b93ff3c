package com.nti56.nlink.product.device.server.domain.thing.thingmodel;

import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.client.model.dto.json.enumerate.ThingDataTypeEnum;
import com.nti56.nlink.product.device.client.model.dto.json.modelfield.DataTypeElm;
import com.nti56.nlink.product.device.client.model.dto.json.modelfield.SpecElm;
import com.nti56.nlink.product.device.server.domain.thing.dpo.ThingModelDataTypeDpo;
import lombok.Data;

import java.util.Optional;

/**
 * 类说明: 属性类型领域对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-24 17:34:20
 * @since JDK 1.8
 */
@Data
public class ThingModelDataType {

    private ThingDataTypeEnum type;
    private Boolean isArray;
    private SpecElm spec;
    
    private DataTypeElm raw;

    public static Result<ThingModelDataType> checkInfo(
        DataTypeElm dataTypeElm
    ) {
        
        if(dataTypeElm == null){
            return Result.error("属性类型不能为空");
        }

        //dataType
        String typeStr = dataTypeElm.getType();
        if(typeStr == null){
            return Result.error("属性缺少数据类型");
        }
        ThingDataTypeEnum typeEnum = ThingDataTypeEnum.typeOfName(typeStr);
        if(typeEnum == null){
            return Result.error(
                "属性数据类型错误，dataType: " + typeStr
            );
        }
        if(ThingDataTypeEnum.DATA_MODEL.equals(typeEnum)){
            return Result.error(
                "属性不支持数据模型类型，dataType: " + typeStr
            );
        }
        ThingModelDataType dataType = new ThingModelDataType();
        dataType.setType(typeEnum);

        //isArray
        Boolean isArrayBool = dataTypeElm.getIsArray();
        if(isArrayBool == null){
            return Result.error("属性类型缺少是否数组");
        }
        dataType.setIsArray(isArrayBool);

        dataType.spec = dataTypeElm.getSpec();

        dataType.raw = dataTypeElm;
        return Result.ok(dataType);
    }

    public static ThingModelDataType buildByDpo(ThingModelDataTypeDpo dpo) {
        ThingModelDataType thingModelDataType = new ThingModelDataType();
        thingModelDataType.isArray = dpo.getIsArray();
        thingModelDataType.type = ThingDataTypeEnum.typeOfName(dpo.getType());
        return thingModelDataType;
    }

    @Override
    public boolean equals(Object obj){
        if (!Optional.ofNullable(obj).isPresent()) {
            return false;
        }
        if (obj instanceof ThingModelDataType) {
            ThingModelDataType thingModelDataType = (ThingModelDataType) obj;
            if (this.type.equals(thingModelDataType.type) && this.isArray.equals(thingModelDataType.isArray)) {
                return true;
            }
            return false;
        }
        return false;
    }

    public ThingModelDataTypeDpo toDpo() {
        ThingModelDataTypeDpo dpo = new ThingModelDataTypeDpo();
        dpo.setType(type.getName());
        dpo.setIsArray(isArray);
        dpo.setSpec(spec);
        return dpo;
    }
}
