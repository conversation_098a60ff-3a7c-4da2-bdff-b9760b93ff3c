package com.nti56.nlink.product.device.server.controller;

import cn.hutool.core.util.ObjectUtil;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Sets;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.*;
import com.nti56.nlink.product.device.server.model.DeviceDebugDto;
import com.nti56.nlink.product.device.server.model.DeviceNamesToIdsDto;
import com.nti56.nlink.product.device.server.model.EventDebugResult;
import com.nti56.nlink.product.device.server.model.PropertyValueWithTime;
import com.nti56.nlink.product.device.server.model.deviceLog.DeviceStateVo;
import com.nti56.nlink.product.device.server.model.product.vo.DeviceVO;
import com.nti56.nlink.product.device.server.service.IDeviceDebugService;
import com.nti56.nlink.product.device.server.service.IDeviceService;
import com.nti56.nlink.product.device.server.verticle.MqttLabelConsumerVerticle;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.vertx.core.Vertx;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;

/**
 * 类说明: 设备controller
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 13:32:04
 * @since JDK 1.8
 */
@RestController
@RequestMapping("device/debug")
@Tag(name = "设备调试模块")
@Slf4j
public class DeviceDebugController {

    @Autowired
    IDeviceService deviceService;

    @Autowired
    IDeviceDebugService deviceDebugService;

    @Autowired
    Vertx vertx;

    @PutMapping("{deviceId}/{eventName}/getRuntimeData")
    @Operation(summary = "获取属性数据")
    public R getRuntimeData(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, @PathVariable("deviceId") Long deviceId, @PathVariable("eventName") String eventName) {
        log.info("获取设备事件触发条件属性值,设备：{}，事件：{}，租户：{}", deviceId, eventName, tenantIsolation);
        return R.result(deviceDebugService.getRuntimeData(tenantIsolation.getTenantId(),deviceId, eventName));
    }

    @PostMapping("{deviceId}/{eventName}")
    @Operation(summary = "事件调试")
    public R eventDebug(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, @PathVariable("deviceId") Long deviceId, @PathVariable("eventName") String eventName,@Parameter(name = "input") @RequestBody Map<String,Object> input) {
        log.info("设备事件调试,设备：{}，事件：{}，租户：{}", deviceId, eventName, tenantIsolation);
        Result<EventDebugResult> eventDebugResultResult = deviceDebugService.eventDebug(tenantIsolation.getTenantId(), deviceId, eventName, input);
        return R.result(eventDebugResultResult);
    }

    @GetMapping("{deviceId}")
    @Operation(summary = "获取运行时")
    public R getActual(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, @PathVariable("deviceId") Long deviceId) {
        log.info("设备事件调试,设备：{}，租户：{}", deviceId, tenantIsolation);
        Result<DeviceVO> deviceDtoResult = deviceDebugService.getActual(tenantIsolation.getTenantId(), deviceId);
        return R.result(deviceDtoResult);
    }

    @GetMapping("property-of-device-by-ids")
    @Operation(summary = "根据设备id获取多个设备的某一个属性")
    public Result<Map<String, Object>> propertyOfDeviceByIds(
        @RequestHeader("ot_headers") TenantIsolation tenantIsolation, 
        @RequestParam("property") String property, 
        @RequestParam("deviceIds") Long[] deviceIds
    ) {
        return deviceDebugService.getActuralPropertyOfDevices(tenantIsolation.getTenantId(), property, deviceIds);
    }

    @PostMapping("property-of-device-by-names")
    @Operation(summary = "根据设备名获取多个设备的某一个属性")
    public Result<Map<String, Object>> propertyOfDeviceByNames(
        @RequestHeader("ot_headers") TenantIsolation tenantIsolation, 
        @RequestParam("property") String property, 
        @RequestBody DeviceDebugDto dto
    ) {
        List<String> deviceNames = dto.getDeviceNames();
        Set<Long> tenantIds= Sets.newHashSet();
        tenantIds.add(tenantIsolation.getTenantId());
        if(dto.getTenantIds() != null){
            tenantIds.addAll(dto.getTenantIds());
        }
        return deviceDebugService.getActuralPropertyOfDevicesByTenantIds(tenantIds, property, deviceNames);
    }

    @PostMapping("property-of-device-by-names-without-tenant")
    @Operation(summary = "根据设备名获取多个设备的某一个属性")
    public Result<Map<String, Object>> propertyOfDeviceByNamesWithoutTenant(
        @RequestHeader("ot_headers") TenantIsolation tenantIsolation, 
        @RequestParam("property") String property, 
        @RequestBody DeviceDebugDto dto
    ) {
        List<String> deviceNames = dto.getDeviceNames();
        List<Long> tenantIds = dto.getTenantIds();
        return deviceDebugService.getActuralPropertyOfDevices(property, deviceNames, tenantIds);
    }

    @PostMapping("property-and-time-of-device-by-names")
    @Operation(summary = "根据设备名获取多个设备的某一个属性")
    public Result<Map<String, PropertyValueWithTime>> propertyAndTimeOfDeviceByNames(
        @RequestHeader("ot_headers") TenantIsolation tenantIsolation, 
        @RequestParam("property") String property, 
        @RequestBody DeviceDebugDto dto
    ) {
        List<String> deviceNames = dto.getDeviceNames();
        return deviceDebugService.getActuralPropertyAndTimeOfDevices(tenantIsolation.getTenantId(), property, deviceNames);
    }

    @PostMapping("property-and-time-of-device-by-names-without-tenant")
    @Operation(summary = "根据设备名获取多个设备的某一个属性")
    public Result<Map<String, PropertyValueWithTime>> propertyAndTimeOfDeviceByNamesWithoutTenant(
        @RequestHeader("ot_headers") TenantIsolation tenantIsolation, 
        @RequestParam("property") String property, 
        @RequestBody DeviceDebugDto dto
    ) {
        List<String> deviceNames = dto.getDeviceNames();
        List<Long> tenantIds = dto.getTenantIds();
        return deviceDebugService.getActuralPropertyAndTimeOfDevices(property, deviceNames, tenantIds);
    }

    @GetMapping("properties-of-device-by-name")
    @Operation(summary = "根据设备名获取1个设备的多个属性")
    public Result<Map<String, Object>> propertiesOfDeviceByName(
        @RequestHeader("ot_headers") TenantIsolation tenantIsolation, 
        @RequestParam("properties") List<String> properties, 
        @RequestParam("deviceName") String deviceName
    ) {
        return deviceDebugService.getActuralPropertiesOfDevice(tenantIsolation.getTenantId(), properties, deviceName);
    }

    @GetMapping("properties-and-times-of-device-by-name")
    @Operation(summary = "根据设备名获取1个设备的多个属性")
    public Result<Map<String, PropertyValueWithTime>> propertiesAndTimesOfDeviceByName(
        @RequestHeader("ot_headers") TenantIsolation tenantIsolation, 
        @RequestParam("properties") List<String> properties, 
        @RequestParam("deviceName") String deviceName
    ) {
        return deviceDebugService.getActuralPropertiesAndTimesOfDevice(tenantIsolation.getTenantId(), properties, deviceName);
    }

    @PostMapping("device-names-to-ids")
    @Operation(summary = "设备名批量换取设备id")
    public Result<List<Long>> deviceNamesToIds(
        @RequestHeader("ot_headers") TenantIsolation tenantIsolation, 
        @RequestBody DeviceNamesToIdsDto dto
    ) {
        return deviceService.deviceNamesToIds(tenantIsolation.getTenantId(), dto);
    }

    @PostMapping("device-id-name-map-by-names")
    @Operation(summary = "ot设备id名称映射")
    public Result<Map<Long, String>> deviceIdNameMapByNames(
        @RequestHeader("ot_headers") TenantIsolation tenantIsolation, 
        @RequestBody DeviceNamesToIdsDto dto
    ) {
        return deviceService.deviceIdNameMapByNames(tenantIsolation.getTenantId(), dto.getDeviceNames());
    }

    @GetMapping("subscription/enable/{deviceId}")
    @Operation(summary = "获取运行时设备订阅状态")
    public R getSubscriptionState(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, @PathVariable("deviceId") Long deviceId) {
        if (ObjectUtil.isEmpty(deviceId)) {
            return R.error(ServiceCodeEnum.CODE_PARAM_ERROR);
        }
        Result<Set<Long>> subscriptionStateList = deviceDebugService.getSubscriptionState(tenantIsolation.getTenantId(), deviceId);
        return R.result(subscriptionStateList);
    }

    @GetMapping("mqtt-label-counter")
    @Operation(summary = "获取运行时设备订阅状态")
    public CompletableFuture<String> mqttLabelCounter(@RequestHeader("ot_headers") TenantIsolation tenantIsolation) {
        CompletableFuture<String> resultFuture = new CompletableFuture<>();

        vertx.eventBus().request(MqttLabelConsumerVerticle.MQTT_LABEL_CONSUMER_COUNTER, "get", t -> {
            String body = t.result().body().toString();
            resultFuture.complete(body);
        });
        return resultFuture;
    }

    @GetMapping("page/state-of-device")
    @Operation(summary = "查询设备状态日志")
    public Result<Page<DeviceStateVo>> pageStateOfDevice(
        @RequestHeader("ot_headers") TenantIsolation tenantIsolation,
        @RequestParam("deviceName") String deviceName, 
        @RequestParam("size") Integer size, // 每页大小
        @RequestParam("current") Integer current, // 当前页，从1开始
        @RequestParam("begin") String begin, // 开始时间，不传默认为1970-01-01 00:00:00
        @RequestParam("end") String end // 结束时间，不传默认为当前时间
    ) {
        Result<Page<DeviceStateVo>> pageResult = deviceDebugService.pageStateOfDevice(
            tenantIsolation.getTenantId(), deviceName, size, current, begin, end
        );
        return pageResult;
    }



}
