package com.nti56.nlink.product.device.server.domain.thing.enumerate;

import lombok.Getter;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-24 14:06:33
 * @since JDK 1.8
 */
public enum YesNoEnum {
    NO(0, "no", "no"), 
    YES(1, "yes", "yes")
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    YesNoEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static YesNoEnum typeOfValue(Integer value){
        YesNoEnum[] values = YesNoEnum.values();
        for (YesNoEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static YesNoEnum typeOfName(String name){
        YesNoEnum[] values = YesNoEnum.values();
        for (YesNoEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static YesNoEnum typeOfNameDesc(String nameDesc){
        YesNoEnum[] values = YesNoEnum.values();
        for (YesNoEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
