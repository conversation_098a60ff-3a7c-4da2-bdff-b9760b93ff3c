package com.nti56.nlink.product.device.server.controller;

import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.R;
import com.nti56.nlink.product.device.server.model.ServiceConfigDTO;
import com.nti56.nlink.product.device.server.service.IServiceConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.Objects;

/**
 * 类说明：
 *
 * @ClassName ModelDeviceGraphController
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/11/22 17:23
 * @Version 1.0
 */

@RestController
@RequestMapping("/thing-service/config")
@Tag(name = "通用服务配置")
public class ThingServiceConfigController {

    @Autowired
    private IServiceConfigService serviceConfigService;

   /* @Value("${job.server.admin.addresses}")
    private String adminAddresses;

    @Value("${job.server.userName}")
    private String userName;

    @Value("${job.server.password}")
    private String password;

    @Value("${job.server.jobGroup}")
    private String jobGroup;*/

    @GetMapping("/{serviceId}")
    @Operation(summary = "查询服务配置", parameters = {
            @Parameter(name = "serviceId", description = "物服务ID", required = true),
            @Parameter(name = "modelId", description = "模型ID", required = true),
            @Parameter(name = "deviceId", description = "设备ID", required = true)
    })
    public R getServiceConfig(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, @PathVariable("serviceId") Long serviceId,
                              Long modelId, Long deviceId) {
        if (!Objects.isNull(deviceId)) {
            return R.result(serviceConfigService.getConfigByDeviceId(tenantIsolation, serviceId, deviceId));
        } else if (!Objects.isNull(modelId)) {
            return R.result(serviceConfigService.getConfigByModelId(tenantIsolation, serviceId, modelId));
        } else {
            return R.error("设备ID和模型ID为空，找不到指定配置");
        }
    }

    @PostMapping("/")
    @Operation(summary = "创建/修改配置")
    public R getInheritsGraphByName(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                                    @Parameter(description = "配置实体", required = true)
                                    @RequestBody ServiceConfigDTO serviceConfig) {

        return R.result(serviceConfigService.saveOrUpdateConfig(serviceConfig, tenantIsolation));

    }

    //todo 任务开启 停止 删除

    /*@GetMapping("/createTask")
    public R testCreateTask() {

        JobInfoDTO jobInfoDTO = JobInfoDTO.builder().jobDesc("test pd crate")
                .cron("0,30 * * * * ? ")
                .jobGroup(Long.valueOf(jobGroup))
                .adminUrl("https://ot-dev.nti56.com/xxl-job-admin")
                .userName(userName)
                .password(password)
                .jobParam("aaa")
                .taskHandler("serviceTaskHandler")
                .build();
        XxlJobUtil.add("https://ot-dev.nti56.com/xxl-job-admin/jobinfo/add", jobInfoDTO);
        return R.ok();
    }*/
}
