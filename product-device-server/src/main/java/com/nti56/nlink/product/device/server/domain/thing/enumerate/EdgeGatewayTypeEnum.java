package com.nti56.nlink.product.device.server.domain.thing.enumerate;

import lombok.Getter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 类说明: 网关节点类型枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 14:04:42
 * @since JDK 1.8
 */
public enum EdgeGatewayTypeEnum {
    GATEWAY(1, "gateway", "网关设备"),
    VIRTUAL_GATEWAY(2, "virtualGateway", "虚拟网关"),

    CONNECTOR_GATEWAY(3, "connectorGateway", "直连网关"),

    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    EdgeGatewayTypeEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static EdgeGatewayTypeEnum typeOfValue(Integer value){
        EdgeGatewayTypeEnum[] values = EdgeGatewayTypeEnum.values();
        for (EdgeGatewayTypeEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static EdgeGatewayTypeEnum typeOfName(String name){
        EdgeGatewayTypeEnum[] values = EdgeGatewayTypeEnum.values();
        for (EdgeGatewayTypeEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static EdgeGatewayTypeEnum typeOfNameDesc(String nameDesc){
        EdgeGatewayTypeEnum[] values = EdgeGatewayTypeEnum.values();
        for (EdgeGatewayTypeEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }

    public static List toList(){
        List<Map> result = new ArrayList<>();
        EdgeGatewayTypeEnum[] values = EdgeGatewayTypeEnum.values();
        Map<String,Object> map ;
        for (EdgeGatewayTypeEnum v : values) {
            map = new HashMap<>();
            map.put("name",v.name);
            map.put("value",v.value);
            map.put("nameDesc",v.nameDesc);
            result.add(map);
        }
        return result;
    }
}
