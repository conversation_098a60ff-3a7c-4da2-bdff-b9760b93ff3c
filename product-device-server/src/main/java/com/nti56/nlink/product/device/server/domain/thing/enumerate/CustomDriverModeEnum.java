package com.nti56.nlink.product.device.server.domain.thing.enumerate;

import lombok.Getter;

public enum CustomDriverModeEnum {
    ASYNC(1, "async", "异步"),
    SYNC(2, "sync", "同步")
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    CustomDriverModeEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static CustomDriverModeEnum typeOfValue(Integer value){
        CustomDriverModeEnum[] values = CustomDriverModeEnum.values();
        for (CustomDriverModeEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static CustomDriverModeEnum typeOfName(String name){
        CustomDriverModeEnum[] values = CustomDriverModeEnum.values();
        for (CustomDriverModeEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static CustomDriverModeEnum typeOfNameDesc(String nameDesc){
        CustomDriverModeEnum[] values = CustomDriverModeEnum.values();
        for (CustomDriverModeEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
