package com.nti56.nlink.product.device.server.domain.redirect;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.http.*;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Maps;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.nlink.product.device.server.entity.InstanceRedirectEntity;
import com.nti56.nlink.product.device.server.service.impl.strategy.MQTTClientPool;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.util.Strings;
import org.springframework.util.StringUtils;

import java.util.Map;
import java.util.Objects;
import java.util.Set;


/**
 * 类说明：
 *
 * @ClassName WebhookRedirectFn
 * @Description webhook 回调domain
 * <AUTHOR>
 * @Date 2022/6/27 11:40
 * @Version 1.0
 */
@Slf4j
@Data
public class WebhookRedirectFn implements RedirectFn {

    private String method;

    private String url;

    private Object body;

    private Set<Param> headers;

    private Set<Param> params;

    private Long redirectId;

    private Integer connectionTimeout;

    private Integer requestTimeout;

    private Long reconnectGapTime = 30000L;
    @Override
    public Result<Object> execFn() {
        if(MQTTClientPool.recentConnectFail(redirectId,reconnectGapTime)){
            return Result.error("webhook连接异常重连中.....请稍后！");
        }
        HttpRequest request = HttpUtil.createRequest(Method.valueOf(Strings.toUpperCase(method)), url);
        request.setConnectionTimeout(connectionTimeout);
        request.setReadTimeout(requestTimeout);
        if (CollectionUtil.isNotEmpty(headers)) {
            for (Param header : headers) {
                request.header(header.getKey(), String.valueOf(header.getValue()));
            }
        }
        if (CollectionUtil.isNotEmpty(params)) {
            Map<String, Object> formMap = Maps.newHashMap();
            for (Param param : params) {
                formMap.put(param.getKey(), param.getValue());
            }
            request.form(formMap);
        }
        if (!Objects.isNull(body) && !StringUtils.isEmpty(body)) {
            request.body(String.valueOf(body), ContentType.JSON.getValue());
        }
        try {
//            log.info("==========param:{},url:{},method:{},redirectId:{}=========",JSONUtil.toJsonStr(params),url,method,redirectId);

            HttpResponse execute = request.execute();
            if (execute.getStatus() == HttpStatus.HTTP_OK) {
                log.info("execute result:{}",execute.body());
                return Result.ok();
            }
        } catch (Exception e) {
            MQTTClientPool.putRedirectConnectFailedTime(redirectId);
            log.error("invoke redirect function occur error.error msg:{}", e.getMessage());
            return Result.error(ServiceCodeEnum.CODE_UNKNOWN_ERROR, e.getMessage());
        }

        return Result.error(ServiceCodeEnum.CODE_UNKNOWN_ERROR, "调用错误");
    }

    @Override
    public Result<Object> execFnSync() {
        return null;
    }

    @Override
    public String getTargetUrl() {
        return this.url;
    }

    public static WebhookRedirectFn getFunctionInstance(InstanceRedirectEntity instanceRedirectEntity, Object payload) {
        try {
            WebhookRedirectFn webhookRedirectFn = JSONUtil.toBean(instanceRedirectEntity.getRedirectFn(), WebhookRedirectFn.class);
            webhookRedirectFn.setRedirectId(instanceRedirectEntity.getId());
            webhookRedirectFn.setConnectionTimeout(instanceRedirectEntity.getRedirectRequestConnectTimeout());
            webhookRedirectFn.setRequestTimeout(instanceRedirectEntity.getRedirectRequestTimeout());
            if(!Objects.isNull(payload)){
                webhookRedirectFn.setBody(payload);
            }
            return webhookRedirectFn;
        } catch (Exception e) {
            log.error("phrase webhook function error,can't phrase to Bean");
        }
        return null;
    }
}

class Param {
    private String key;
    private Object value;

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public Object getValue() {
        return value;
    }

    public void setValue(Object value) {
        this.value = value;
    }
}
