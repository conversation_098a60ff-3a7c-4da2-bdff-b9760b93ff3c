package com.nti56.nlink.product.device.server.domain.thing.enumerate;

import lombok.Getter;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-24 14:06:33
 * @since JDK 1.8
 */
public enum FaultEventStatusEnum {
    START(true, "start", "event start"),
    END(false, "end", "event end")
    ;

    @Getter
    private Boolean value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    FaultEventStatusEnum(Boolean value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static FaultEventStatusEnum typeOfValue(Boolean value){
        FaultEventStatusEnum[] values = FaultEventStatusEnum.values();
        for (FaultEventStatusEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static FaultEventStatusEnum typeOfName(String name){
        FaultEventStatusEnum[] values = FaultEventStatusEnum.values();
        for (FaultEventStatusEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static FaultEventStatusEnum typeOfNameDesc(String nameDesc){
        FaultEventStatusEnum[] values = FaultEventStatusEnum.values();
        for (FaultEventStatusEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
