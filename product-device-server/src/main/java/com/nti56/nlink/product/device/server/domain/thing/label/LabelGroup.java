package com.nti56.nlink.product.device.server.domain.thing.label;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ArrayUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.nti56.nlink.common.fetcher.CommonFetcher;
import com.nti56.nlink.common.fetcher.Preloader;
import com.nti56.nlink.common.util.BeanUtilsIntensifier;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.nlink.product.device.server.entity.ChannelEntity;
import com.nti56.nlink.product.device.server.entity.LabelEntity;
import com.nti56.nlink.product.device.server.entity.LabelGroupEntity;
import com.nti56.nlink.product.device.server.enums.ErrorEnum;
import com.nti56.nlink.product.device.server.exception.BizException;
import com.nti56.nlink.product.device.server.model.label.dto.ExcelLabelDTO;
import com.nti56.nlink.product.device.server.model.label.dto.LabelDTO;
import com.nti56.nlink.common.util.JwtUserInfoUtils;
import lombok.*;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 类说明: 标签分组领域对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-05-19 09:20:16
 * @since JDK 1.8
 */
@Builder
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class LabelGroup {

    public static final String LABEL_GROUP_SPLIT_SYMBOL = "\\.";
    public static final String LABEL_GROUP_GRADE_SYMBOL = ".";

    @Setter
    private Long id;
    private String name;
    private int level;
    private String levelName;
    private Long channelId;
    private String channelName;
    private List<LabelGroup> children;
    private List<LabelDTO> labels;
    private String descript;
    private Integer driver;
    private Long edgeGatewayId;

    public static Result<LabelGroup> checkInfo(LabelGroupEntity entity, CommonFetcher commonFetcher){

        if(entity == null){
            return Result.error("标签分组不能为空");
        }
        LabelGroup labelGroup = new LabelGroup();
        labelGroup.id = entity.getId();
        labelGroup.name = entity.getName();
        labelGroup.channelId = entity.getChannelId();
        ChannelEntity channelEntity = commonFetcher.get(entity.getChannelId(), ChannelEntity.class);
        labelGroup.channelName = channelEntity.getName();
        labelGroup.edgeGatewayId = channelEntity.getEdgeGatewayId();
        return Result.ok(labelGroup);
    }

    public static LabelGroup getFlatBean(LabelGroupEntity labelGroupEntity) {
        return LabelGroup.builder()
                .id(labelGroupEntity.getId())
                .name(labelGroupEntity.getName())
                .build();
    }

    public static List<Long> getLabelGroupIds(String prefix,Long channelId,CommonFetcher commonFetcher){
        List<LabelGroupEntity> ls = commonFetcher.list("channel_id", channelId, LabelGroupEntity.class);
        Stream<LabelGroupEntity> stream = ls.stream();
        List<Long> lgIds;
        if (!StringUtils.isEmpty(prefix)) {
            String[] split = prefix.split(LABEL_GROUP_SPLIT_SYMBOL);
            int level = split.length - 1;
            stream = stream.filter(labelGroupEntity -> labelGroupEntity.getName().indexOf(prefix) == 0)
                    .filter(labelGroupEntity -> {
                        String[] split1 = labelGroupEntity.getName().split(LABEL_GROUP_SPLIT_SYMBOL);
                        return split1[level].equals(split[level]);
                    });
        }
        lgIds = stream.map(LabelGroupEntity::getId).collect(Collectors.toList());
        return lgIds;
    }

    public static List<LabelDTO> getLabelsByPrefix(String prefix, Long channelId, CommonFetcher commonFetcher){
        List<LabelDTO> labelList = new ArrayList<>();
        Stream<LabelGroupEntity> lgByChannel = commonFetcher.list("channel_id", channelId, LabelGroupEntity.class).stream();
        if (!StringUtils.isEmpty(prefix)) {
            String[] split = prefix.split(LabelGroup.LABEL_GROUP_SPLIT_SYMBOL);
            int level = split.length - 1;
            lgByChannel = lgByChannel
                    .filter(labelGroupEntity -> labelGroupEntity.getName().indexOf(prefix) == 0)
                    .filter(labelGroupEntity -> {
                        String[] split1 = labelGroupEntity.getName().split(LABEL_GROUP_SPLIT_SYMBOL);
                        return split1[level].equals(split[level]);
                    });
        }
        List<LabelGroupEntity> list = lgByChannel.collect(Collectors.toList());
        List<Long> labelGroupIds = list.stream().map(LabelGroupEntity::getId).collect(Collectors.toList());
        Preloader<LabelEntity> labelsPreloader = commonFetcher.preloader(LabelEntity.class).preload("label_group_id", labelGroupIds);
        list.forEach(labelGroupEntity -> {
            List<LabelEntity> labels = labelsPreloader.filter(LabelEntity::getLabelGroupId,labelGroupEntity.getId());
            Optional.ofNullable(labels).orElse(new ArrayList<>()).forEach(labelEntity -> {
                labelEntity.setName(labelGroupEntity.getName() + LabelGroup.LABEL_GROUP_GRADE_SYMBOL + labelEntity.getName());
                LabelDTO labelDTO = BeanUtilsIntensifier.copyBean(labelEntity, LabelDTO.class);
                labelList.add(labelDTO);
            });
        });
        if(StringUtils.isEmpty(JwtUserInfoUtils.userJson())){
            throw new BizException(ErrorEnum.MENU_USER_INFO_NOT_EXIST.getCode(), ErrorEnum.MENU_USER_INFO_NOT_EXIST.getMessage());
        }
        JSONObject json = JSON.parseObject(JwtUserInfoUtils.userJson());
        Integer labelAllow = json.getInteger("labelAllow");
        if(labelAllow!=null && labelAllow == 0){
            labelList.forEach(labelDTO->{
                labelDTO.setAddress("");
                labelDTO.setDescript("");
            });
        }
        return labelList;
    }

    public static List<LabelGroupEntity> updateLevelNameByPrefix(String prefix, String newLevelName, Long channelId, CommonFetcher commonFetcher){
        String[] split = prefix.split(LabelGroup.LABEL_GROUP_SPLIT_SYMBOL);
        int level = split.length - 1;
        List<LabelGroupEntity> ls = commonFetcher.list("channel_id", channelId, LabelGroupEntity.class);
        List<LabelGroupEntity> list = ls.stream()
                .filter(labelGroupEntity -> labelGroupEntity.getName().indexOf(prefix) == 0)
                .filter(labelGroupEntity -> {
                    String[] split1 = labelGroupEntity.getName().split(LABEL_GROUP_SPLIT_SYMBOL);
                    return split1[level].equals(split[level]);
                })
                .collect(Collectors.toList());
        if (!split[level].equals(newLevelName)) {
            batchRename(list, newLevelName, level);
        }
        return list;
    }

    private static void batchRename(List<LabelGroupEntity> list, String newLevelName, int level) {
        if (CollectionUtil.isNotEmpty(list)) {
            list.forEach(labelGroupEntity -> {
                rename(labelGroupEntity, newLevelName,level);
            });
        }
    }

    private static void rename(LabelGroupEntity labelGroupEntity, String newLevelName, int level) {
        String[] split = labelGroupEntity.getName().split(LabelGroup.LABEL_GROUP_SPLIT_SYMBOL);
        split[level] = newLevelName;
        labelGroupEntity.setName(buildName(split,split.length -1));
    }

    public static String rename(String name, String newLevelName){
        String[] split = name.split(LabelGroup.LABEL_GROUP_SPLIT_SYMBOL);
        split[split.length - 1] = newLevelName;
        return buildName(split,split.length -1);
    }

    public static LabelGroup buildLevelBean(String levelName, String name,int level){
        return LabelGroup.builder()
                .levelName(levelName)
                .name(name)
                .level(level)
                .children(new ArrayList<>())
                .build();
    }

    public static void setBean(LabelGroupEntity labelGroupEntity, List<LabelGroup> labelGroups){
        String[] levelNames = labelGroupEntity.getName().split(LabelGroup.LABEL_GROUP_SPLIT_SYMBOL);
        setBean(labelGroupEntity, levelNames, 0, labelGroups, levelNames.length == 1);
    }

    public static String buildName(String[] levelNames, int index) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0 ;i <= index;i++){
            sb.append(levelNames[i]);
            if (i < index){
                sb.append(LabelGroup.LABEL_GROUP_GRADE_SYMBOL);
            }
        }
        if (sb.length() > 128) {
            throw new BizException(ServiceCodeEnum.LABEL_GROUP_NAME_ERROR);
        }
        return sb.toString();
    }

    public static void setBean(LabelGroupEntity entity, String[] levelNames, int level, List<LabelGroup> labelGroups, boolean lastLevel){
        LabelGroup labelGroup = null;
        if (CollectionUtil.isNotEmpty(labelGroups)) {
            Optional<LabelGroup> first = labelGroups.stream().filter(lg -> lg.levelName.equals(levelNames[level])).findFirst();
            if (first.isPresent()) {
                labelGroup = first.get();
            }
        }
        if (!Optional.ofNullable(labelGroup).isPresent()) {
            labelGroup = LabelGroup.builder()
                    .levelName(levelNames[level])
                    .name(buildName(levelNames,level))
                    .level(level)
                    .children(new ArrayList<>())
                    .descript(entity.getDescript())
                    .build();
            labelGroups.add(labelGroup);
        }
        if (lastLevel) {
            labelGroup.id = entity.getId();
            labelGroup.channelId = entity.getChannelId();
            labelGroup.descript = entity.getDescript();
        }else {
            setBean(entity, levelNames,level + 1,labelGroup.children, levelNames.length == level + 2);
        }
    }

    public static List<LabelGroup> getBeans(List<LabelGroupEntity> labelGroupEntities, CommonFetcher commonFetcher,String search){
        List<LabelGroup> labelGroups = new ArrayList<>();
        if (!StringUtils.isEmpty(search)) {
            labelGroupEntities = labelGroupEntities.stream().filter(labelGroupEntity -> labelGroupEntity.getName().contains(search)).collect(Collectors.toList());
        }
        Supplier<Stream<LabelGroupEntity>> streamSupplier = labelGroupEntities::stream;
        streamSupplier.get().sorted(Comparator.comparing(LabelGroupEntity::getName)).forEach(labelGroupEntity -> {
            setBean(labelGroupEntity, labelGroups);
        });
        return labelGroups;
    }

    public static List<LabelGroup> inputLabels(List<LabelDTO> labels, Long channelId, CommonFetcher commonFetcher,List<ExcelLabelDTO> excelLabelDTOList){
        List<LabelGroup> labelGroups = new ArrayList<>();
        Map<String, List<LabelDTO>> groupList = groupLabels(labels);
        List<LabelGroupEntity> list = commonFetcher.list("channel_id", channelId, LabelGroupEntity.class);
        list.stream().filter(labelGroupEntity -> groupList.containsKey(labelGroupEntity.getName())).forEach(labelGroupEntity -> {
            LabelGroup build = LabelGroup.builder()
                    .labels(groupList.get(labelGroupEntity.getName()))
                    .id(labelGroupEntity.getId())
                    .name(labelGroupEntity.getName())
                    .build();
            labelGroups.add(build);
            groupList.remove(labelGroupEntity.getName());
        });
        groupList.forEach( (k,v) ->{
            if (k.length() > 256) {
                ExcelLabelDTO excelLabelDTO = new ExcelLabelDTO();
                excelLabelDTO.setMessageName(k);
                excelLabelDTO.setMessageContent("labelName 长度 > 256!");
                excelLabelDTOList.add(excelLabelDTO);
            }else {
                LabelGroup build = LabelGroup.builder()
                    .name(k)
                    .labels(v)
                    .build();
                labelGroups.add(build);
            }
        });
        return labelGroups;
    }

    public static List<LabelGroup> inputLabels(List<LabelDTO> labels, Long channelId, CommonFetcher commonFetcher){
        List<LabelGroup> labelGroups = new ArrayList<>();
        Map<String, List<LabelDTO>> groupList = groupLabels(labels);
        List<LabelGroupEntity> list = commonFetcher.list("channel_id", channelId, LabelGroupEntity.class);
        list.stream().filter(labelGroupEntity -> groupList.containsKey(labelGroupEntity.getName())).forEach(labelGroupEntity -> {
            LabelGroup build = LabelGroup.builder()
                    .labels(groupList.get(labelGroupEntity.getName()))
                    .id(labelGroupEntity.getId())
                    .name(labelGroupEntity.getName())
                    .build();
            labelGroups.add(build);
            groupList.remove(labelGroupEntity.getName());
        });
        groupList.forEach( (k,v) ->{
            if (k.length() > 256) {
                throw new BizException(ServiceCodeEnum.LABEL_GROUP_NAME_ERROR);
            }
            LabelGroup build = LabelGroup.builder()
                    .name(k)
                    .labels(v)
                    .build();
            labelGroups.add(build);
        });
        return labelGroups;
    }


    public static void main(String[] args) {
        String name = "aaa";
        int index = name.lastIndexOf(LabelGroup.LABEL_GROUP_GRADE_SYMBOL);
        System.out.println(index);
    }

    private static Map<String,List<LabelDTO>> groupLabels(List<LabelDTO> labels) {
        Map<String,List<LabelDTO>> group = new HashMap<>();
        labels.forEach(label -> {
            String fullName = label.getName();
            int index = fullName.lastIndexOf(LabelGroup.LABEL_GROUP_GRADE_SYMBOL);
            String groupName;
            if (index == -1) {
                groupName = "default";
            }else {
                groupName = fullName.substring(0,index);
            }
            label.setName(fullName.substring(index + 1));
            if (group.containsKey(groupName)) {
                group.get(groupName).add(label);
            }else {
                List<LabelDTO> list = new ArrayList<>();
                list.add(label);
                group.put(groupName,list);
            }
            String[] split = groupName.split(LabelGroup.LABEL_GROUP_SPLIT_SYMBOL);
            StringBuilder groupNameSB = new StringBuilder();
            for (int i = 0; i < split.length - 1; i++) {
                groupNameSB.append(split[i]);
                if (!group.containsKey(groupNameSB.toString())) {
                    group.put(groupNameSB.toString(),new ArrayList<>());
                }
                groupNameSB.append(LABEL_GROUP_GRADE_SYMBOL);
            }
        });
        return group;
    }

    public static LabelGroup getLevelBean(String[] split, List<LabelGroup> beans) {
        if (ArrayUtil.isEmpty(split)) {
            return null;
        }
        LabelGroup bean = null;
        for (int i = 0;i < split.length;i++) {
            int finalI = i;
            if (CollectionUtil.isEmpty(beans)) {
                return null;
            }
            List<LabelGroup> collect = beans.stream().filter(labelGroup -> labelGroup.getLevelName().equals(split[finalI])).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(collect) || collect.size() != 1) {
                return null;
            }
            bean = collect.get(0);
            beans = bean.children;
        }
        return bean;
    }

    public static Set<String> getFastLevelGroup(List<LabelGroupEntity> groupEntityList) {
        Set<String> names = new HashSet<>();
        if (CollectionUtil.isNotEmpty(groupEntityList)) {
            groupEntityList.forEach(labelGroupEntity -> {
                String[] split = labelGroupEntity.getName().split(LabelGroup.LABEL_GROUP_SPLIT_SYMBOL);
                names.add(split[0]);
            });
        }
        return names;
    }

    public void setDriver(Integer driver){
        this.driver = driver;
        if (CollectionUtil.isNotEmpty(children)){
            for (LabelGroup child : children) {
                child.setDriver(driver);
            }
        }
    }
}
