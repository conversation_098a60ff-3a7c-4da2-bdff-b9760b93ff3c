package com.nti56.nlink.product.device.server.domain.thing.device;

import com.nti56.nlink.product.device.server.domain.thing.modelbase.Subscription;
import com.nti56.nlink.product.device.server.entity.DeviceServiceLogEntity;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName DataResource
 * @date 2022/7/25 18:30
 * @Version 1.0
 */
public interface DeviceDataResource {


    Map<String, Object> getById(Long deviceId);

    Object getProperty(Long deviceId, String property);

    boolean setProperty(Long deviceId, String property, Object value);

    boolean setProperties(Long deviceId, Map<String, Object> properties);

    Set<Subscription> getSubscriptionRegistry(Long deviceId, Collection<String> properties);

    Map<String,Set<Subscription>> getSubscriptionRegistry2Map(Long deviceId, Collection<String> properties);

    Map<String,Set<Subscription>> getNoChangeSubscriptionRegistry2Map(Long deviceId, Collection<String> properties);

    Map<String, Object> getProperties(Long deviceId, Collection<String> properties);

    void addDeviceServiceCallLog(DeviceServiceLogEntity logEntity);

    Map<Long, Map<String, Object>> getByIds(List<Long> deviceIds);

    Set<Long> getSubscriptionEnable(Long tenantId, Long deviceId);

    Map<String, Object> getChangeTimeById(Long deviceId);

    void updateNoChangeExpireTime(String key, Integer seconds);

    void deleteProperties(Long id, Set<String> oldPropertyNames);
}
