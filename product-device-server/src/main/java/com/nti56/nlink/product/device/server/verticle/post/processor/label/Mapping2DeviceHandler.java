package com.nti56.nlink.product.device.server.verticle.post.processor.label;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.nti56.nlink.common.util.BeanUtilsIntensifier;
import com.nti56.nlink.product.device.server.constant.RedisConstant;
import com.nti56.nlink.product.device.server.domain.thing.device.DeviceTwin;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.ReportTypeEnum;
import com.nti56.nlink.product.device.server.domain.thing.topic.GwUpLabelTopic;
import com.nti56.nlink.product.device.server.domain.thing.topic.GwUpPropertyTopic;
import com.nti56.nlink.product.device.server.domain.thing.up.UpData;
import com.nti56.nlink.product.device.server.domain.thing.up.UpProp;
import com.nti56.nlink.product.device.server.service.cache.redis2Mem.MemoryCache;
import com.nti56.nlink.product.device.server.util.redis.RedisUtil;
import com.nti56.nlink.product.device.server.verticle.post.processor.PostProcessorHandler;
import com.nti56.nlink.product.device.server.verticle.post.processor.property.UpData2InfluxDBHandler;
import com.nti56.nlink.product.device.server.verticle.post.processor.property.UpData2RedisHandler;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 说明：
 *
 */
@Slf4j
@Component
public class Mapping2DeviceHandler extends PostProcessorHandler<GwUpLabelTopic.TopicInfo> implements InitializingBean{

    @Async("labelConsumerAsyncExecutor")
    @Override
    public void process(GwUpLabelTopic.TopicInfo topicInfo, UpData upData){
        long start = System.currentTimeMillis();
        log.debug("上报链路,mapping2DeviceHandler begin:{},topic:{}",start,topicInfo);
        super.process(topicInfo,upData);
        long end = System.currentTimeMillis();
        log.debug("上报链路,mapping2DeviceHandler end:{},耗时：{}ms,topic:{}",end,end - start,topicInfo);
    }

    @Autowired @Lazy
    RedisTemplate redisTemplate;

    @Lazy
    @Autowired
    UpData2RedisHandler upData2RedisHandler;

    @Lazy
    @Autowired
    UpData2InfluxDBHandler upData2InfluxDBHandler;

    @Lazy
    @Autowired
    PropertyChangeToWSHandler propertyChangeToWsHandler;

    @Override
    public void afterPropertiesSet() throws Exception {
    }

    @Override
    public void doProcess(GwUpLabelTopic.TopicInfo topicInfo, UpData upData) {
        RedisUtil redisUtil = new RedisUtil(redisTemplate, null);
        Map<Long, List<UpProp>> deviceUpPorp = new HashMap<>();
        Map<Long,Map<String,String>> deviceNameMap = new HashMap<>();
        if(CollectionUtil.isEmpty(upData.getUpGroups())){
            log.debug("upData upGroups is empty");
        }
        upData.getUpGroups().forEach(upGroup -> {
//            Map<String, UpProp> propMap = BeanUtilsIntensifier.collection2Map(upGroup.getProp(), UpProp::getLabelName);
            Map<String, UpProp> propMap = upGroup.getProp().stream().collect(Collectors.toMap(prop -> {
                return prop.getLabelName() + prop.getDataType() + prop.getIsArray();
            }, Function.identity()));
            if(CollectionUtil.isEmpty(propMap)){
                log.debug("propMap is empty");
            }
            String key = String.format(RedisConstant.GROUP_DEVICE_MAPPING, topicInfo.getEdgeGatewayId(), upGroup.getChannelName(), upGroup.getGroupName());
            // Map<Object, Object> hmget = redisUtil.hmget(key);
             Map<String, Object> hmget= MemoryCache.getGroupDeviceMap(key);
            if(CollectionUtil.isEmpty(hmget)){
                log.debug("hmget is empty");
            }
            hmget.forEach((deviceIdStr,setStr) -> {
                // Boolean enable = (Boolean) redisUtil.get(String.format(RedisConstant.DEVICE_ENABLE, deviceIdStr));
                Boolean enable = (Boolean) MemoryCache.getDeviceEnable(Long.parseLong((String)deviceIdStr));
                if (ObjectUtils.isEmpty(enable) || !enable) {
                    log.debug("enable is empty or false and enable is {},enable");
                    return;
                }
                Long deviceId = Long.valueOf(deviceIdStr.toString());
                for (String property : (Set<String>) setStr) {
                    if (propMap.containsKey(property)) {
                        if (!deviceUpPorp.containsKey(deviceId)) {
                            deviceUpPorp.put(deviceId,new ArrayList<>());
                        }
                        Map<String,String> nameMap;
                        if (!deviceNameMap.containsKey(deviceId)) {
                            // String labelPropertyKey = String.format(RedisConstant.LABEL_PROPERTY_MAPPING, deviceId);
                            nameMap = MemoryCache.getLabelPropertyMap(deviceId);
                            if(nameMap==null){
                                  log.warn("1:nameMap is null,deviceId:{} ",deviceId);
                            }
                            deviceNameMap.put(deviceId,nameMap);
                        }else {
                            nameMap = deviceNameMap.get(deviceId);
                        }
                        UpProp upProp = propMap.get(property);
                        upProp.setDeviceId(deviceId);
                        String k = upGroup.getChannelName() + "." + upGroup.getGroupName() + "." + upProp.getLabelName();
                        // log.info("k is {}",k);
                        // if (upProp == null||upProp.getLabelName()==null) {
                        //     log.warn("upProp is null? :{}",upProp==null);
                        // }else{
                        //     log.info("upProp.getLabelName() is {}",upProp.getLabelName());
                        // }
                        if (nameMap == null) {
                            log.warn("2:nameMap is null,deviceId:{}",deviceId);
                        }
                        String defaultPropertyNames=nameMap.getOrDefault(k, upProp.getLabelName());
                        // if(defaultPropertyNames==null){
                        //     log.warn("defaultPropertyNames is null");
                        // }
                        String[] propertyNames = nameMap.getOrDefault(k, upProp.getLabelName()).split(",");
                        List<UpProp> list = new ArrayList<>();
                        if (propertyNames.length > 1) {
                            for (int i = 0; i < propertyNames.length; i++) {
                                UpProp upProp1 = BeanUtilsIntensifier.copyBean(upProp, UpProp.class);
                                upProp1.setProperty(propertyNames[i]);
                                list.add(upProp1);
                            }
                        }else {
                            UpProp upProp1 = BeanUtilsIntensifier.copyBean(upProp, UpProp.class);
                            upProp1.setProperty(propertyNames[0]);
                            list.add(upProp1);
                        }
                        deviceUpPorp.get(deviceId).addAll(list);
                    }else {
                        log.debug("propMap is not contains property key and property is {}",property);
                    }
                }
            });
        });
        if(CollectionUtil.isEmpty(deviceUpPorp)){
            log.debug("deviceUpPorp is empty");
        }
        deviceUpPorp.forEach((deviceId,upPorps) -> {
            GwUpPropertyTopic.TopicInfo propTopic = GwUpPropertyTopic.TopicInfo.builder()
                    .deviceId(deviceId.toString())
                    .edgeGatewayId(topicInfo.getEdgeGatewayId())
                    .tenantId(topicInfo.getTenantId())
                    .reportType(ReportTypeEnum.CHANGE_REPORT.getName())
                    .build();
            UpData upData1 = BeanUtilsIntensifier.copyBean(upData,UpData.class);
            upData1.setProp(upPorps);
            upData1.setUpGroups(null);
            upData2InfluxDBHandler.process(propTopic,upData1);
            upData2RedisHandler.process(propTopic,upData1);
            propertyChangeToWsHandler.process(propTopic, upData1);
        });
    }

    public void processWriteTwin(Long edgeGatewayId, Long deviceId, Long tenantId, UpData upData){
        GwUpPropertyTopic.TopicInfo propTopic = GwUpPropertyTopic.TopicInfo.builder()
                .deviceId(deviceId.toString())
                .edgeGatewayId(edgeGatewayId.toString())
                .tenantId(tenantId.toString())
                .reportType(ReportTypeEnum.WRITE_TWIN.getName())
                .build();
        upData2RedisHandler.process(propTopic, upData);
        upData2InfluxDBHandler.process(propTopic, upData);
        propertyChangeToWsHandler.process(propTopic, upData);
    }


    public void processWriteTwin(Long edgeGatewayId, Long deviceId, Long tenantId, UpData upData, DeviceTwin deviceTwin){
        GwUpPropertyTopic.TopicInfo propTopic = GwUpPropertyTopic.TopicInfo.builder()
                .deviceId(deviceId.toString())
                .edgeGatewayId(edgeGatewayId.toString())
                .tenantId(tenantId.toString())
                .reportType(ReportTypeEnum.WRITE_TWIN.getName())
                .build();
        upData2InfluxDBHandler.process(propTopic, upData);
        upData2RedisHandler.doProcess(propTopic, upData,deviceTwin);
        propertyChangeToWsHandler.process(propTopic, upData);
    }
}
