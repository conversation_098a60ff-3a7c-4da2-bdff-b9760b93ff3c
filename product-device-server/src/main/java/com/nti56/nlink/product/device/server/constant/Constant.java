package com.nti56.nlink.product.device.server.constant;

/**
 * 类说明: 常量
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-11-30 17:38:52
 * @since JDK 1.8
 */
public class Constant {

    public final static String CHANNEL_CACHE_CONNECT = "channelCacheConnect";
    public final static String CHANNEL_CONNECT = "channelConnect";
    public static final int USER_SOURCE_IT = 0;
    public static final int USER_SOURCE_OT = 1;
    
    public final static String ZIP_SUFFIX = ".zip";


    public static final String AUTH_FAILED_PATH = "/auth/failed";

    public static final String WEB_FILTER_ATTR_NAME = "filterchain";
    //认证失败属性名
    public static final String AUTH_ERROR_ATTR_NAME = "auth-error";

    public static final String TOKEN_HEADER = "Authorization";

    public static final String LOGIN_TOKEN = "Logintoken";

    public static final String CLIENT_ID = "Clientid";

    public static final String TENANT_ID = "tenantId";
    public static final String MODULE_ID = "moduleId";
    public static final String SPACE_ID = "spaceId";
    public static final String ENGINEERING_ID = "engineeringId";

    public static final String TOKEN_PREFIX = "Bearer ";

    public static final String TENANT_HEADER = "ot_headers";

    public static final String APPCODE_HEADER = "ot";

    public static final String PMO_DEVICE_TAG_KEY = "所属客户";
    public static final String APP_CODE_STR = "appCode";
    public static final Long DEFAULT_THING = 999L;




}
