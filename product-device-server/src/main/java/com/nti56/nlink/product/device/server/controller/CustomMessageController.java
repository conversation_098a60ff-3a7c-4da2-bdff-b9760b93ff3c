package com.nti56.nlink.product.device.server.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.PageParam;
import com.nti56.nlink.common.util.R;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.service.ICustomMessageService;
import com.nti56.nlink.product.device.server.entity.CustomMessageEntity;
import com.nti56.nlink.product.device.server.model.custom.CustomMessageDto;
import com.nti56.nlink.product.device.server.model.custom.CustomMessageFullBo;

import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;

import java.util.List;


/**
 * <p>
 * 自定义消息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2022-11-18 11:43:28
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/")
@Tag( name = "自定义协议包模块")
@Api(protocols = "http,https", tags = "自定义消息表模块")
public class CustomMessageController {

    @Autowired
    ICustomMessageService service;

    // @GetMapping("custom-message/page")
    // @Operation(summary = "获取分页")
    public R page(@RequestHeader("ot_headers") TenantIsolation tenant, PageParam pageParam,CustomMessageEntity entity){
        Page<CustomMessageEntity> page = pageParam.toPage(CustomMessageEntity.class);
        Result<Page<CustomMessageEntity>> result = service.getPage(tenant, entity,page);
        return R.result(result);
    }

    @GetMapping("custom-message/list")
    @Operation(summary = "获取列表" )
    public R list(@RequestHeader("ot_headers") TenantIsolation tenant, CustomMessageEntity entity){
        Result<List<CustomMessageEntity>> result = service.list(tenant, entity);
        return R.result(result);
    }

    @PostMapping("custom-message")
    @Operation(summary = "创建协议包")
    public R create(@RequestHeader("ot_headers") TenantIsolation tenant, @ApiParam("对象") @RequestBody CustomMessageDto dto){
        Result<Long> result = service.create(tenant, dto);
        return R.result(result);
    }

    @PutMapping("custom-message")
    @Operation(summary = "更新协议包")
    public R update(@RequestHeader("ot_headers") TenantIsolation tenant, @ApiParam("对象") @RequestBody CustomMessageDto dto){
        Result<Void> result = service.update(tenant, dto);
        return R.result(result);
    }

    @DeleteMapping("custom-message/{entityId}")
    @Operation(summary = "删除协议包")
    public R delete(@RequestHeader("ot_headers") TenantIsolation tenant, @ApiParam("目标ID") @PathVariable Long entityId){
        Result<Void> result = service.deleteById(tenant, entityId);
        return R.result(result);
        }

    @GetMapping("custom-message/{entityId}")
    @Operation(summary = "获取协议包")
    public R get(@RequestHeader("ot_headers") TenantIsolation tenant, @ApiParam("目标ID") @PathVariable Long entityId){
        Result<CustomMessageFullBo> result = service.getById(tenant, entityId);
        return R.result(result);
        }
    
}
