package com.nti56.nlink.product.device.server.domain.thing.modelbase;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName StatusData
 * @Version 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class StatusData {
    
    private Long id;
    private Integer type;
    private String name;
    private Integer status;

}
