package com.nti56.nlink.product.device.server.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.*;
import com.nti56.nlink.product.device.server.entity.ResourceRelationEntity;
import com.nti56.nlink.product.device.server.service.IResourceRelationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.function.Function;


/**
 * <p>
 * 资源关系表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2022-04-11 17:27:49
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/")
@Tag(name = "资源关系表")
public class ResourceRelationController {

    @Autowired
    IResourceRelationService service;

    @GetMapping("resource_relation/page")
    @Operation(summary = "获取分页")
    public R page(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                  PageParam pageParam, ResourceRelationEntity entity){
        Page<ResourceRelationEntity> page = pageParam.toPage(ResourceRelationEntity.class);
        entity.setTenantId(tenantIsolation.getTenantId());
        Result<Page<ResourceRelationEntity>> result = service.getPage(entity,page);
        return R.result(result);
    }

    @GetMapping("resource_relation/list")
    @Operation(summary = "获取列表" )
    public R list(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                  ResourceRelationEntity entity){
        entity.setTenantId(tenantIsolation.getTenantId());
        Result<List<ResourceRelationEntity>> result = service.list(entity);
        return R.result(result);
    }

    @PostMapping("resource_relation")
    @Operation(summary = "创建对象")
    public R create(
                    @Parameter(description = "对象") @RequestBody ResourceRelationEntity entity){
        return checkParamAndDoSomething(entity,service::saveResourceRelation);
    }

    @PutMapping("resource_relation/{entityId}")
    @Operation(summary = "更新")
    public R update(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                    @Parameter(description = "对象") @RequestBody ResourceRelationEntity entity){
        if (BeanUtilsIntensifier.checkBeanAndProperties(entity, ResourceRelationEntity::getId)) {
            return R.error(ServiceCodeEnum.CODE_PARAM_ERROR);
        }
        Result result = service.update(entity,tenantIsolation);
        return R.result(result);
    }

    @DeleteMapping("resource_relation/{entityId}")
    @Operation(summary = "删除对象")
    public R delete(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                    @Parameter(description = "目标ID") @PathVariable Long entityId){
        Result result = service.deleteById(entityId,tenantIsolation);
        return R.result(result);
        }

    @GetMapping("resource_relation/{entityId}")
    @Operation(summary = "获取对象")
    public R get(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                 @Parameter(description = "目标ID") @PathVariable Long entityId){
        Result<ResourceRelationEntity> result = service.getByIdAndTenantIsolation(entityId,tenantIsolation);
        return R.result(result);
    }

    private R checkParamAndDoSomething(ResourceRelationEntity entity, Function<ResourceRelationEntity,Result> func){
        if (BeanUtilsIntensifier.checkBeanAndProperties(entity, ResourceRelationEntity::getDeviceId)) {
            return R.error(ServiceCodeEnum.CODE_PARAM_ERROR);
        }
        Result result = func.apply(entity);
        return R.result(result);
    }
    
}
