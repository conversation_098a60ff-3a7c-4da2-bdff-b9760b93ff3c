package com.nti56.nlink.product.device.server.controller;

import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.PageParam;
import com.nti56.nlink.common.util.R;
import com.nti56.nlink.product.device.server.model.upgrade.dto.BatchUpgradePackageDTO;
import com.nti56.nlink.product.device.server.model.upgrade.dto.CreateUpgradePackageDTO;
import com.nti56.nlink.product.device.server.model.upgrade.dto.EditUpgradePackageDTO;
import com.nti56.nlink.product.device.server.model.upgrade.dto.QueryUpgradePackageDTO;
import com.nti56.nlink.product.device.server.service.IUpgradePackageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 *
 * OTA升级包
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2023-3-15 11:56:31
 * @since JDK 1.8
 */
@RestController
@RequestMapping("upgradePackage")
@Tag(name = "升级包配置")
public class UpgradePackageController {
    
    @Autowired
    private IUpgradePackageService upgradePackageService;
    
    
    @GetMapping("page")
    @Operation(summary = "查询升级包配置列表" )
    public R pageUpgradePackage(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,PageParam pageParam,@Validated QueryUpgradePackageDTO queryUpgradePackageDTO){
        return R.result(upgradePackageService.pageUpgradePackage(pageParam,queryUpgradePackageDTO));
    }
    
    @PostMapping("")
    @Operation(summary = "新增升级包配置")
    public R createUpgradePackage(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@RequestPart("createUpgradePackageDTO")CreateUpgradePackageDTO createUpgradePackageDTO, MultipartFile file){
        return R.result(upgradePackageService.createUpgradePackage(createUpgradePackageDTO,file));
    }
    
    @PutMapping("{id}")
    @Operation(summary = "修改升级包配置")
    public R editUpgradePackage(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
        @RequestBody @Validated EditUpgradePackageDTO editUpgradePackageDTO){
        return R.result(upgradePackageService.editUpgradePackage(editUpgradePackageDTO));
    }
    
    @GetMapping("targetVersion")
    @Operation(summary = "获取期望版本号")
    public R getTargetVersion(@RequestHeader("ot_headers") TenantIsolation tenantIsolation){
        return R.result(upgradePackageService.getTargetVersion());
    }
    
    @GetMapping("upgradeRange")
    @Operation(summary = "获取租户网关范围")
    public R getUpgradeRange(@RequestHeader("ot_headers") TenantIsolation tenantIsolation){
        return R.result(upgradePackageService.getUpgradeRange(tenantIsolation.getTenantId()));
    }
    
    @GetMapping("updateLogView/{tenantId}/{edgeGatewayId}")
    @Operation(summary = "获取网关ota升级日志" )
    public R updateLogView(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@Parameter(description = "租户ID") @PathVariable Long tenantId,@Parameter(description = "网关ID") @PathVariable Long edgeGatewayId){
        return R.ok(upgradePackageService.updateLogView(edgeGatewayId,tenantId));
    }
    
    @GetMapping("download/{tenantId}/{edgeGatewayId}")
    @Operation(summary = "下载ota升级包" )
    public R downloadUpgradePackage(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@Parameter(description = "租户ID") @PathVariable Long tenantId,@Parameter(description = "网关ID") @PathVariable Long edgeGatewayId){
        //记录开始升级时间
        return R.ok(upgradePackageService.downloadUpgradePackage(edgeGatewayId,tenantId));
    }
    
    @GetMapping("executeUpgrade/{tenantId}/{edgeGatewayId}")
    @Operation(summary = "执行ota升级" )
    public R executeUpgrade(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@Parameter(description = "租户ID") @PathVariable Long tenantId,@Parameter(description = "网关ID") @PathVariable Long edgeGatewayId){
        //更改状态为升级中
        return R.ok(upgradePackageService.executeUpgrade(edgeGatewayId,tenantId));
    }
    
    @PostMapping("batchDownload")
    @Operation(summary = "批量下载ota升级包" )
    public R batchDownload(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@RequestBody List<BatchUpgradePackageDTO> batchUpgradePackageDTOList){
        //记录开始升级时间
        return R.ok(upgradePackageService.batchDownload(batchUpgradePackageDTOList));
    }
    
    @PostMapping("batchExecuteUpgrade")
    @Operation(summary = "批量执行ota升级" )
    public R batchExecuteUpgrade(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@RequestBody List<BatchUpgradePackageDTO> batchUpgradePackageDTOList){
        //更改状态为升级中
        return R.ok(upgradePackageService.batchExecuteUpgrade(batchUpgradePackageDTOList));
    }
}
