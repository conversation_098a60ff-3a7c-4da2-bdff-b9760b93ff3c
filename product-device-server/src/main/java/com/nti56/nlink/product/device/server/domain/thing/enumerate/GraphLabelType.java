package com.nti56.nlink.product.device.server.domain.thing.enumerate;

import lombok.Getter;

public enum GraphLabelType {

    THING_MODEL("THING_MODEL", "THING_MODEL"),
    Device("Device", "Device");

    @Getter
    private String value;

    @Getter
    private String name;


    GraphLabelType(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public static GraphLabelType typeOfValue(String value){
        GraphLabelType[] values = GraphLabelType.values();
        for (GraphLabelType v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static GraphLabelType typeOfName(String name){
        GraphLabelType[] values = GraphLabelType.values();
        for (GraphLabelType v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

}
