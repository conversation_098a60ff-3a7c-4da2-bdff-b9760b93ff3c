package com.nti56.nlink.product.device.server.domain.thing.dpo;

import cn.hutool.core.collection.CollectionUtil;
import com.nti56.nlink.product.device.client.model.dto.json.dpo.EventDpo;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName ModelSelectDpo
 * @date 2022/8/10 11:10
 * @Version 1.0
 */
@Data
@NoArgsConstructor
public class ModelSelectDpo {
    private Long id;
    private String name;
    private String descript;
    private List<String> properties;
    private List<String> events;
    private List<ServiceDpo> services;

    public ModelSelectDpo(ModelDpo modelDpo) {
        this.id = modelDpo.getId();
        this.descript = modelDpo.getDescript();
        this.name = modelDpo.getName();
        this.services = modelDpo.getServices();
        if (CollectionUtil.isNotEmpty(modelDpo.getEvents())) {
            this.events = modelDpo.getEvents().stream().map(EventDpo::getName).collect(Collectors.toList());
        }
        this.properties = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(modelDpo.getProperties())) {
            this.properties.addAll(modelDpo.getProperties().stream().map(PropertyDpo::getName).collect(Collectors.toList()));
        }
    }
}
