package com.nti56.nlink.product.device.server.controller;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.*;
import com.nti56.nlink.product.device.server.entity.AuditLogEntity;
import com.nti56.nlink.product.device.server.entity.SubscriptionEntity;
import com.nti56.nlink.product.device.server.service.AuditLogService;
import com.nti56.nlink.product.device.server.service.ISubscriptionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.function.Function;


/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2022-09-22 15:17:38
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/")
@Tag(name = "订阅模块")
public class AuditLogController {

    @Autowired
    private AuditLogService service;

    @GetMapping("auditLog/page")
    @Operation(summary = "获取分页")
    public R page(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                  PageParam pageParam, AuditLogEntity entity){
        Page<AuditLogEntity> page = pageParam.toPage(AuditLogEntity.class);
        entity.setTenantId(tenantIsolation.getTenantId());
        Result<Page<AuditLogEntity>> result = service.getPage(entity,page);
        return R.result(result);
    }
    

    @GetMapping("auditLog/getLastSync")
    @Operation(summary = "获取上次同步CommonType网关的时间")
    public R getLastSync(){
        return R.result(service.getLastSync());
    }




}
