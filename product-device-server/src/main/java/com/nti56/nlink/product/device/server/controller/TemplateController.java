package com.nti56.nlink.product.device.server.controller;

import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.PageParam;
import com.nti56.nlink.common.util.R;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.enums.AuditEnum;
import com.nti56.nlink.product.device.server.model.template.dto.CreateTemplateDTO;
import com.nti56.nlink.product.device.server.model.template.dto.EditTemplateDTO;
import com.nti56.nlink.product.device.server.model.template.dto.TemplateDTO;
import com.nti56.nlink.product.device.server.service.ITemplateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/3/10 10:38<br/>
 * @since JDK 1.8
 */

@RestController
@RequestMapping("templates")
@Tag( name = "模板模块")
public class TemplateController  {

    @Autowired
    private ITemplateService templateService;

    @PostMapping()
    @Operation(summary = "新增模板" )
    public R createTemplate(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, @Validated @RequestBody CreateTemplateDTO dto){
        return R.result(templateService.createTemplate(dto,tenantIsolation));
    }

    @DeleteMapping("{id}")
    @Operation(summary = "删除模板" )
    public R deleteTemplate(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, @PathVariable Long id){
        return R.result(templateService.deleteTemplate(id,tenantIsolation));
    }

    @PutMapping("{id}")
    @Operation(summary = "修改模板" )
    public R editTemplate(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, @Validated @RequestBody EditTemplateDTO dto){
        return R.result(templateService.editTemplate(dto,tenantIsolation));
    }

    @GetMapping("page")
    @Operation(summary = "分页查询模板消息" )
    public R pageTemplate(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, PageParam pageParam,@Validated TemplateDTO dto){
        return R.result(templateService.pageTemplate(pageParam, dto,tenantIsolation));
    }

    @GetMapping("list")
    @Operation(summary = "查询所有模板消息" )
    public R listTemplate(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@Validated TemplateDTO dto){
        return R.result(templateService.listTemplate(dto,tenantIsolation));
    }

    @GetMapping("{id}")
    @Operation(summary = "根据id查询模板信息" )
    public R getTemplate(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, @PathVariable Long id){
        return R.result(Result.ok(templateService.getByIdAndTenantIsolation(id,tenantIsolation)));
    }


    @GetMapping("auditStatus")
    @Operation(summary = "获取审核状态" )
    public R getAuditStatus(){
        return R.ok(AuditEnum.toList());
    }
}
