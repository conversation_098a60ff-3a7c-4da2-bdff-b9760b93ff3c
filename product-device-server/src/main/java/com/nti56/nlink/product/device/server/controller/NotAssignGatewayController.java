package com.nti56.nlink.product.device.server.controller;

import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.PageParam;
import com.nti56.nlink.common.util.R;
import com.nti56.nlink.product.device.server.model.notAssign.dto.AssignGatewayDto;
import com.nti56.nlink.product.device.server.model.notAssign.dto.CreateGatewayDto;
import com.nti56.nlink.product.device.server.service.INotAssignGatewayService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import java.util.List;

/**
 *
 * 未分配网关
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2023-4-21 11:56:59
 * @since JDK 1.8
 */
@RestController
@RequestMapping("notAssignGateway")
@Tag(name = "未分配网关")
public class NotAssignGatewayController {
    
    @Autowired
    private INotAssignGatewayService notAssignGatewayService;

    @GetMapping("page")
    @Operation(summary = "查询未分配网关列表" )
    public R pageNotAssignGateway(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,PageParam pageParam){
        return R.result(notAssignGatewayService.pageNotAssignGateway(pageParam));
    }

    @PostMapping("assignGateway")
    @Operation(summary = "分配网关" )
    public R assignGateway(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@Valid @RequestBody AssignGatewayDto assignGatewayDto){
        return R.result(notAssignGatewayService.assignGateway(assignGatewayDto));
    }

    @PostMapping("batchCreateGateway")
    @Operation(summary = "批量创建网关" )
    public R batchCreateGateway(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@Valid @RequestBody List<CreateGatewayDto> createGatewayDtoList){
        return R.result(notAssignGatewayService.batchCreateGateway(createGatewayDtoList,tenantIsolation));
    }

}
