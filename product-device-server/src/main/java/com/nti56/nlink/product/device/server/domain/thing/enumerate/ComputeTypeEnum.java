package com.nti56.nlink.product.device.server.domain.thing.enumerate;

import lombok.Getter;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-24 14:06:38
 * @since JDK 1.8
 */
public enum ComputeTypeEnum {
    DEVICE(1, "device", "device"), 
    RULE(3, "rule", "rule")
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    ComputeTypeEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static ComputeTypeEnum typeOfValue(Integer value){
        ComputeTypeEnum[] values = ComputeTypeEnum.values();
        for (ComputeTypeEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static ComputeTypeEnum typeOfName(String name){
        ComputeTypeEnum[] values = ComputeTypeEnum.values();
        for (ComputeTypeEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static ComputeTypeEnum typeOfNameDesc(String nameDesc){
        ComputeTypeEnum[] values = ComputeTypeEnum.values();
        for (ComputeTypeEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }
}
