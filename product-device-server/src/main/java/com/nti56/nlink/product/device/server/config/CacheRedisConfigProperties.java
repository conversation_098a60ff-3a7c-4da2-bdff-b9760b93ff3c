package com.nti56.nlink.product.device.server.config;

import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.List;

/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName CacheRedisConfigProperties
 * @date 2022/7/25 15:50
 * @Version 1.0
 */
@ConfigurationProperties(
        prefix = "redis.config.properties"
)
public class CacheRedisConfigProperties {
    private List<CacheRedisConfigProperties.CacheConfig> configs;

    public CacheRedisConfigProperties() {
    }

    public void setConfigs(List<CacheRedisConfigProperties.CacheConfig> configs) {
        this.configs = configs;
    }

    public List<CacheRedisConfigProperties.CacheConfig> getConfigs() {
        return this.configs;
    }

    public static class CacheConfig {
        private String key;
        private long second = 60L;

        public CacheConfig() {
        }

        public void setKey(String key) {
            this.key = key;
        }

        public void setSecond(long second) {
            this.second = second;
        }

        public String getKey() {
            return this.key;
        }

        public long getSecond() {
            return this.second;
        }
    }
}
