package com.nti56.nlink.product.device.server.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.*;
import com.nti56.nlink.product.device.server.entity.DeviceServiceEntity;
import com.nti56.nlink.product.device.server.service.IDeviceServiceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;
import java.util.function.Function;


/**
 * <p>
 * 设备服务表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2022-04-13 10:28:52
 * @since JDK 1.8
 */
@RestController
@RequestMapping("device-service")
@Tag(name = "设备服务表")
public class DeviceServiceController {

    @Autowired
    IDeviceServiceService service;

    @GetMapping("page")
    @Operation(summary = "获取分页")
    public R page(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                  PageParam pageParam, DeviceServiceEntity entity) {
        Page<DeviceServiceEntity> page = pageParam.toPage(DeviceServiceEntity.class);
        entity.setTenantId(tenantIsolation.getTenantId());
        Result<Page<DeviceServiceEntity>> result = service.getPage(entity, page);
        return R.result(result);
    }

    @GetMapping("list/{deviceId}")
    @Operation(summary = "根据物模型ID获取服务列表")
    public R listByThingModel(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                              @PathVariable Long deviceId) {
        DeviceServiceEntity entity = new DeviceServiceEntity();
        entity.setDeviceId(deviceId);
        entity.setTenantId(tenantIsolation.getTenantId());
        Result<List<DeviceServiceEntity>> list = service.list(entity);
        return R.result(list);
    }

    @GetMapping("list")
    @Operation(summary = "获取列表")
    public R list(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, DeviceServiceEntity entity) {
        entity.setTenantId(tenantIsolation.getTenantId());
        Result<List<DeviceServiceEntity>> result = service.list(entity);
        return R.result(result);
    }

    @PostMapping("")
    @Operation(summary = "创建对象")
    public R create(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@RequestBody DeviceServiceEntity entity
    ) {
        Result<DeviceServiceEntity> result = service.save(tenantIsolation, entity);
        return R.result(result);
    }

    @PutMapping("{entityId}")
    @Operation(summary = "更新")
    public R update(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                    @Parameter(description = "对象") @RequestBody DeviceServiceEntity entity) {
        if (BeanUtilsIntensifier.checkBeanAndProperties(entity, DeviceServiceEntity::getId)) {
            return R.error(ServiceCodeEnum.CODE_PARAM_ERROR);
        }
        Result result = service.update(entity,tenantIsolation);
        return R.result(result);
    }

    @DeleteMapping("{entityId}")
    @Operation(summary = "删除对象")
    public R delete(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                    @Parameter(description = "目标ID") @PathVariable Long entityId) {
        Result result = service.deleteById(entityId,tenantIsolation);
        return R.result(result);
    }

    @DeleteMapping("")
    @Operation(summary = "批量删除对象")
    public R deleteBatchByIds(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                    @Parameter(description = "选中的目标ID") @RequestBody List<Long> ids) {
        if (Objects.isNull(tenantIsolation) || Objects.isNull(tenantIsolation.getTenantId()) || CollectionUtil.isEmpty(ids)) {
            return R.result(ServiceCodeEnum.CODE_PARAM_ERROR);
        }
        Result result = service.deleteBatchByIds(ids,tenantIsolation);
        return R.result(result);
    }

    @GetMapping("{entityId}")
    @Operation(summary = "获取对象")
    public R get(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                 @Parameter(description = "目标ID") @PathVariable Long entityId) {
        Result<DeviceServiceEntity> result = service.getByIdAndTenantIsolation(entityId,tenantIsolation);
        return R.result(result);
    }

    private R checkParamAndDoSomething(DeviceServiceEntity entity, Function<DeviceServiceEntity, Result> func) {
        if (BeanUtilsIntensifier.checkBeanAndProperties(entity, DeviceServiceEntity::getServiceName, DeviceServiceEntity::getAsync,
                DeviceServiceEntity::getDeviceId, DeviceServiceEntity::getOverride)) {
            return R.error(ServiceCodeEnum.CODE_PARAM_ERROR);
        }
        Result result = func.apply(entity);
        return R.result(result);
    }


}
