package com.nti56.nlink.product.device.server.domain.thing.modelbase;


import com.nti56.nlink.common.util.BeanUtilsIntensifier;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.client.model.dto.json.ModelField;
import com.nti56.nlink.product.device.client.model.dto.json.modelfield.DataTypeElm;
import com.nti56.nlink.product.device.client.model.dto.json.modelfield.PropertyElm;
import com.nti56.nlink.product.device.server.domain.thing.dpo.PropertyDpo;
import com.nti56.nlink.product.device.server.domain.thing.dpo.ThingModelDataTypeDpo;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.ModelTypeEnum;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.ReportTypeEnum;
import com.nti56.nlink.product.device.server.domain.thing.thingmodel.ThingModelDataType;
import com.nti56.nlink.product.device.server.model.inherit.PropertyOfInherit;
import com.nti56.nlink.product.device.server.util.RegexUtil;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import java.util.*;
import java.util.Map.Entry;
import java.util.stream.Collectors;

/**
 * 类说明: 物模型属性领域对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-24 16:41:19
 * @since JDK 1.8
 */
@Getter
public class Property {

    //直属设备id
    private Long deviceId;

    //直属设备名称
    private String deviceName;

    //直属物模型id
    private Long thingModelId;

    //直属物模型名称
    private String thingModelName;

    //直属模型id
    private Long directlyModelId;
    //直属模型类型
    private ModelTypeEnum modelType;

    private String name;
    private String descript;

    @Setter
    private String labelName;
    @Setter
    private Long labelId;
    @Setter
    private String labelGroupName;
    @Setter
    private String channelName;

    private ReportTypeEnum reportType;
    private ThingModelDataType dataType;

    private Boolean bindLabel;

    private Boolean readOnly;
    private Boolean persist;
    private Boolean required;


    private PropertyElm raw;

    private Property(){}

    public static Result<Property> checkInfo(
        Long thingModelId,
        String thingModelName,
        Long deviceId,
        String deviceName,
        PropertyElm propertyElm
    ) {
        if(thingModelId == null && deviceId == null){
            return Result.error("直属模型id不能为空");
        }

        if(propertyElm == null){
            return Result.error("属性不能为空");
        }

        String nameStr = propertyElm.getName();
        if(nameStr == null){
            return Result.error("属性名称不能为空");
        }

        Property property = new Property();
        property.thingModelId = thingModelId;
        property.thingModelName = thingModelName;
        property.deviceId = deviceId;
        property.deviceName = deviceName;
        property.descript = propertyElm.getDescript();
        
        property.persist = propertyElm.getPersist();
        property.required = propertyElm.getRequired();

        Boolean readOnly = propertyElm.getReadOnly();
        if(readOnly == null){
            readOnly = false;
        }
        property.readOnly = readOnly;
        
        if (property.thingModelId != null) {
            property.directlyModelId = property.thingModelId;
            property.modelType = ModelTypeEnum.THING_MODEL;
        }else if (property.deviceId != null){
            property.directlyModelId = property.deviceId;
            property.modelType = ModelTypeEnum.DEVICE_MODEL;
        }else{
            return Result.error("模型属性错误，缺少物模型id或设备id");
        }

        //名称
        if(!RegexUtil.checkName(nameStr)){
            return Result.error(
                "属性名称格式错误，只支持英文数字下划线，且英文首字母，name: " + nameStr
            );
        }
        if(nameStr.length() > 128){
            return Result.error(
                    "属性名称长度超过128个字符，name: " + nameStr
            );
        }
        property.name = nameStr;

        //属性类型
        DataTypeElm dataTypeElm = propertyElm.getDataType();
        Result<ThingModelDataType> dataTypeResult = ThingModelDataType.checkInfo(
            dataTypeElm
        );
        if(!dataTypeResult.getSignal()){
            return Result.error(dataTypeResult.getMessage() + ", name:" + nameStr);
        }
        property.dataType= dataTypeResult.getResult();

        //上报类型
        ReportTypeEnum reportTypeEnum = ReportTypeEnum.typeOfValue(propertyElm.getReportType());
        if(reportTypeEnum == null){
            return Result.error("上报类型错误" + ", name:" + nameStr + ", reportType:" + propertyElm.getReportType());
        }
        property.reportType = reportTypeEnum;

        //bindLabel
        Boolean bindLabel = propertyElm.getBindLabel();
        property.bindLabel = bindLabel;

        property.raw = propertyElm;

        return Result.ok(property);
    }

    public static Result<List<Property>> batchCheckInfo(
        Long thingModelId,
        String thingModelName,
        Long deviceId,
        String deviceName,
        ModelField model
    ){
        List<Property> properties = new ArrayList<>();
        if(model != null){
            //自定义属性
            List<PropertyElm> propertyElms = model.getProperties();
            HashSet<Object> set = new HashSet<>();
            BeanUtilsIntensifier.getSomething2Collection(propertyElms,PropertyElm::getName,set);
            List<String> reservedWord = Arrays.asList("id", "name", "description", "status");
            if (set.containsAll(reservedWord)) {
                return Result.error("id,name,description,status为属性保留字，不能添加为自定义属性名称！");
            }
            if(propertyElms != null && propertyElms.size() > 0){
                for(PropertyElm propertyElm:propertyElms){
                    Result<Property> propertyResult = Property.checkInfo(
                        thingModelId, thingModelName, deviceId, deviceName, propertyElm
                    );
                    if(!propertyResult.getSignal()){
                        return Result.error(propertyResult.getMessage() + ", property:" + propertyElm.getName());
                    }
                    properties.add(propertyResult.getResult());
                }
            }
        }
        return Result.ok(properties);
    }
    
    public static Result<List<Property>> checkRepeat(
        List<Property> inheritProperties,
        List<Property> selfProperties
    ){
        
        List<Property> properties = new ArrayList<>();
        if(inheritProperties != null){
            properties.addAll(inheritProperties);
        }
        if(selfProperties != null){
            properties.addAll(selfProperties);
        }

        if(properties.size() > 0){
            //属性名称不能重复
            Map<String, Long> propertyCountMap = properties.stream()
                    .map(Property::getName)
                    .collect(Collectors.groupingBy(
                        p -> p,
                        Collectors.counting()
                    ));
            for(Entry<String, Long> entry:propertyCountMap.entrySet()){
                if(entry.getValue() > 1){
                    return Result.error("模型属性名重复，name:" + entry.getKey());
                }
            }
        }
        return Result.ok(properties);
    }

    public static Property buildByDpo(PropertyDpo propertyDpo) {
        Property property = new Property();
        property.name = propertyDpo.getName();
        property.descript = propertyDpo.getDescript();
        property.bindLabel = false;
        property.dataType = ThingModelDataType.buildByDpo(propertyDpo.getDataType());
        return property;
    }

    public PropertyOfInherit toPropertyOfInherit() {
        PropertyOfInherit p = new PropertyOfInherit();
        BeanUtils.copyProperties(raw, p);
        p.setBaseDeviceId(deviceId);
        p.setBaseDeviceName(deviceName);
        p.setBaseThingModelId(thingModelId);
        p.setBaseThingModelName(thingModelName);
        return p;
    }

    @Override
    public boolean equals(Object obj){
        if (!Optional.ofNullable(obj).isPresent()) {
            return false;
        }
        if (obj instanceof Property) {
            Property property = (Property) obj;

            if (this.thingModelId != null 
                    && (this.thingModelId.equals(property.getThingModelId()) || this.thingModelId.equals(property.deviceId))
                    && this.name.equals(property.name) 
                    && this.dataType.equals(property.dataType) 
                    && this.reportType.equals(property.reportType) 
                    && this.readOnly.equals(property.readOnly)
                    && this.bindLabel.equals(property.bindLabel)) {
                return true;
            }
            if (this.deviceId != null 
                    && (this.deviceId.equals(property.thingModelId) || this.deviceId.equals(property.deviceId))
                    && this.name.equals(property.name) 
                    && this.dataType.equals(property.dataType) 
                    && this.reportType.equals(property.reportType)
                    && this.readOnly.equals(property.readOnly)
                    && this.bindLabel.equals(property.bindLabel)){
                return true;
            }
            return false;
        }
        return false;
    }

    public PropertyDpo toDpo() {
        PropertyDpo dpo = new PropertyDpo();
        dpo.setDeviceId(deviceId);
        dpo.setDeviceName(deviceName);
        dpo.setThingModelId(thingModelId);
        dpo.setThingModelName(thingModelName);
        dpo.setName(name);
        dpo.setBindLabel(bindLabel);
        dpo.setReadOnly(readOnly);
        dpo.setRequired(required);
        dpo.setReportType(reportType.getValue());
        dpo.setPersist(persist);
        ThingModelDataTypeDpo dataTypeDpo = dataType.toDpo();
        dpo.setDataType(dataTypeDpo);
        dpo.setDescript(descript);
        dpo.setLabelId(labelId);
        dpo.setLabelName(labelName);
        dpo.setChannelName(channelName);
        dpo.setLabelGroupName(labelGroupName);
        return dpo;
    }

}
