package com.nti56.nlink.product.device.server.controller;

import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.BeanUtilsIntensifier;
import com.nti56.nlink.common.util.R;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.nlink.product.device.server.entity.LabelBindRelationEntity;
import com.nti56.nlink.product.device.server.model.AdditionBindPreviewDto;
import com.nti56.nlink.product.device.server.model.AdditionLabelBindRelationDto;
import com.nti56.nlink.product.device.server.model.LabelBindRelationBo;
import com.nti56.nlink.product.device.server.service.ILabelBindRelationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.function.Function;


/**
 * <p>
 * 标签绑定关系表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2022-04-11 17:27:49
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/label-bind-relation/")
@Tag(name = "标签绑定关系表")
public class LabelBindRelationController {

    @Autowired
    ILabelBindRelationService relationService;

//    @PostMapping("{deviceId}")
//    @Operation(summary = "保存设备标签绑定关系")
//    public R create(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, @PathVariable("deviceId") Long deviceId , @Parameter(description = "对象") @RequestBody List<LabelBindRelationEntity> relations) {
//        Result<Map<String,ServiceCodeEnum>> result = relationService.saveList(tenantIsolation.getTenantId(),deviceId, relations);
//        return R.result(result);
//    }

    @GetMapping("list/{deviceId}")
    @Operation(summary = "获取列表")
    public R list(@PathVariable("deviceId") Long deviceId,@RequestHeader("ot_headers") TenantIsolation tenantIsolation) {
        Result<List<LabelBindRelationBo>> result = relationService.list(deviceId,tenantIsolation.getTenantId());
        return R.result(result);
    }

    @PostMapping("")
    @Operation(summary = "创建对象")
    public R create(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@Parameter(description = "对象") @RequestBody LabelBindRelationEntity entity) {
        return checkParamAndDoSomething(tenantIsolation,entity, relationService::createLabelBindRelation);
    }

    @GetMapping("list/label/{channelId}/{labelGroupName}/{labelName}/{edgeGatewayId}")
    @Operation(summary = "根据网关ID、绝对名称获取绑定关系")
    public R getByLabelId(
            @RequestHeader("ot_headers") TenantIsolation tenantIsolation,@Parameter(description = "通道Id") @PathVariable("channelId") Long channelId,
            @Parameter(description = "网关Id") @PathVariable("edgeGatewayId") Long edgeGatewayId,@Parameter(description = "标签名称") @PathVariable("labelName") String labelName,@Parameter(description = "分组名称") @PathVariable("labelGroupName") String labelGroupName) {
        if (ObjectUtils.isEmpty(edgeGatewayId) || ObjectUtils.isEmpty(channelId) || ObjectUtils.isEmpty(labelName) || ObjectUtils.isEmpty(labelGroupName)) {
            return R.error(ServiceCodeEnum.CODE_PARAM_ERROR);
        }
        Result result = relationService.getByAbsoluteLabelName(labelGroupName,labelName,edgeGatewayId,channelId,tenantIsolation);
        return R.result(result);
    }

    @DeleteMapping("")
    @Operation(summary = "删除对象")
    public R batchDelete(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@Parameter(description = "目标ID")@RequestBody  List<Long> ids) {
        Result<Integer> result = relationService.batchDelete(ids, tenantIsolation.getTenantId());
        return R.result(result);
    }

    private R checkParamAndDoSomething(TenantIsolation tenantIsolation,LabelBindRelationEntity entity, Function<LabelBindRelationEntity, Result> func) {
        if (BeanUtilsIntensifier.checkBeanAndProperties(tenantIsolation, TenantIsolation::getTenantId,TenantIsolation::getEngineeringId,TenantIsolation::getModuleId,TenantIsolation::getSpaceId)) {
            return R.error(ServiceCodeEnum.CODE_PARAM_ERROR);
        }
        if (BeanUtilsIntensifier.checkBeanAndProperties(entity, LabelBindRelationEntity::getLabelId, LabelBindRelationEntity::getDeviceId, LabelBindRelationEntity::getDirectlyModelId, LabelBindRelationEntity::getPropertyName)) {
            return R.error(ServiceCodeEnum.CODE_PARAM_ERROR);
        }
        BeanUtilsIntensifier.propertyInjection(tenantIsolation,entity);
        Result result = func.apply(entity);
        return R.result(result);
    }

    @PostMapping("/additionAddPreview")
    @Operation(summary = "设备追加标签绑定预览")
    public R additionAddPreview(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@Parameter(description = "对象") @RequestBody AdditionLabelBindRelationDto bindRelationDto) {
        return R.result(relationService.additionAddPreview(tenantIsolation,bindRelationDto));
    }

    @PostMapping("/additionAdd")
    @Operation(summary = "设备追加标签绑定")
    public R additionAdd(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@Parameter(description = "对象") @RequestBody List<AdditionBindPreviewDto> bindRelationDto) {
        return R.result(relationService.additionAdd(tenantIsolation,bindRelationDto));
    }


}
