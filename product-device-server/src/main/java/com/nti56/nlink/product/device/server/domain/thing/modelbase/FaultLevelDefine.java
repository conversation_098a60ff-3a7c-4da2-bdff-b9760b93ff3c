package com.nti56.nlink.product.device.server.domain.thing.modelbase;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.client.model.dto.json.condition.TriggerConditionElm;
import com.nti56.nlink.product.device.client.model.dto.json.enumerate.ThingDataTypeEnum;
import com.nti56.nlink.product.device.client.model.dto.json.enumerate.WarningLevelEnum;
import com.nti56.nlink.product.device.client.model.dto.json.modelfield.FaultLevelDefineElm;
import com.nti56.nlink.product.device.server.domain.thing.up.UpProp;
import lombok.Getter;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 类说明：
 *
 * @ClassName FaultLevelDefine
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/2/8 17:24
 * @Version 1.0
 */

public class FaultLevelDefine {

    @Getter
    private FaultLevelDefineElm levelDefine;


    public static Result<FaultLevelDefine> checkInfo(FaultLevelDefineElm faultLevelDefineElm) {

        Map<String, List<TriggerConditionElm>> levelTriggerMap = faultLevelDefineElm.getLevelDefine();
        if(MapUtil.isEmpty(levelTriggerMap)){
            return Result.error("故障等级规则不能为空！");
        }
        for (Map.Entry<String, List<TriggerConditionElm>> stringListEntry : levelTriggerMap.entrySet()) {
            Stack<String> stack = new Stack<>();
            for (TriggerConditionElm triggerConditionElm : stringListEntry.getValue()) {
                if (StringUtils.isBlank(triggerConditionElm.getType())) {
                    return Result.error("条件类型为空");
                }
                if (Objects.isNull(triggerConditionElm.getLevel())) {
                    return Result.error("条件层级为空");
                }
                if (StringUtils.isNotBlank(triggerConditionElm.getLogic())) {
                    if (!Lists.newArrayList("and", "or").contains(triggerConditionElm.getLogic())) {
                        return Result.error("条件逻辑符错误");
                    }
                }
                switch (triggerConditionElm.getType()) {
                    case "compare":
                        if (triggerConditionElm.getLeft() == null) {
                            return Result.error("条件属性为空");
                        }
                        if (StringUtils.isBlank(triggerConditionElm.getOperator())) {
                            return Result.error("条件为空");
                        }
                        if (triggerConditionElm.getRight() == null) {
                            return Result.error("条件值为空");
                        }
                        ThingDataTypeEnum thingDataTypeEnum = ThingDataTypeEnum.typeOfName(triggerConditionElm.getPropteType());
                        if(Objects.isNull(thingDataTypeEnum)){
                            return Result.error("属性类型不支持或为空");
                        }
                        break;
                    case "bracket":
                        if (StringUtils.isBlank(triggerConditionElm.getBracket())) {
                            return Result.error("括号为空");
                        }
                        if (StringUtils.equals(triggerConditionElm.getBracket(), "left")) {
                            stack.push(triggerConditionElm.getBracket());
                        }
                        if (StringUtils.equals(triggerConditionElm.getBracket(), "right")) {
                            if (!StringUtils.equals("left", stack.pop())) {
                                return Result.error("括号不配对");
                            }
                        }
                        if (StringUtils.isNotBlank(triggerConditionElm.getLogic())
                                && !StringUtils.equals(triggerConditionElm.getBracket(), "right")) {
                            return Result.error("触发条件逻辑符错误");
                        }
                        break;
                    case "single":
                        if (triggerConditionElm.getSingle() == null) {
                            return Result.error("事件为空");
                        }
                        if (Objects.isNull(triggerConditionElm.getTtl())) {
                            return Result.error("有效时间为空");
                        }
                        break;
                    default:
                        return Result.error("未知比较类型");
                }
            }
            if (stack.size() > 0) {
                return Result.error("括号不配对");
            }
        }
        FaultLevelDefine faultLevelDefine = new FaultLevelDefine();
        faultLevelDefine.levelDefine = faultLevelDefineElm;
        return Result.ok(faultLevelDefine);
    }


    public static Integer getFaultLevel(Map<String, List<TriggerConditionElm>> levelDefine, List<UpProp> prop) {

        if(MapUtil.isEmpty(levelDefine)){
            return WarningLevelEnum.FATAL_WARNING.getCode();
        }
        List<WarningLevelEnum> collect = Arrays.stream(WarningLevelEnum.values()).sorted(((o1, o2) -> o2.getCode() - o1.getCode())).collect(Collectors.toList());
        ExpressionParser parser = new SpelExpressionParser();
        Map<String, Object> propMap = prop.stream().collect(Collectors.toMap(UpProp::getProperty, UpProp::getValue, (key1, key2) -> key2));
        for (WarningLevelEnum warningLevelEnum : collect) {
            String triggerCondition = constructCondition( levelDefine.get(warningLevelEnum.getTypeName()));
            if(StrUtil.isBlank(triggerCondition)){
                continue;
            }
            EvaluationContext context = new StandardEvaluationContext();
            context.setVariable("map", propMap);
            Boolean trigger = parser.parseExpression(triggerCondition).getValue(context, Boolean.class);
            if(trigger){
                return warningLevelEnum.getCode();
            }
        }
        //默认返回该级别
        return WarningLevelEnum.FATAL_WARNING.getCode();
    }

    public static Integer getFaultLevelByCondition(Map<String, String> triggerMaps, List<UpProp> prop) {

        if(MapUtil.isEmpty(triggerMaps)){
            return WarningLevelEnum.FATAL_WARNING.getCode();
        }
        List<WarningLevelEnum> collect = Arrays.stream(WarningLevelEnum.values()).sorted(((o1, o2) -> o2.getCode() - o1.getCode())).collect(Collectors.toList());
        ExpressionParser parser = new SpelExpressionParser();
        Map<String, Object> propMap = prop.stream().collect(Collectors.toMap(UpProp::getProperty, UpProp::getValue, (key1, key2) -> key2));
        for (WarningLevelEnum warningLevelEnum : collect) {
            String triggerCondition= triggerMaps.get(warningLevelEnum.getTypeName());
            if(StrUtil.isBlank(triggerCondition)){
                continue;
            }
            EvaluationContext context = new StandardEvaluationContext();
            context.setVariable("map", propMap);
            Boolean trigger = parser.parseExpression(triggerCondition).getValue(context, Boolean.class);
            if(trigger){
                return warningLevelEnum.getCode();
            }
        }
        //默认返回该级别
        return WarningLevelEnum.FATAL_WARNING.getCode();
    }

    public static String constructCondition(List<TriggerConditionElm> conditionList) {
        if(CollectionUtil.isEmpty(conditionList)){
            return "";
        }
        StringBuilder sb = new StringBuilder();
        for (TriggerConditionElm condition : conditionList) {
            switch (condition.getType()) {
                case "compare":
                    if (StringUtils.isNotBlank(condition.getLeft().getProperty())) {
                        String key = String.format("'%s'", condition.getLeft().getProperty());
                        sb.append("#map[").append(key).append("] ");
                    }
                    if (StringUtils.equals(condition.getOperator().trim(), "=")) {
                        condition.setOperator("==");
                    }
                    sb.append(" ").append(condition.getOperator()).append(" ");
                    sb.append(condition.getRight().getValue()).append(" ");
                    if (StringUtils.isNotBlank(condition.getLogic())) {
                        sb.append(" ").append(condition.getLogic()).append(" ");
                    }
                    break;
                case "bracket":
                    if (StringUtils.equals(condition.getBracket(), "left")) {
                        sb.append("( ");
                    }
                    if (StringUtils.equals(condition.getBracket(), "right")) {
                        sb.append(" )");
                        if (StringUtils.isNotBlank(condition.getLogic())) {
                            sb.append(" ").append(condition.getLogic()).append(" ");
                        }
                    }
                    break;
                case "single":
                    /*if (!Objects.isNull(condition.getWasTrigger())) {
                        if (!condition.getWasTrigger()) {
                            sb.append("!");
                        }
                    }*/
                    sb.append("#map['")
                            .append(condition.getSingle().getEvent())
                            .append("'] ");
                    if (StringUtils.isNotBlank(condition.getLogic())) {
                        sb.append(" ").append(condition.getLogic()).append(" ");
                    }
                    break;
                default:
                    throw new IllegalArgumentException(condition.getType() + "触发条件类型未定义");
            }
        }
        return sb.toString();
    }

    public static void main(String[] args) {

        String triggerConfig = "[\n" +
                "{\n" +
                "\"left\":{\n" +
                "\"property\":\"DM\"\n" +
                "},\n" +
                "\"type\":\"compare\",\n" +
                "\"level\":0,\n" +
                "\"logic\":\"\",\n" +
                "\"right\":{\n" +
                "\"value\":\"20\"\n" +
                "},\n" +
                "\"isArray\":false,\n" +
                "\"operator\":\">\",\n" +
                "\"propteType\":\"Short\",\n" +
                "\"isPropteType\":true,\n" +
                "\"propertyType\":\"SHORT\"\n" +
                "}\n" +
                "]";
        FaultLevelDefineElm faultLevelDefineElm = new FaultLevelDefineElm();
        Map<String, List<TriggerConditionElm>> levelDefine = Maps.newHashMap();
        List<TriggerConditionElm> ld = JSONUtil.toList(triggerConfig,TriggerConditionElm.class);
        levelDefine.put(WarningLevelEnum.ALERT_WARNING.getTypeName(),ld);
        faultLevelDefineElm.setLevelDefine(levelDefine);
        faultLevelDefineElm.setEnableDefine(true);
        Result<FaultLevelDefine> faultLevelDefineResult = FaultLevelDefine.checkInfo(faultLevelDefineElm);
        UpProp upProp = new UpProp();
        upProp.setProperty("DM");
        upProp.setValue(60);
        Integer faultLevel = FaultLevelDefine.getFaultLevel(levelDefine, Lists.newArrayList(upProp));
        System.out.println(faultLevel);
    }

}
