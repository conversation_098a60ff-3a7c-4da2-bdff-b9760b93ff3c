package com.nti56.nlink.product.device.server.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.*;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.DriverEnum;
import com.nti56.nlink.product.device.server.entity.ChannelParamEntity;
import com.nti56.nlink.product.device.server.model.ChannelDto;
import com.nti56.nlink.product.device.server.model.ChannelParamDto;
import com.nti56.nlink.product.device.server.model.ChannelRequestBo;
import com.nti56.nlink.product.device.server.model.LabelGroupDto;
import com.nti56.nlink.product.device.server.model.channel.dto.CopyChannelDTO;
import com.nti56.nlink.product.device.server.model.channel.dto.CreateChannelDTO;
import com.nti56.nlink.product.device.server.model.channel.dto.EditChannelDTO;
import com.nti56.nlink.product.device.server.model.channel.dto.ListChannelWithLabelGroupTreeAndLabelDTO;
import com.nti56.nlink.product.device.server.model.channel.vo.*;
import com.nti56.nlink.product.device.server.service.IChannelParamService;
import com.nti56.nlink.product.device.server.service.IChannelService;
import com.nti56.nlink.product.device.server.service.ILabelGroupService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 类说明: 通道controller
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 13:32:04
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/")
@Tag(name = "通道模块")
public class ChannelController  {

    @Lazy
    @Autowired
    private IChannelService channelService;

    @Autowired
    private IChannelParamService channelParamService;

    @Autowired
    private ILabelGroupService labelGroupService;

    @GetMapping("channel/page")
    @Operation(summary = "获取通道分页")
    public R pageChannel(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, PageParam pageParam, ChannelDto channelDto){
        Page<ChannelDto> page = pageParam.toPage(ChannelDto.class);
        Result<Page<ChannelDto>> result = channelService.getChannelPage(channelDto,page,tenantIsolation);
        return  R.result(result);
    }

    @GetMapping("channel/list/no-edge-gateway")
    @Operation(summary = "获取没有绑定网关的通道" )
    public R listChannel(@RequestHeader("ot_headers") TenantIsolation tenantIsolation){
        Result<List<ChannelVO>> result = channelService.listChannelNoEdgeGateway(tenantIsolation);
        return R.result(result);
    }

    @GetMapping("channel/channel-param/{channelId}")
    @Operation(summary = "根据通道获取参数")
    public R getChannelParamByChannel(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                                      @Parameter(description = "通道ID") @PathVariable String channelId){
        Result result = BeanUtilsIntensifier.stringToId(channelId);
        if (result.getSignal()) {
            ChannelParamDto channelParamDto = BeanUtilsIntensifier.copyBean(ChannelParamEntity.builder().channelId(Long.valueOf(channelId)).build(),ChannelParamDto.class);
            result = channelParamService.listChannelParam(channelParamDto,tenantIsolation);
        }
        return R.result(result);
    }

    @GetMapping("channel/label-group/{channelId}")
    @Operation(summary = "根据通道获取标签分组")
    public R getLabelGroupByChannel(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                                    @Parameter(description = "通道ID") @PathVariable Long channelId){
        Result<List<LabelGroupDto>> listResult = labelGroupService.listLabelGroupByChannelId(channelId, tenantIsolation);
        return R.result(listResult);
    }

    @GetMapping("channel/driverType")
    @Operation(summary = "获取通道类型")
    public R getDriverType(){
        List<Map<String,Object>> list = DriverEnum.toList();
        return R.ok(list);
    }


    @DeleteMapping("channel/{id}")
    @Operation(summary = "根据id删除通道")
    public R deleteChannelAll(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                              @PathVariable Long id){
        return R.result(channelService.deleteChannel(id,tenantIsolation));
    }
    
    @GetMapping("channel/connect-test/{edgeGatewayId}")
    @Operation(summary = "根据网关id,获取网关下的通道信息列表" )
    public R channelConnectTestList(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@PathVariable Long edgeGatewayId){
        Result<List<ChannelCheckoutOutVO>> result = channelService.channelConnectTestList(tenantIsolation,edgeGatewayId);
        return R.result(result);
    }

    @PutMapping("channel/{channelId}/edgeGateway/{edgeGatewayId}")
    @Operation(summary = "绑定通道网关")
    public R bingEdgeGateway(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                              @PathVariable Long channelId,@PathVariable Long edgeGatewayId){
        return R.result(channelService.bingEdgeGateway(channelId,edgeGatewayId,tenantIsolation));
    }

    @PostMapping(value="channel/connect-test")
    @Operation(summary = "通道连接测试")
    public R connectTest(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                         @RequestBody List<Long> channelIds) {
        return R.ok(channelService.connectTest(tenantIsolation,channelIds));
    }

    @PostMapping("channel")
    @Operation(summary = "新增通道")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200",description = "返回值",
                    content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = CreateChannelAllVO.class)
                    )})
    })
    public R createChannel(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                              @RequestBody @Validated CreateChannelDTO dto){
        return R.result(channelService.createChannel(dto,tenantIsolation));
    }


    @PutMapping("channel/{id}")
    @Operation(summary = "修改通道")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200",description = "返回值",
                    content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = EditChannelAllVO.class)
                    )})
    })
    public R editChannel(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                            @RequestBody @Validated EditChannelDTO dto){
        return R.result(channelService.editChannel(dto,tenantIsolation));
    }

    @PutMapping("channel/status/{id}/{edgeGatewayId}")
    @Operation(summary = "通道状态控制")
    public R statusChange(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@PathVariable("id") Long id,@PathVariable("edgeGatewayId") Long edgeGatewayId){
        return R.result(channelService.statusChange(id, edgeGatewayId,tenantIsolation));
    }

    @PutMapping("channel/modify/{id}/{edgeGatewayId}/{intervalMs}")
    @Operation(summary = "通道修改轮询间隔")
    public R modifyChannelInterval(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                                   @PathVariable("id") Long id,
                                   @PathVariable("intervalMs") Integer intervalMs,
                                   @PathVariable("edgeGatewayId") Long edgeGatewayId
    ){
        return R.result(channelService.modifyChannelInterval(id, edgeGatewayId, intervalMs, tenantIsolation));
    }

    @GetMapping("channel/{id}")
    @Operation(summary = "根据id获取通道")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200",description = "返回值",
                    content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = QueryChannelByIdVO.class)
                    )})
    })
    public R getChannel(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                           @PathVariable Long id){
        return R.result(channelService.getChannel(id,tenantIsolation));
    }

    @GetMapping("channel/debug-info/{id}")
    @Operation(summary = "获取通道调试信息")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200",description = "返回值",
                    content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = QueryChannelByIdVO.class)
                    )})
    })
    public R getChannelDebugInfo(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                           @PathVariable Long id){
        return R.result(channelService.getChannelDebugInfo(id,tenantIsolation));
    }



    @GetMapping("channel/all/{id}")
    @Operation(summary = "根据id获取通道")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200",description = "返回值",
                    content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = QueryChannelAllByIdVO.class)
                    )})
    })
    public R getChannelAll(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                           @PathVariable Long id){
        return R.result(channelService.getChannelAll(id,tenantIsolation));
    }

    @PostMapping("channel/edgeGateway/{edgeGatewayId}")
    @Operation(summary = "根据网关id获取通道" )
    public R listChannelWithLabelGroupTreeAndLabel(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                                        @RequestBody @Validated ListChannelWithLabelGroupTreeAndLabelDTO dto){
        return R.result(channelService.listChannelWithLabelGroupTreeAndLabel(dto,tenantIsolation));
    }


    @PostMapping("channel/copy")
    @Operation(summary = "复制通道")
    public R copyChannel(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                           @RequestBody @Validated CopyChannelDTO dto){
        return R.result(channelService.copyChannel(dto,tenantIsolation));
    }

    @PostMapping(value="channel/status/{edgeGatewayId}")
    @Operation(summary = "通道连接测试")
    public R channelStatus(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@PathVariable Long edgeGatewayId) {
        return R.ok(channelService.channelStatus(edgeGatewayId,tenantIsolation));
    }

    @PostMapping(value="channel/batch/edit/{edgeGatewayId}")
    @Operation(summary = "通道批量操作")
    public R batchEditChannel(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, @RequestBody ChannelRequestBo requestBo,@PathVariable("edgeGatewayId") Long edgeGatewayId){
        return R.ok(channelService.batchEditChannel(tenantIsolation,requestBo,edgeGatewayId));
    }
}
