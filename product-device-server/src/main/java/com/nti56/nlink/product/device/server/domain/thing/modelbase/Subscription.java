package com.nti56.nlink.product.device.server.domain.thing.modelbase;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.common.util.ServiceCodeEnum;
import com.nti56.nlink.product.device.client.model.dto.json.dpo.SubscriptionDpo;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.SubscriptionEventTypeEnum;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.SubscriptionFromEnum;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.ThingServiceCallTypeEnum;
import com.nti56.nlink.product.device.server.domain.thing.up.UpProp;
import com.nti56.nlink.product.device.server.entity.DeviceServiceLogEntity;
import com.nti56.nlink.product.device.server.entity.SubscriptionEntity;
import com.nti56.nlink.product.device.server.exception.BizException;
import com.nti56.nlink.product.device.server.model.inherit.SubscriptionOfInherit;
import com.nti56.nlink.product.device.server.service.ISubscriptionService;
import com.nti56.nlink.product.device.server.serviceEngine.DataConversionService;
import com.nti56.nlink.product.device.server.util.ApplicationContextUtil;
import jdk.nashorn.api.scripting.ScriptObjectMirror;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.util.StringUtils;
import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 类说明: 物模型订阅领域对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-24 16:41:31
 * @since JDK 1.8
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Slf4j
public class Subscription implements Serializable {
    
    private Long id;

    private String name;

    private String descript;

    private String properties;

    private String events;

    private Integer eventType;

    private Long callbackId;

    private Long directlyModelId;

    private Integer modelType;

    private Boolean sendOneByOne;

    private Integer noChangeSeconds;

    private Integer outType;

    private Long targetDevice;

    private String targetService;

    private String receivers;

    private Boolean enable;

    private String dataConversionCode;

    private SubscriptionEntity raw;

    //直属物模型id
    private Long thingModelId;

    //直属物模型名称
    private String thingModelName;

    @Setter
    private EventData event;
    
    private String fromId;
    
    public static Result<Subscription> checkInfo(Long thingModelId,
                                                 String thingModelName,
                                                 SubscriptionEntity subscriptionEntity
    ) {

        if(subscriptionEntity == null){
            return Result.error("订阅不能为空");
        }

        Long id = subscriptionEntity.getId();
        if(id== null){
            return Result.error("订阅id不能为空");
        }

        Subscription subscription = new Subscription();

        subscription.id = subscriptionEntity.getId();

        String nameStr = subscriptionEntity.getName();
        if(StringUtils.isEmpty(nameStr)){
            return Result.error("订阅名称不能为空");
        }
        subscription.name = nameStr;

        if (ObjectUtil.isEmpty(subscriptionEntity.getSendOneByOne())){
            subscription.sendOneByOne = false;
        }else {
            subscription.sendOneByOne = subscriptionEntity.getSendOneByOne();
        }
        if (ObjectUtil.isEmpty(subscriptionEntity.getEnable())){
            subscription.enable = true;
        }else {
            subscription.enable = subscriptionEntity.getEnable();
        }
        if (ObjectUtil.isEmpty(subscriptionEntity.getOutType())) {
            subscription.outType = 0;
        }else {
            subscription.outType = subscriptionEntity.getOutType();
        }
        subscription.targetDevice = subscriptionEntity.getTargetDevice();
        subscription.descript = subscriptionEntity.getDescript();
        subscription.targetService = subscriptionEntity.getTargetService();
        subscription.receivers = subscriptionEntity.getReceivers();
        Integer eventType = subscriptionEntity.getEventType();
        if (eventType == null) {
            return Result.error("事件类型不能为空");
        }
        subscription.eventType = eventType;

        String properties = subscriptionEntity.getProperties();
        if (SubscriptionEventTypeEnum.DATA_CHANGE.getValue().equals(subscriptionEntity.getEventType()) && StringUtils.isEmpty(properties)) {
            return Result.error("属性不能为空");
        }
        subscription.properties = properties;

        String events = subscriptionEntity.getEvents();
        if ((SubscriptionEventTypeEnum.GATEWAY_EVENT.getValue().equals(subscriptionEntity.getEventType())  || SubscriptionEventTypeEnum.CHANNEL_EVENT.getValue().equals(subscriptionEntity.getEventType()) || SubscriptionEventTypeEnum.FAULT.getValue().equals(subscriptionEntity.getEventType())|| SubscriptionEventTypeEnum.ERROR.getValue().equals(subscriptionEntity.getEventType()))&& StringUtils.isEmpty(events)) {
            return Result.error("事件不能为空");
        }
        subscription.events = events;

        subscription.callbackId = subscriptionEntity.getCallbackId();
        subscription.directlyModelId = subscriptionEntity.getDirectlyModelId();
        subscription.modelType = subscriptionEntity.getModelType();
        Result<Void> result = checkDataConversionCode(subscriptionEntity.getDataConversionCode());
        if (!result.getSignal()) {
            return Result.error(ServiceCodeEnum.SUBSCRIPTION_CODE_ERROR,result.getMessage());
        }
        subscription.dataConversionCode = subscriptionEntity.getDataConversionCode();
        subscription.raw = subscriptionEntity;
        subscription.thingModelId = thingModelId;
        subscription.thingModelName = thingModelName;
        subscription.fromId = subscriptionEntity.getFromId();
        subscription.noChangeSeconds = subscriptionEntity.getNoChangeSeconds();
        if(SubscriptionEventTypeEnum.NO_CHANGE.getValue().equals(eventType) && subscription.noChangeSeconds == null){
            return Result.error(ServiceCodeEnum.SUBSCRIPTION_CODE_ERROR,"不改变事件的未改变时长不能为空");
        }
        return Result.ok(subscription);
    }

    public static Result<Void> checkDataConversionCode(String code){
        ScriptEngine nashorn = new ScriptEngineManager().getEngineByName("nashorn");
        try {
            DataConversionService.loadCode(DataConversionService.initServiceName(null,code),nashorn);
        }catch (BizException e){
            return Result.error(e.getMessage());
        }
        return Result.ok();
    }

    
    public static Result<List<Subscription>> batchCheckInfo(Long thingModelId,
                                                            String thingModelName,
                                                            List<SubscriptionEntity> subscriptionEntities
    ){
        List<Subscription> subscriptions = new ArrayList<>();
        if(subscriptionEntities != null && subscriptionEntities.size() > 0){
            //自定义订阅
            for(SubscriptionEntity subscriptionEntity:subscriptionEntities){
                Result<Subscription> subscriptionResult = Subscription.checkInfo(thingModelId,thingModelName,subscriptionEntity);
                if(!subscriptionResult.getSignal()){
                    return Result.error(subscriptionResult.getMessage());
                }
                subscriptions.add(subscriptionResult.getResult());
            }
        }
        return Result.ok(subscriptions);
    }

    public static Result<List<Subscription>> checkRepeat(
        List<Subscription> inheritSubscriptions,
        List<Subscription> selfSubscriptions
    ){
        List<Subscription> result = new ArrayList<>();
        //订阅名称不能重复
        Set<String> nameSet = new HashSet<>();
        if (CollectionUtil.isNotEmpty(inheritSubscriptions)){
            for (Subscription inheritSubscription : inheritSubscriptions) {
                if (!nameSet.add(inheritSubscription.name)){
                    return Result.error("物模型订阅名重复，name:"+inheritSubscription.name);
                }
            }
            result.addAll(inheritSubscriptions);
        }

        if (CollectionUtil.isNotEmpty(selfSubscriptions)){
            for (Subscription selfSubscription : selfSubscriptions) {
                if (!nameSet.add(selfSubscription.name)){
                    return Result.error("物模型订阅名重复，name:"+selfSubscription.name);
                }
            }
            result.addAll(selfSubscriptions);
        }
        

        return Result.ok(result);
    }


    public SubscriptionOfInherit toSubscriptionOfInherit() {
        SubscriptionOfInherit e = new SubscriptionOfInherit();
        BeanUtils.copyProperties(raw, e);
        e.setBaseThingModelId(thingModelId);
        e.setBaseThingModelName(thingModelName);
        return e;
    }


    public SubscriptionDpo toDpo() {
        SubscriptionDpo dpo = new SubscriptionDpo();
        dpo.setId(id);
        dpo.setThingModelId(thingModelId);
        dpo.setThingModelName(thingModelName);
        dpo.setName(name);
        dpo.setDescript(descript);
        dpo.setProperties(properties);
        dpo.setEvents(events);
        dpo.setEventType(eventType);
        dpo.setCallbackId(callbackId);
        dpo.setDirectlyModelId(directlyModelId);
        dpo.setDataConversionCode(dataConversionCode);
        dpo.setModelType(modelType);
        dpo.setSendOneByOne(sendOneByOne);
        dpo.setOutType(outType);
        dpo.setTargetDevice(targetDevice);
        dpo.setTargetService(targetService);
        dpo.setReceivers(receivers);
        dpo.setEnable(enable);
        return dpo;
    }

    public void subscriptionPostProcess(SubscriptionFromEnum from, Long deviceId, Map<String, Object> actual,Long timestamp,SubscriptionEventTypeEnum eventType) {
        if(actual != null){
            actual.put("from", from.getName());
        }
        log.debug("触发订阅后置处理：deviceId:{},subscriptionId:{},event:{}",deviceId,id, event);
        if (ObjectUtil.isNotEmpty(sendOneByOne) && sendOneByOne) {
            event.eventData.forEach(upProp -> {
                List<UpProp> list = new ArrayList<>();
                list.add(upProp);
                Result<ScriptObjectMirror> result = DataConversionService.dataConversion(id,actual, name, timestamp,
                        EventData.builder()
                                .eventData(list)
                                .eventDesc(event.eventDesc)
                                .eventTime(event.eventTime)
                                .eventName(event.eventName)
                                .faultData(event.faultData)
                                .build(),1);
    
                doCallback(result,deviceId,raw.getTenantId());
            });
        }else {
            //me,eventName,eventTime,eventData
            Result<ScriptObjectMirror> result = DataConversionService.dataConversion(id,actual, name, timestamp, event,1);
            doCallback(result,deviceId,raw.getTenantId());
        }
    }

    private void doCallback(Result<ScriptObjectMirror> result,Long deviceId,Long tenantId) {
        if (!result.getSignal()) {
            log.warn("订阅触发失败，失败信息：{}",result.getMessage());
            return;
        }
        ISubscriptionService subscriptionService = ApplicationContextUtil.getBean("subscriptionServiceImpl", ISubscriptionService.class);
        if (ObjectUtil.isEmpty(outType)) outType = 0;
        switch (outType){
            case 1:
                if (ObjectUtil.isEmpty(targetDevice) || targetDevice == 0) {
                    targetDevice = deviceId;
                }
                log.debug("订阅数据转换成功，执行服务：{}.{}，入参：{}，tenant:{}",targetDevice,targetService,result.getResult(),tenantId);
                DeviceServiceLogEntity logEntity = DeviceServiceLogEntity.builder().createTime(LocalDateTime.now()).deviceId(targetDevice).serviceName(targetService).callType(ThingServiceCallTypeEnum.SUBSCRIPTION_INVOKE.getValue()).creatorId(deviceId).creator(name).build();
                subscriptionService.invokeDeviceService(tenantId,targetDevice,targetService,result.getResult(),logEntity,false);
                break;
            case 0:
                log.debug("订阅数据转换成功，执行回调：{}，消息：{},tenant:{}",callbackId,result.getResult(),tenantId);
                subscriptionService.execRedirectWithPayload(tenantId, callbackId, JSON.toJSONString(result.getResult()));
                break;
            case 2:
                if(tenantId != null && tenantId.equals(1623216996149927937L)){
                    log.info("subscribe-efefe {}", result.getResult());
                }
                log.debug("订阅数据转换成功，发送消息，templateId：{}，receivers:{}，消息：{},tenant:{}",callbackId,receivers,result.getResult(),tenantId);
                subscriptionService.sendNotifies(tenantId,id,callbackId,receivers,JSON.toJSONString(result.getResult()));
                break;
            default:
        }
    }
    
    
    public void edgeGatewaySubscriptionPostProcess(SubscriptionFromEnum from, Long sourceId,Long tenantId, Map<String, Object> actual,Long timestamp,Integer outType) {
        if(actual != null){
            actual.put("from", from.getName());
        }
        log.debug("触发网关订阅后置处理：sourceId:{},subscriptionId:{},event:{}",sourceId,id, event);
        //me,eventName,eventTime,eventData
        Result<ScriptObjectMirror> result = DataConversionService.dataConversion(id,actual, name, timestamp, event,1);
        doEdgeGatewayCallback(result,outType,tenantId);
    }
    
    
    private void doEdgeGatewayCallback(Result<ScriptObjectMirror> result,Integer outType,Long tenantId) {
        if (!result.getSignal()) {
            log.warn("网关订阅触发失败，失败信息：{}",result.getMessage());
            return;
        }
        ISubscriptionService subscriptionService = ApplicationContextUtil.getBean("subscriptionServiceImpl", ISubscriptionService.class);
        switch (outType){
            case 0:
                log.debug("网关订阅数据转换成功，执行回调：{}，消息：{},tenant:{}",callbackId,result.getResult(),tenantId);
                subscriptionService.execRedirectWithPayload(tenantId, callbackId, JSON.toJSONString(result.getResult()));
                break;
            case 2:
                log.debug("网关订阅数据转换成功，发送消息，templateId：{}，receivers:{}，消息：{},tenant:{}",callbackId,receivers,result.getResult(),tenantId);
                subscriptionService.sendNotifies(tenantId,id,callbackId,receivers,JSON.toJSONString(result.getResult()));
                break;
            default:
        }
    }


}
