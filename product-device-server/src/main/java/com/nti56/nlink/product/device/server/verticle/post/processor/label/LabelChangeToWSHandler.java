package com.nti56.nlink.product.device.server.verticle.post.processor.label;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Map;
import java.util.Set;

import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;

import com.alibaba.fastjson.JSON;
import com.nti56.nlink.common.util.R;
import com.nti56.nlink.product.device.server.config.websocket.WsSessionManager;
import com.nti56.nlink.product.device.server.domain.thing.topic.GwUpLabelTopic;
import com.nti56.nlink.product.device.server.domain.thing.up.UpData;
import com.nti56.nlink.product.device.server.verticle.post.processor.PostProcessorHandler;

import lombok.extern.slf4j.Slf4j;

/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName KeepCollectingHandler
 * @date 2022/9/23 13:28
 * @Version 1.0
 */
@Slf4j
@Component
public class LabelChangeToWSHandler extends PostProcessorHandler<GwUpLabelTopic.TopicInfo> {

    @Async("labelConsumerAsyncExecutor")
    @Override
    public void process(GwUpLabelTopic.TopicInfo topicInfo, UpData upData){
        super.process(topicInfo,upData);
    }

    @Override
    public void doProcess(GwUpLabelTopic.TopicInfo topicInfo, UpData upData) {
        upData.setProp(new ArrayList<>());
        upData.getUpGroups().forEach(upGroup -> upData.getProp().addAll(upGroup.getProp()));
        String prefix = WsSessionManager.LABEL_SESSION_KEY_PREFIX + topicInfo.getEdgeGatewayId();
        if (WsSessionManager.containsPrefix(prefix)) {
            Set<Map.Entry<String, WebSocketSession>> list = WsSessionManager.list(prefix);
            list.forEach(entry -> {
                try {
                    R r = R.ok(upData.getProp());
                    r.put("timestamp",upData.getTimestamp());
                    entry.getValue().sendMessage(new TextMessage(JSON.toJSONString(r)));
                    log.debug("标签改变消息发送：sessionId:{}",entry.getKey());
                } catch (IOException e) {
                    log.error("标签改变触发消息发送失败！topicInfo:{},upData:{}",topicInfo,upData);
                }catch (IllegalStateException e){
                    log.error("TheWebSocketSession {} has been closed {}",entry.getKey(), e.fillInStackTrace());
                    WsSessionManager.removeAndClose(entry.getKey());
                }
            });
        }
    }
}
