package com.nti56.nlink.product.device.server.controller;


import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.service.AppService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 租户开通应用初始化
 */
@RestController
@RequestMapping("/app")
public class AppController {


    @Autowired
    private AppService appService;

    @PostMapping("/init")
    public Result<Boolean> init(@RequestHeader(required = false) Long tenantId,@RequestHeader(required = false) Long clientId) {
        if(tenantId == null){
            if( clientId != null){
                tenantId = clientId;
            }else{
                return Result.error("租户id不能为空");
            }
        }
        return appService.init(tenantId);
    }
}
