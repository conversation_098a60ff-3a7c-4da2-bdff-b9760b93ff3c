package com.nti56.nlink.product.device.server.config;

import lombok.Data;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * 类说明: 网关导出<br/>
 *
 * <AUTHOR> <br/>
 * @date 2022/5/18 17:00<br/>
 * @version 1.0
 * @since JDK 1.8
 */
@ConfigurationProperties(prefix = "nlink.gateway.export")
@Configuration
@Data
@RefreshScope
public class ExportGatewayConfig {

  /**
   *  产品设备jar地址
   */
  private String jarPath;

  /**
   * 产品设备bootstrap.yml
   */
  private String bootstrapPath;

  /**
   * 产品设备application.yml
   */
  private String applicationPath;

  private String shell;

  private String bat;

  private String appName;

}
