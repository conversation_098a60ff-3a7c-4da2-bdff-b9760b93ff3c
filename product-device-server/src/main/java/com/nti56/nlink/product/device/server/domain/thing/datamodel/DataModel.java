package com.nti56.nlink.product.device.server.domain.thing.datamodel;

import java.util.ArrayList;
import java.util.List;

import com.nti56.nlink.common.fetcher.CommonFetcher;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.entity.DataModelEntity;
import com.nti56.nlink.product.device.server.entity.DataModelPropertyEntity;
import com.nti56.nlink.product.device.server.model.ExpandDataModelPropertyBo;

import lombok.Getter;

/**
 * 类说明: 数据模型领域对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-04-14 10:08:30
 * @since JDK 1.8
 */
public class DataModel {

    @Getter
    private Long id;
    @Getter
    private String name;
    @Getter
    private String descript;
    @Getter
    private List<DataModelProperty> properties;

    public static final String DATA_MODEL_ID_PATH_SPLITER = "/";

    public List<ExpandDataModelPropertyBo> listExpandProperty(){
        List<ExpandDataModelPropertyBo> list = new ArrayList<>();
        for(DataModelProperty property: properties){
            List<ExpandDataModelPropertyBo> l = property.listExpandProperty("");
            list.addAll(l);
        }
        return list;
    }

    public List<ExpandDataModelPropertyBo> listExpandProperty(String propertyPrefix){
        List<ExpandDataModelPropertyBo> list = new ArrayList<>();
        for(DataModelProperty property: properties){
            List<ExpandDataModelPropertyBo> l = property.listExpandProperty(propertyPrefix);
            list.addAll(l);
        }
        return list;
    }
    
    public static Result<DataModel> checkInfo(
        DataModelEntity entity, 
        CommonFetcher commonFetcher
    ){
        return checkInfo(
            entity, 
            commonFetcher, 
            DATA_MODEL_ID_PATH_SPLITER
        );
    }

    public static Result<DataModel> checkInfo(
        DataModelEntity entity, 
        CommonFetcher commonFetcher,
        String dataModelIdPath
    ){
        if(entity == null){
            return Result.error("模型不能为空");
        }
        DataModel dataModel = new DataModel();
        
        if(entity.getId() == null){
            return Result.error("模型id不能为空");
        }
        dataModel.id = entity.getId();

        if(entity.getName() == null){
            return Result.error("模型名称不能为空");
        }
        dataModel.name = entity.getName();

        dataModel.descript = entity.getDescript();

        List<DataModelPropertyEntity> propertyEntityList = commonFetcher.list("data_model_id", entity.getId(), DataModelPropertyEntity.class);
        if(propertyEntityList != null && propertyEntityList.size() > 0){
            List<DataModelProperty> properties = new ArrayList<>();
            for(DataModelPropertyEntity propertyEntity:propertyEntityList){
                Result<DataModelProperty> propertyResult = DataModelProperty.checkInfo(
                    propertyEntity, commonFetcher,
                    dataModelIdPath + entity.getId() + DATA_MODEL_ID_PATH_SPLITER
                );
                if(!propertyResult.getSignal()){
                    return Result.error(propertyResult.getMessage() + ", dataModelId:" + entity.getId());
                }
                properties.add(propertyResult.getResult());
            }
            dataModel.properties = properties;
        }

        return Result.ok(dataModel);
    }
}
