package com.nti56.nlink.product.device.server.model.edgegateway;

import io.vertx.core.shareddata.Shareable;
import lombok.Getter;

/**
 * 类说明: EventBus响应对象，用于替代HttpServerResponse
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024-01-01 00:00:00
 * @since JDK 1.8
 */
public class EventBusResponse implements Shareable {

    @Getter
    private String replyAddress;

    @Getter
    private String requestId;

    public EventBusResponse(String replyAddress, String requestId) {
        this.replyAddress = replyAddress;
        this.requestId = requestId;
    }
} 