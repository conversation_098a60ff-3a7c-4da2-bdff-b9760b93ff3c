package com.nti56.nlink.product.device.server.domain.tag;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.entity.Tag;
import com.nti56.nlink.product.device.server.manager.ITagManager;
import com.nti56.nlink.product.device.client.model.req.TagReq;
import lombok.extern.slf4j.Slf4j;
import org.dozer.Mapper;

/**
 * 类说明: 标记<br/>
 *
 * <AUTHOR> <br/>
 * @date 2022/5/13 11:52<br/>
 * @version 1.0
 * @since JDK 1.8
 */
@Slf4j
public class TagDomain {

  /**
   * 校验tag
   * @param tenantIsolation
   * @param req
   * @param tagManager
   * @param dozerMapper
   * @return
   */
  public static Result<Tag> checkTag(TenantIsolation tenantIsolation, TagReq req, ITagManager tagManager, Mapper dozerMapper) {
    if (tenantIsolation.getTenantId() == null) {
      return Result.error("租户id为空");
    }
    QueryWrapper<Tag> queryWrapper = new QueryWrapper<>();
    queryWrapper.eq("tag_key", req.getTagKey()).eq("tenant_id", tenantIsolation.getTenantId()).eq("tag_value", req.getTagValue());
    if (tagManager.count(queryWrapper) == 0) {
      req.setEngineeringId(tenantIsolation.getEngineeringId());
      req.setSpaceId(tenantIsolation.getSpaceId());
      req.setModuleId(tenantIsolation.getModuleId());
      req.setTenantId(tenantIsolation.getTenantId());
      Tag tag = dozerMapper.map(req, Tag.class);
      return Result.ok(tag);
    }
    return Result.error("标记已重复，请重新设置");
  }


}
