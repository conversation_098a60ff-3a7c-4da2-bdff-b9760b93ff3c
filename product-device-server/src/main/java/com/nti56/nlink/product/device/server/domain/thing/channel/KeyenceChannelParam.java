package com.nti56.nlink.product.device.server.domain.thing.channel;

import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.client.model.dto.json.ChannelRuntimeInfoField;
import com.nti56.nlink.product.device.client.model.dto.json.device.ChannelElm;
import com.nti56.nlink.product.device.server.util.RegexUtil;
import lombok.Getter;

import java.util.Map;
import java.util.HashMap;
import java.util.List;
import com.nti56.nlink.product.device.server.entity.ChannelParamEntity;


/**
 * 类说明：基恩士通道参数校验
 *
 * @ClassName KeyenceChannelParam
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/9/15 13:26
 * @Version 1.0
 */

@Getter
public class KeyenceChannelParam extends ChannelParam{
    private String ip;
    private Integer port;
    
    public static final String[] requiredParam = new String[]{
        "ip::true", 
        "port::true",
        "reconnectGapMs:3000:true::断线重连间隔（毫秒）",
        "maxConnection:1:true::通道最多连接数",
        "delayIdleMs:0:true::延迟空闲时间（毫秒）"
    };

    
    public static Result<ChannelParam> checkParam(List<ChannelParamEntity> channelParamList){
        
        KeyenceChannelParam param = new KeyenceChannelParam();
        
        Result<Map<String, String>> baseResult = ChannelParam.checkBase(
            param, requiredParam, channelParamList
        );
        if(!baseResult.getSignal()){
            return Result.error(baseResult.getMessage());
        }
        Map<String, String> paramMap = baseResult.getResult();

        String ipStr = paramMap.get("ip");
        if(ipStr == null){
            return Result.error("通道参数缺少ip");
        }
        if(!RegexUtil.checkIpv4(ipStr)){
            return Result.error("通道ip格式错误，ip:" + ipStr);
        }
        param.ip = ipStr;
        String portStr = paramMap.get("port");
        if(portStr == null){
            return Result.error("通道参数缺少port");
        }
        if(!RegexUtil.checkPort(portStr)){
            return Result.error("通道port格式错误，port:" + portStr);
        }
        param.port = Integer.parseInt(portStr);
        
        //channelKey
        param.channelKey = param.ip + param.port;

        return Result.ok(param);

    }

    @Override
    public void processRuntimeInfo(ChannelRuntimeInfoField info) {
        processBaseRuntimeInfo(info);
       
        info.setIp(ip);
        info.setPort(port);
    }

    @Override
    public void processChannelElm(ChannelElm channelElm) {
        processBaseChannelElm(channelElm);
       
        channelElm.setIp(ip);
        channelElm.setPort(port);
    }

    
}
