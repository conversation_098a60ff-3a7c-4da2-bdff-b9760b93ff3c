package com.nti56.nlink.product.device.server.domain.thing.enumerate;

import lombok.Getter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 类说明: 变动项目
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023-02-1:33:25
 * @since JDK 1.8
 */
public enum ChangeTypeEnum {
    ADD(1, "add", "新增"),
    DELETE(2, "delete", "删除"),
    UPDATE(3, "update", "更新"),
    STATUS(4, "status", "状态"),
    MODEL(5, "model", "设备模型更新"),
    PROPERTY(6, "property", "设备属性值更新"),
    ;

    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    ChangeTypeEnum(Integer value, String name, String nameDesc) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
    }

    public static ChangeTypeEnum typeOfValue(Integer value){
        ChangeTypeEnum[] values = ChangeTypeEnum.values();
        for (ChangeTypeEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static ChangeTypeEnum typeOfName(String name){
        ChangeTypeEnum[] values = ChangeTypeEnum.values();
        for (ChangeTypeEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static ChangeTypeEnum typeOfNameDesc(String nameDesc){
        ChangeTypeEnum[] values = ChangeTypeEnum.values();
        for (ChangeTypeEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }


    public static List toList(){
        List<Map> result = new ArrayList<>();
        ChangeTypeEnum[] values = ChangeTypeEnum.values();
        Map<String,Object> map ;
        for (ChangeTypeEnum v : values) {
            map = new HashMap<>();
            map.put("name",v.name);
            map.put("value",v.value);
            map.put("nameDesc",v.nameDesc);
            result.add(map);
        }
        return result;
    }
}
