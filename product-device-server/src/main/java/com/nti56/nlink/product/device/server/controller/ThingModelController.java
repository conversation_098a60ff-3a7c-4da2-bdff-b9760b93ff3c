package com.nti56.nlink.product.device.server.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.PageParam;
import com.nti56.nlink.common.util.R;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.domain.thing.dpo.ModelDpo;
import com.nti56.nlink.product.device.server.domain.thing.dpo.PropertyDpo;
import com.nti56.nlink.product.device.server.entity.ThingModelEntity;
import com.nti56.nlink.product.device.server.model.*;
import com.nti56.nlink.product.device.server.model.inherit.ThingModelOfInherit;
import com.nti56.nlink.product.device.server.model.thingModel.dto.AppendThingModelByLabelIdsDTO;
import com.nti56.nlink.product.device.server.model.thingModel.dto.CreateThingModelByLabelGroupDTO;
import com.nti56.nlink.product.device.server.model.thingModel.dto.CreateThingModelByLabelIdsDTO;
import com.nti56.nlink.product.device.server.model.thingModel.vo.ThingModelValidRepeatVo;
import com.nti56.nlink.product.device.server.service.IThingModelService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 类说明: 物模型controller
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 13:33:12
 * @since JDK 1.8
 */
@RestController
@Slf4j
@RequestMapping("/")
@Tag(name = "物模型模块")
public class ThingModelController {

    @Autowired
    IThingModelService thingModelService;

    @PostMapping("/thing-model")
    @Operation(summary = "创建物模型")
    public R createThingModel( 
        @RequestHeader("ot_headers") TenantIsolation tenantIsolation,
        @Parameter(description = "物模型实体",required = true)
        @RequestBody ThingModelInfoDto thingModelInfo
    ){
        Result<ThingModelEntity> result = thingModelService.createThingModel(
            tenantIsolation,
            thingModelInfo
        );
        return R.ok().put("thingModelId", result.getResult().getId());
    }


    @PostMapping("/thing-model/create-by-label-ids")
    @Operation(summary = "根据标签id列表创建物模型")
    public R createThingModelByLabelIds(
            @RequestHeader("ot_headers") TenantIsolation tenantIsolation,
            @RequestBody @Validated CreateThingModelByLabelIdsDTO dto
    ){
        return R.result(thingModelService.createThingModelByLabelIds(
                dto,
                tenantIsolation
        ));
    }


    @PostMapping("/thing-model/create-by-label-group")
    @Operation(summary = "根据标签分组创建物模型")
    public R createThingModelByLabelGroup(
            @RequestHeader("ot_headers") TenantIsolation tenantIsolation,
            @RequestBody @Validated CreateThingModelByLabelGroupDTO dto
    ){
        return R.result(thingModelService.createThingModelByLabelGroup(
                dto,
                tenantIsolation
        ));
    }

    @DeleteMapping("/thing-model/{thingModelId}")
    public R deleteThingModel(
        @RequestHeader("ot_headers") TenantIsolation tenantIsolation,
        @PathVariable Long thingModelId
    ){
        Result<Boolean> result = thingModelService.deleteThingModel(
            tenantIsolation,
            thingModelId
        );
        if(result.getSignal()){
            return R.ok();
        }else{
            return R.error(result.getMessage());
        }
    }

    @PutMapping("/thing-model/info")
    public R editThingModelInfo(
        @RequestHeader("ot_headers") TenantIsolation tenantIsolation,
        @Parameter(description = "物模型信息",required = true)@RequestBody ThingModelInfoDto thingModelInfo
    ){
        Result<Boolean> result = thingModelService.editThingModelInfo(
            tenantIsolation,
            thingModelInfo
        );
        if(result.getSignal()){
            return R.ok();
        }else{
            return R.error(result.getMessage());
        }
    }

    @PutMapping("/thing-model/model")
    public R editThingModelModel(
        @RequestHeader("ot_headers") TenantIsolation tenantIsolation,
        @Parameter(description = "物模型模型定义",required = true)@RequestBody ThingModelModelDto thingModelModel
    ){
        Result<Boolean> result = thingModelService.editThingModelModel(
            tenantIsolation,
            thingModelModel
        );
        if(result.getSignal()){
            return R.ok();
        }else{
            return R.error(result.getMessage());
        }
    }

    @GetMapping("/thing-model/page")
    @Operation(summary = "查询物模型")
    public R listThingModel(
            @RequestHeader("ot_headers") TenantIsolation tenantIsolation,
            @Parameter(description = "物模型名称") String searchStr,
            @Parameter(description = "标记id") Long[] tagIds,
            PageParam pageParam
    ){
        Page<ThingModelVo> page = pageParam.toPage(ThingModelVo.class);
        Result<Page<ThingModelVo>> result = thingModelService.listThingModel(page,tenantIsolation,searchStr,tagIds == null ? null : Arrays.asList(tagIds));
        return R.result(result);
    }
    
    @GetMapping("thing-model/{entityId}")
    @Operation(summary = "获取对象")
    public R get(
        @RequestHeader("ot_headers") TenantIsolation tenantIsolation,
        @Parameter(description = "目标ID") @PathVariable Long entityId
    ){
        Result<ThingModelDto> result = thingModelService.getThingModelById(
            tenantIsolation,
            entityId
        );
        return R.result(result);
    }

    @GetMapping("thing-model/{entityId}/4/service")
    @Operation(summary = "获取完整模型")
    public R getModel4Service(
            @RequestHeader("ot_headers") TenantIsolation tenantIsolation,
            @Parameter(description = "目标ID") @PathVariable Long entityId
    ){
        Result<ModelDpo> result = thingModelService.getModel4Service(tenantIsolation,entityId);
        return R.result(result);
    }

    @GetMapping("thing-model/select/list/{id}")
    @Operation(summary = "根据id获取模型附属下拉框列表")
    public R getModelSelectList(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@PathVariable Long id){
        log.info("获取模型附属下拉框列表：{},租户信息：{}", id, tenantIsolation);
        return R.result(thingModelService.getModelSelectList(id,tenantIsolation));
    }

    @GetMapping("/thing-model/list/inherit-available")
    @Operation(summary = "获取物模型下拉可被继承的物模型列表" ,
    parameters = {
            @Parameter(name = "thingModelId",description = "物模型id",required = false)
    })
    public R listInheritAvailable(
        @RequestHeader("ot_headers") TenantIsolation tenantIsolation,
        Long thingModelId
    ){
        Result<List<ThingModelSimpleBo>> result = thingModelService.listInheritAvailable(
            tenantIsolation,
            thingModelId
        );
        return R.result(result);
    }


    @PostMapping("/thing-model/valid-repeat")
    @Operation(summary = "根据选中物模型校验是否存在重复")
    public R listAll(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@RequestBody ThingModelValidRepeatDto thingModelValidRepeatDto) {
        Result<ThingModelValidRepeatVo> result = thingModelService.validRepeat(tenantIsolation,thingModelValidRepeatDto);
        return R.result(result);
    }

    @GetMapping("/thing-model/inherit-part-model")
    @Operation(summary = "获取物模型继承部分的模型",
    parameters = {
            @Parameter(name = "thingModelId",description = "物模型id",required = true)
    })
    public R getInheritPartModel(
        @RequestHeader("ot_headers") TenantIsolation tenantIsolation,
        Long thingModelId
    ){
        Result<ThingModelOfInherit> result = thingModelService.getInheritPartModel(
            tenantIsolation,
            thingModelId
        );
        return R.result(result);
    }

    @GetMapping("/thing-model/inherit-model")
    @Operation(summary = "获取物模型继承部分的模型",
    parameters = {
            @Parameter(name = "thingModelIds",description = "物模型ids",required = true)
    })
    public R getInheritModel(
        @RequestHeader("ot_headers") TenantIsolation tenantIsolation,
        Long[] thingModelIds
    ){
        if(thingModelIds == null || thingModelIds.length <= 0){
            return R.ok();
        }
        Result<List<ModelDpo>> result = thingModelService.getInheritModel(
            tenantIsolation,
            Arrays.asList(thingModelIds)
        );
        if(!result.getSignal()){
            return R.error(result.getMessage());
        }

        List<ModelDpo> list = result.getResult();
        //把属性名提取成map
        Map<String, String> propertyMap = list.stream().reduce(
            new HashMap<>(),
            (m, item) -> {
                List<String> collect = item.getProperties()
                    .stream()
                    .map(PropertyDpo::getName)
                    .collect(Collectors.toList());
                for(String propertyName:collect){
                    m.put(propertyName, propertyName);
                }
                return m;
            },
            (a, b) -> {return null;}
        );
        return R.ok()
            .put("inheritModel", list)
            .put("propertyMap", propertyMap)
            ;
    }


    @PostMapping("/thing-model/appendThingModelByLabelIds")
    @Operation(summary = "根据标签id列表追加物模型")
    public R appendThingModelByLabelIds(
            @RequestHeader("ot_headers") TenantIsolation tenantIsolation,
            @RequestBody @Validated AppendThingModelByLabelIdsDTO dto){
        return R.result(thingModelService.appendThingModelByLabelIds(dto,tenantIsolation));
    }


    @PostMapping("thing-model/export/{entityId}")
    @Operation(summary = "导出物模型")
    public R export(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
            @Parameter(description = "目标ID") @PathVariable Long entityId){
        Result<ThingTransmitModelDto> result = thingModelService.exportModel(
                tenantIsolation,
                entityId
        );
        return R.result(result);
    }

    @PostMapping("thing-model/import")
    @Operation(summary = "导入物模型")
    public R importThingModel(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@Parameter(description = "物模型信息",required = true)@RequestBody ThingTransmitModelDto thingTransmitModelDto){
        Result<ThingModelImportVo> result = thingModelService.importModel(
                tenantIsolation,
                thingTransmitModelDto
        );
        return R.result(result);
    }

    @PostMapping("thing-model/coverImport/{entityId}")
    @Operation(summary = "覆盖导入物模型")
    public R coverImportModel(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@Parameter(description = "物模型信息",required = true)@RequestBody ThingTransmitModelDto thingTransmitModelDto,@Parameter(description = "目标ID") @PathVariable String entityId){
        Result<ThingModelImportVo> result = thingModelService.coverImportModel(
                tenantIsolation,
                thingTransmitModelDto,
                Long.valueOf(entityId)
        );
        return R.result(result);
    }

    @PostMapping("thing-model/copyModel/{entityId}")
    @Operation(summary = "复制物模型")
    public R copyModel(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@Parameter(description = "目标ID") @PathVariable String entityId){
        Result<Long> result = thingModelService.copyThingModel(
                tenantIsolation,
                Long.valueOf(entityId)
        );
        return R.result(result);
    }


    @GetMapping("thing-model/sinkSubscribe")
    @Operation(summary = "下沉CommonType订阅")
    public R sinkSubscribe(){
        thingModelService.sinkSubscribe();
        return R.ok();
    }
}

