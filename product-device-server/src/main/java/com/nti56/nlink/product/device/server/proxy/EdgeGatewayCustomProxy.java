package com.nti56.nlink.product.device.server.proxy;

import com.alibaba.fastjson.JSONObject;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.MqttTopicEnum;
import com.nti56.nlink.product.device.server.feign.IEdgeGatewayCustomProxy;
import com.nti56.nlink.product.device.server.util.MqttProxyEventBusUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 类说明: 边缘网关自定义代理 - EventBus实现
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024-01-01 00:00:00
 * @since JDK 1.8
 */
@Component
public class EdgeGatewayCustomProxy implements IEdgeGatewayCustomProxy {

    @Autowired
    private MqttProxyEventBusUtil mqttProxyEventBusUtil;

    @Override
    public Result<String> customDriverSerialize(Long edgeGatewayId, Long tenantId, 
                                               String customDriverName, String messageName, 
                                               Map<String, Object> var) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("customDriverName", customDriverName);
            params.put("messageName", messageName);
            params.put("var", var);
            String body = JSONObject.toJSONString(params);
            
            CompletableFuture<String> future = mqttProxyEventBusUtil.sendAssignRequest(
                MqttTopicEnum.OT_CUSTOM, 
                "request", 
                tenantId.toString(), 
                edgeGatewayId.toString(), 
                "customDriverSerialize", 
                body
            );
            
            String response = future.get();
            return JSONObject.parseObject(response, Result.class);
        } catch (Exception e) {
            return Result.error("自定义协议序列化失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Map<String, Object>> customDriverDeserialize(Long edgeGatewayId, Long tenantId, 
                                                               String customDriverName, String messageName, 
                                                               String str) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("customDriverName", customDriverName);
            params.put("messageName", messageName);
            params.put("str", str);
            String body = JSONObject.toJSONString(params);
            
            CompletableFuture<String> future = mqttProxyEventBusUtil.sendAssignRequest(
                MqttTopicEnum.OT_CUSTOM, 
                "request", 
                tenantId.toString(), 
                edgeGatewayId.toString(), 
                "customDriverDeserialize", 
                body
            );
            
            String response = future.get();
            return JSONObject.parseObject(response, Result.class);
        } catch (Exception e) {
            return Result.error("自定义协议反序列化失败: " + e.getMessage());
        }
    }
} 