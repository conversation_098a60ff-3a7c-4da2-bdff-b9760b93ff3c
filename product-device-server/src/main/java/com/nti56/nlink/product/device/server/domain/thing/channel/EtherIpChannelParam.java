package com.nti56.nlink.product.device.server.domain.thing.channel;

import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.client.model.dto.json.ChannelRuntimeInfoField;
import com.nti56.nlink.product.device.client.model.dto.json.device.ChannelElm;
import com.nti56.nlink.product.device.server.util.RegexUtil;
import lombok.Getter;

import java.util.HashMap;
import java.util.List;
import com.nti56.nlink.product.device.server.entity.ChannelParamEntity;
import org.springframework.util.StringUtils;

import java.util.Map;

/**
 * 类说明: AB plc/EtherIp驱动通道参数领域对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-08-10 14:29:25
 * @since JDK 1.8
 */
@Getter
public class EtherIpChannelParam extends ChannelParam{
    
    private String ip;
    private Integer port;
    private Integer slot;
    private Integer queueTimeout;
    private String channelKeyPrefix;

    public static final String[] requiredParam = new String[]{
        "ip::true", 
        "port::true",
        "slot::true",
        "reconnectGapMs:3000:true::断线重连间隔（毫秒）",
        "maxConnection:1:true::通道最多连接数",
        "delayIdleMs:0:true::延迟空闲时间（毫秒）",
        "queueTimeout:10:true::队列超时清除时间（秒），不能小于10",
        "channelKeyPrefix::true::通道key前缀"
    };

    
    public static Result<ChannelParam> checkParam(List<ChannelParamEntity> channelParamList){
        
        EtherIpChannelParam param = new EtherIpChannelParam();

        Result<Map<String, String>> baseResult = ChannelParam.checkBase(
            param, requiredParam, channelParamList
        );
        if(!baseResult.getSignal()){
            return Result.error(baseResult.getMessage());
        }
        Map<String, String> paramMap = baseResult.getResult();

        //ip
        String ipStr = paramMap.get("ip");
        if(ipStr == null){
            return Result.error("通道参数缺少ip");
        }
        if(!RegexUtil.checkIpv4(ipStr)){
            return Result.error("通道ip格式错误，ip:" + ipStr);
        }
        param.ip = ipStr;

        //port
        String portStr = paramMap.get("port");
        if(portStr == null){
            return Result.error("通道参数缺少port");
        }
        if(!RegexUtil.checkPort(portStr)){
            return Result.error("通道port格式错误，port:" + portStr);
        }
        param.port = Integer.parseInt(portStr);

        //channelKeyPrefix
        String channelKeyPrefix = paramMap.get("channelKeyPrefix");
        if(channelKeyPrefix == null){
            channelKeyPrefix = "";
        }
        param.channelKeyPrefix = channelKeyPrefix;

        //channelKey
        param.channelKey = channelKeyPrefix + param.ip + param.port;

        //slot
        String slotStr = paramMap.get("slot");
        if(slotStr == null){
            return Result.error("通道参数缺少slot");
        }
        if(!RegexUtil.checkInt(slotStr)){
            return Result.error("通道slot格式错误，slot:" + slotStr);
        }
        param.slot = Integer.parseInt(slotStr);

        //queueTimeout
        String queueTimeoutStr = paramMap.get("queueTimeout");
        if(StringUtils.isEmpty(queueTimeoutStr)){
            queueTimeoutStr = "10";
            param.queueTimeout = 10;
        }
        if(!RegexUtil.checkInt(queueTimeoutStr)){
            return Result.error("通道queueTimeout格式错误，queueTimeout:" + queueTimeoutStr);
        }
        Integer queueTimeout = Integer.parseInt(queueTimeoutStr);
        if(queueTimeout < 10){
            return Result.error("通道queueTimeout不能小于10秒，queueTimeout:" + queueTimeoutStr);
        }
        param.queueTimeout = queueTimeout;
        
        return Result.ok(param);

    }

    @Override
    public void processRuntimeInfo(ChannelRuntimeInfoField info) {
        processBaseRuntimeInfo(info);
        
        info.setIp(ip);
        info.setPort(port);
        info.setSlot(slot);
        info.setQueueTimeout(queueTimeout);
    }

    @Override
    public void processChannelElm(ChannelElm channelElm) {
        processBaseChannelElm(channelElm);
       
        channelElm.setIp(ip);
        channelElm.setPort(port);
        channelElm.setSlot(slot);
        channelElm.setQueueTimeout(queueTimeout);
    }

    
}
