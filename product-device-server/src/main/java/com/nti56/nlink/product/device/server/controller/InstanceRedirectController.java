package com.nti56.nlink.product.device.server.controller;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.*;
import com.nti56.nlink.product.device.server.entity.InstanceRedirectEntity;
import com.nti56.nlink.product.device.server.model.EdgeGatewayDto;
import com.nti56.nlink.product.device.server.model.edgegateway.dto.ExcelMessageDTO;
import com.nti56.nlink.product.device.server.model.edgegateway.vo.EdgeGatewayVO;
import com.nti56.nlink.product.device.server.model.redirect.ExcelRedirectDTO;
import com.nti56.nlink.product.device.server.model.redirect.RedirectDTO;
import com.nti56.nlink.product.device.server.model.redirect.RedirectReferenceDto;
import com.nti56.nlink.product.device.server.service.IInstanceRedirectService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Set;


/**
 * 类说明：
 *
 * @ClassName InstanceRedirectController
 * @Description 实例回调controller
 * <AUTHOR>
 * @Date 2022/6/24 16:04
 * @Version 1.0
 */


@RestController
@RequestMapping("redirect")
@Slf4j
@Tag(name = "redirect", description = "实例回调")
@SecurityRequirement(name = "token")
public class InstanceRedirectController {

    @Autowired
    private IInstanceRedirectService instanceRedirectService;

    @GetMapping("/page")
    @Operation(summary = "回调分页数据")
    public R page(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                  PageParam pageParam, InstanceRedirectEntity entity) {
        Page<InstanceRedirectEntity> page = pageParam.toPage(InstanceRedirectEntity.class);
        entity.setTenantId(tenantIsolation.getTenantId());
        Result<Page<RedirectDTO>> result = instanceRedirectService.getPage(entity, page);
        return R.result(result);
    }


    @GetMapping("/list")
    @Operation(summary = "获取回调列表")
    public R list(@RequestHeader("ot_headers") TenantIsolation tenantIsolation, InstanceRedirectEntity entity) {
        entity.setTenantId(tenantIsolation.getTenantId());
        Result<List<InstanceRedirectEntity>> result = instanceRedirectService.list(entity);
        return R.result(result);
    }

    @PostMapping("/")
    @Operation(summary = "创建一个回调")
    public R create(
            @RequestHeader("ot_headers") TenantIsolation tenantIsolation,
            @Parameter(description = "") @RequestBody InstanceRedirectEntity entity
    ) {
        Result<InstanceRedirectEntity> result = instanceRedirectService.save(tenantIsolation, entity);
        return R.result(result);
    }

    @PutMapping("/{redirectId}")
    @Operation(summary = "更新回调信息")
    public R update(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                    @Parameter(description = "对象") @RequestBody InstanceRedirectEntity entity) {
        if (BeanUtilsIntensifier.checkBeanAndProperties(entity, InstanceRedirectEntity::getId)) {
            return R.error(ServiceCodeEnum.CODE_PARAM_ERROR);
        }
        Result result = instanceRedirectService.update(entity, tenantIsolation);
        return R.result(result);
    }

    @DeleteMapping("/{redirectId}")
    @Operation(summary = "删除指定Id回调")
    public R delete(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                    @Parameter(description = "回调ID") @PathVariable Long redirectId) {

        Result result = instanceRedirectService.deleteById(redirectId, tenantIsolation);
        return R.result(result);
    }

    @GetMapping("/{redirectId}")
    @Operation(summary = "获取对象")
    public R get(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                 @Parameter(description = "回调ID") @PathVariable Long redirectId) {
        Result<InstanceRedirectEntity> result = instanceRedirectService.getByIdAndTenantIsolation(redirectId, tenantIsolation);
        return R.result(result);
    }

    @PostMapping("/testRedirect")
    @Operation(summary = "测试回调")
    public R testRedirect(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                          @RequestBody InstanceRedirectEntity entity) {
        Result<Object> result = instanceRedirectService.testRedirect(entity, tenantIsolation);
        if (!result.getSignal()) {
            result.setMessage("连接失败");
        }
        return R.result(result);
    }

//    @GetMapping("/execRedirect")
//    @Operation(summary = "执行回调")
//    public R execRedirect(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
//                          @Parameter(description = "回调ID") @RequestParam Long redirectId) {
//        Result<Boolean> result = instanceRedirectService.execRedirect(redirectId, tenantIsolation);
//        return R.result(result);
//    }

//    @PostMapping("/execRedirectWithPayload")
//    @Operation(summary = "执行回调")
//    public R execRedirectWithPayload(@RequestHeader("ot_headers") String tenantIsolation,
//                                     @Parameter(description = "回调ID") @RequestParam Long redirectId, @RequestBody Object payload) {
//        Result<Boolean> result = instanceRedirectService.execRedirectWithPayload(redirectId, JSONUtil.toBean(tenantIsolation, TenantIsolation.class), payload);
//        return R.result(result);
//    }

    @GetMapping("/redirectCount")
    @Operation(summary = "回调总数")
    public R redirectCount(@RequestHeader("ot_headers") TenantIsolation tenantIsolation) {
        Result<Integer> result = instanceRedirectService.redirectCount(tenantIsolation);
        return R.result(result);
    }

    @GetMapping("/reference/{redirectId}")
    @Operation(summary = "查看回调被引用")
    public R getReference(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,
                          @Parameter(description = "回调ID") @PathVariable Long redirectId) {
        return R.result(instanceRedirectService.getReference(redirectId, tenantIsolation));
    }

    @PostMapping("/updateReference")
    @Operation(summary = "执行回调")
    public R execRedirectWithPayload(@RequestHeader("ot_headers") String tenantIsolation, @RequestBody RedirectReferenceDto referenceDto) {
        Result<Boolean> result = instanceRedirectService.updateReference(JSONUtil.toBean(tenantIsolation, TenantIsolation.class), referenceDto);
        return R.result(result);
    }

    @PostMapping("batch/input/{exportType}")
    @Operation(summary = "导入回调", description = "exportType 0:不保留;1:保留两者")
    public R referenceBatchInput(@RequestHeader("ot_headers") TenantIsolation tenantIsolation,@PathVariable Integer exportType,@RequestBody List<ExcelRedirectDTO> list) {
        Result<List<ExcelMessageDTO>> result = instanceRedirectService.instanceRedirectBatchInput(tenantIsolation,exportType,list);
        return R.result(result);
    }

    @PostMapping({"export"})
    @Operation(summary = "导出回调")
    public void exportReference(HttpServletResponse response, @RequestHeader("ot_headers") TenantIsolation tenantIsolation,@RequestBody Set<Long> referenceIds) throws IOException {
        instanceRedirectService.exportRedirect(response,tenantIsolation.getTenantId(),referenceIds);
    }

}
