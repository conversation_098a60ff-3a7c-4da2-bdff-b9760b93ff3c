package com.nti56.nlink.product.device.server.constant;

/**
 * 类说明: InfluxDb 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-22 09:48:05
 * @since JDK 1.8
 */
public class NotifyConstant {

    public static final int MAX_SMS_SIZE = 500;

    public static final int RETRY_TIME = 3;

    public static final String COMMA = ",";

    public static final int DELETED = 1;

    public static final String PROVIDER = "provider";

    public static final String NONE_DEFAULT_VALUE = "unknown";

    public static final String TEMPLATE_WILDCARD_REGEX = "\\$\\{\\w*\\}";

    public static final String PHONE_REGEX = "^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\\d{8}$";

    public static final String EMAIL_REGEX = "^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+((\\.[a-zA-Z0-9_-]{2,3}){1,2})$";

    public static final String PRIVATE_KEY = "MIIJQwIBADANBgkqhkiG9w0BAQEFAASCCS0wggkpAgEAAoICAQC4obnOLSWI/k1n" +
            "soxIs9QNcdccDxO7LRSIngxeWkrtEuGv5Ah3OVj5uc/I8UVrfBmKzMjeRB/WNon7" +
            "Ak0KHkTxaqaHxxDsL+QUqw9hCM+3E5nQ6FYhoxp+1s38w1gRCIZSdKZillfAweMC" +
            "9XSH9POdrLnVdeo4R3GLh2o1frSYN/SBgBvXqchsIQaxF5GxpFUw4CinEzTc2OlJ" +
            "L418pCwLDPre9tFZk3hCGQrThECZj9jKAhpqqbiWRRfqrvuiyzBJcACyW/MIxKWo" +
            "kjn70Y/P8+EPJHYk4B+TM+l/aqFXEwt+QX2hhcFi6AF3+xHWl1MonZSdCr7YfPEf" +
            "3Pih/EzjjNOtZmTMyME5K5xf75aGaaXJqM8Tmtj9LRXC6PdH/ULrpSfuzKmrLMzT" +
            "WJ7PLFJ82tgYCLBPOcgWPPJzEtsuV1zHWjXx2AH7udtLLf5AQkmq7HRmT7+zfyEN" +
            "Bak1k1b9LUVbNYSosFodp2trft46YAIc8IPdepwoOettLLWlldSIjDXk30g7vBZ6" +
            "ZouS059cgox5DK/y3kD+3LrDad6V+7CTZ8x3txc6kWRzSuL+997d2MKzFa1v/MM3" +
            "RsCWs2yvjOkAIxh/XE7u3WrBdSYBaRgSfkfjlv8PgLi/FvRF5/7zxkhAc3yrl/2q" +
            "TtdhJ6+XCT5GF4a2fvT4F4fMqvglswIDAQABAoICAFWNmyuOf/Ya4INMmPSXX4tb" +
            "VPILinkRSpKDqfeOn2l7dliqscGNMU2GbGIQarqDptJKUkmsB5SbcRR+3HPz0r6L" +
            "TN9dvtmy1108ZsPv8VBEJOJIxqCPk5dyscCORCCgcp2CsBa+06reLPMSCg110+0c" +
            "PIBNQ1sQv+yHsw9WxxhB1Wqskjo80kJIi3FEGp7WJyOeuzewDq7HVFI1VrqK2F3G" +
            "hfkLJbb8Pp1iPUIF5mlB3pK8psEGJCJ9obd9ndyxio5899WuBc/qT1jtaTDmylzy" +
            "BTefDZ/IutjbFTeVkW1nxcubTTurXxEVsJiIHpvxQvdvDY43jHeEbbb2DcAByR5d" +
            "JcpC2tKuxWiYp7aAbf3jrcm6UbtAFavHvhzT7Lz/hQSmLNKb7FVzXMt+e4vHIJOG" +
            "SC++SCw+icUe/0Gl+VOvrzhAGQ1Fahn47YRhfdp8OspSk+znfoDgZgB4jfKBC7YI" +
            "WxMr5o1n9I2eGjzbYE8a6BBK5m/WqJF/ffpKofM1VbQAs34dgclk1IjKgidXeB4N" +
            "JtLpvDX3Vq68zNubBk/DbrnvLf0QXTxouiXhTSzV9tTBPSXjjppBjIhcZcH2Szq5" +
            "nOoktnBRs8bi5nzduGkmi4kdmONVG9FP1OpzNj61PhXOodDmk1mMIU0AoDn2elx0" +
            "B6bKnlCiHCJhla+DhHkBAoIBAQD0Zni4oQYqSHZMO7gT6i9bSTAXruDdP4LGgZLg" +
            "bfSaANzwHytkBwZ67X6uBWhZqmYyuXeXQFzu7qChvQdr4OVUDlbJDvz/ngCGSsis" +
            "K4oFTO1gq85HpyjD2EX2jYs1A1mc6dgVeOurM5ZUVheoUD0FxOoTQQzd8zJXbiS5" +
            "tWAbgPDavGuZ72xomD4So9x6rHWLCDdMdzPgnOJqeq1kJzevfPoPKPE2QOpTQK49" +
            "C4kpHNfVbm7zid8lzNXMdrMZUxqhs1U4fk9+AyXzsDon6h2Tb7qgp0pso+sKrHHn" +
            "6lO+dGmW8q/Le55X7utO0e7ibxwa90VSbjQXv14jPrpZf/3tAoIBAQDBZQzzltCE" +
            "6VKrclT5TwShjQsRB3frC8FeZxl9zORp/CezRLXY3QjHB4jE+eNtqcCL1MwW3es4" +
            "3CBJ+YdkqN8xJAVYFmN1dlR3NeTXllHwMl3wav4J41Q/8nKjFV4roJhVrQRHwM1a" +
            "YJ1Jk1jIAvniz5UqfWWK64WY/B8ajV9ysr0ALjinzyzgUvWoyVlVt1/GHCE9iA6s" +
            "m216pmNgVrREqTxMKcLxX8g8D68997ZA1aAciRQcTSB7/zMBDGXtnRR5LDHIEYMn" +
            "orWRrMXZmSsPNhqNwx6T1qMmJbT4z8wHlChs6NtO8sDpx1F2VVcadXZ+SxfQpvtA" +
            "QbPexY4xvT4fAoIBAAICkb2a/Wz9mZN47kOjqm4KxQ18z7aiHhY53JZh8SAPH8IN" +
            "z5KMCD6ck20k+D28jAigw94HlnwUlFegMV5uxBQT5hzTNbt4djKkjM/1r7Xpcw5X" +
            "e2PVeR1bOwATG/D8rREAT1ml2/JVg0gASUOGHWpduzsbTtrIdEKJ0l2Ao2C2h8SU" +
            "xiHG+VJG5kPK+zY7EuBo2eOUG2YehDIx/hkTxX54/amAQbObhxHWN1ijLqaa6R+F" +
            "P/5axO4PqDZ9R23O5CouSmYji83Sgs5lRaUsZL/grF1D4vXPx1AYKPuqTrQDSxcF" +
            "ufJnet7oRSEp0tgj58xE+ln6NA158nFBw9CWn+UCggEBAKatU8tA6PgUK4bN5AR2" +
            "oNQnPilwGV8/Y2ncS7ELfLUHeKPdjSiZ363BhZPWlrd1/RfG0BrJKqh3t3BqGdGc" +
            "ZbfxhpWTviaHVGbl0RQpXvc4QZQqckXNYlg2uaxqhx3srD3y/uRke9fvur+luXew" +
            "cPOU2sejCXgAYsUmThxRSbkN6SyVqYifWtr/RPAykMb6YxXYOqHQeG01C9cQPkfS" +
            "kk022lv+BiCH34g8MFmk+7PHKdZOIkH+CwgzOx8lM1fl8LY6a7E1vqbTac9ia6aN" +
            "hvn2NE6Hovvhmf0PtHhYh0zk2S58Rj81oyQY/9GP3aFVKUnRqHXQ5GxN9BsEgvfF" +
            "VRsCggEBAIYi5IFWnsRlmu5XbwzBpoPzKBZDcnAWDXB0rZOmXeJ196T9iG7eWmXj" +
            "QNR6A9/Uc9/P6V9FKVRr9npyNmxFZQnNLXv4AHGFaBK91g6JrPbiJiD9g5M0vmqK" +
            "3wWTGLG0RQiUP2bTjC4aVFDkjlJdxX6XqivRBxRGc4hbflNwE0RUdDS5S2v1EYkw" +
            "gNZ2owTw8YhgLrM0+ManBKGldYXXvSgcLKelgDZYNMboUjouBZgXI78WOpNExVVC" +
            "0WwpVVhO0KfSJHnqDegZg/ZDea+ranyngLtqZKWkUxhwNSiCNJtc/aEpS6KkkuEm" +
            "9oJQxG/cyGVOTQcmjcppEyR44RVL0wU=";
}
