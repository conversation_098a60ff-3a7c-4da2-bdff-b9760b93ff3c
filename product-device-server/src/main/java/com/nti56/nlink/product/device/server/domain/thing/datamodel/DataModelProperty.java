package com.nti56.nlink.product.device.server.domain.thing.datamodel;

import com.nti56.nlink.common.fetcher.CommonFetcher;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.client.model.dto.json.enumerate.ThingDataTypeEnum;
import com.nti56.nlink.product.device.server.entity.DataModelEntity;
import com.nti56.nlink.product.device.server.entity.DataModelPropertyEntity;
import com.nti56.nlink.product.device.server.model.ExpandDataModelPropertyBo;
import lombok.Getter;

import java.util.*;

/**
 * 类说明: 数据模型属性领域对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-04-14 10:13:43
 * @since JDK 1.8
 */
@Getter
public class DataModelProperty {
    
    private Long id;

    private String name;

    private String descript;

    private ThingDataTypeEnum dataType;

    private DataModel dataModel;

    private Boolean isArray;

    private Object defaultValue;

    public static final String DATA_MODEL_EXPAND_PROPERTY_SPLITER = ".";

    public List<ExpandDataModelPropertyBo> listExpandProperty(String propertyPrefix) {
        switch (dataType) {
            case DATA_MODEL:
                if(isArray){
                    return dataModel.listExpandProperty(
                        propertyPrefix 
                        + name + "[]"
                        + DATA_MODEL_EXPAND_PROPERTY_SPLITER
                    );
                }
                return dataModel.listExpandProperty(
                    propertyPrefix 
                    + name 
                    + DATA_MODEL_EXPAND_PROPERTY_SPLITER
                );
            default:{
                ExpandDataModelPropertyBo expandProperty = ExpandDataModelPropertyBo.builder()
                    .name(name)
                    .expandName(propertyPrefix + name)
                    .dataType(dataType)
                    .isArray(isArray)
                    .defaultValue(defaultValue)
                    .build();
                return new ArrayList<ExpandDataModelPropertyBo>(){{
                    add(expandProperty);
                }};
            }

        }
    }

    public static Result<DataModelProperty> checkInfo(
        DataModelPropertyEntity entity, 
        CommonFetcher commonFetcher,
        String dataModelIdPath
    ) {
        if(entity == null){
            return Result.error("模型属性不能为空");
        }
        DataModelProperty property = new DataModelProperty();
        
        if(entity.getId() == null){
            return Result.error("模型属性id不能为空");
        }
        property.id = entity.getId();

        if(entity.getName() == null){
            return Result.error("模型属性名称不能为空");
        }
        property.name = entity.getName();

        property.descript = entity.getDescript();

        if(entity.getIsArray() == null){
            return Result.error("模型属性是否数组不能为空");
        }
        property.isArray = entity.getIsArray();

        //dataType
        Integer typeInt = entity.getDataType();
        if(typeInt == null){
            return Result.error("模型属性缺少数据类型");
        }
        ThingDataTypeEnum typeEnum = ThingDataTypeEnum.typeOfValue(typeInt);
        if(typeEnum == null){
            return Result.error(
                "模型属性数据类型错误，dataType: " + typeInt
            );
        }
        property.dataType = typeEnum;
        
        switch (typeEnum) {
            case DATA_MODEL: {
                Long propertyDataModelId = entity.getPropertyDataModelId();
                if(propertyDataModelId == null){
                    return Result.error("模型属性数据类型id不能为空");
                }
                //不允许循环引用
                String[] split = dataModelIdPath.split(DataModel.DATA_MODEL_ID_PATH_SPLITER);
                Set<String> dataModelIdSet = new HashSet<>(Arrays.asList(split));
                if(dataModelIdSet.contains(propertyDataModelId.toString())){
                    return Result.error("模型属性数据类型不能循环引用, propertyDataModelId:" + propertyDataModelId);
                }
                DataModelEntity dataModelEntity = commonFetcher.get(propertyDataModelId, DataModelEntity.class);
                Result<DataModel> dataModelResult = DataModel.checkInfo(dataModelEntity, commonFetcher, dataModelIdPath);
                if(!dataModelResult.getSignal()){
                    return Result.error(dataModelResult.getMessage() + ", dataModelPropertyId" + entity.getId());
                }
                property.dataModel = dataModelResult.getResult();
                break;
            }
            case BOOLEAN: {
                String defaultValueStr = entity.getDefaultValue();
                if(defaultValueStr != null && !"".equals(defaultValueStr)){
                    if("true".equals(defaultValueStr)){
                        property.defaultValue = true;
                    }else if("false".equals(defaultValueStr)){
                        property.defaultValue = false;
                    }else{
                        return Result.error("默认值错误, name:" + entity.getName() + ", defaultValue:" + defaultValueStr);
                    }
                }
                break;
            }
            case CHAR: {
                String defaultValueStr = entity.getDefaultValue();
                if(defaultValueStr != null && !"".equals(defaultValueStr)){
                    try {
                        property.defaultValue = Byte.valueOf(defaultValueStr);
                    } catch (Exception e) {
                        return Result.error("默认值错误, name:" + entity.getName() + ", defaultValue:" + defaultValueStr);
                    }
                }
                break;
            }
            case BYTE: {
                String defaultValueStr = entity.getDefaultValue();
                if(defaultValueStr != null && !"".equals(defaultValueStr)){
                    try {
                        property.defaultValue = Short.valueOf(defaultValueStr);
                    } catch (Exception e) {
                        return Result.error("默认值错误, name:" + entity.getName() + ", defaultValue:" + defaultValueStr);
                    }
                }
                break;
            }
            case SHORT: {
                String defaultValueStr = entity.getDefaultValue();
                if(defaultValueStr != null && !"".equals(defaultValueStr)){
                    try {
                        property.defaultValue = Short.valueOf(defaultValueStr);
                    } catch (Exception e) {
                        return Result.error("默认值错误, name:" + entity.getName() + ", defaultValue:" + defaultValueStr);
                    }
                }
                break;
            }
            case WORD: {
                String defaultValueStr = entity.getDefaultValue();
                if(defaultValueStr != null && !"".equals(defaultValueStr)){
                    try {
                        property.defaultValue = Integer.valueOf(defaultValueStr);
                    } catch (Exception e) {
                        return Result.error("默认值错误, name:" + entity.getName() + ", defaultValue:" + defaultValueStr);
                    }
                }
                break;
            }
            case LONG: {
                String defaultValueStr = entity.getDefaultValue();
                if(defaultValueStr != null && !"".equals(defaultValueStr)){
                    try {
                        property.defaultValue = Integer.valueOf(defaultValueStr);
                    } catch (Exception e) {
                        return Result.error("默认值错误, name:" + entity.getName() + ", defaultValue:" + defaultValueStr);
                    }
                }
                break;
            }
            case DWORD: {
                String defaultValueStr = entity.getDefaultValue();
                if(defaultValueStr != null && !"".equals(defaultValueStr)){
                    try {
                        property.defaultValue = Long.valueOf(defaultValueStr);
                    } catch (Exception e) {
                        return Result.error("默认值错误, name:" + entity.getName() + ", defaultValue:" + defaultValueStr);
                    }
                }
                break;
            }
            case FLOAT: {
                String defaultValueStr = entity.getDefaultValue();
                if(defaultValueStr != null && !"".equals(defaultValueStr)){
                    try {
                        property.defaultValue = Float.valueOf(defaultValueStr);
                    } catch (Exception e) {
                        return Result.error("默认值错误, name:" + entity.getName() + ", defaultValue:" + defaultValueStr);
                    }
                }
                break;
            }
            case BCD: {
                String defaultValueStr = entity.getDefaultValue();
                if(defaultValueStr != null && !"".equals(defaultValueStr)){
                    try {
                        property.defaultValue = Integer.valueOf(defaultValueStr);
                    } catch (Exception e) {
                        return Result.error("默认值错误, name:" + entity.getName() + ", defaultValue:" + defaultValueStr);
                    }
                }
                break;
            }
            case LBCD: {
                String defaultValueStr = entity.getDefaultValue();
                if(defaultValueStr != null && !"".equals(defaultValueStr)){
                    try {
                        property.defaultValue = Integer.valueOf(defaultValueStr);
                    } catch (Exception e) {
                        return Result.error("默认值错误, name:" + entity.getName() + ", defaultValue:" + defaultValueStr);
                    }
                }
                break;
            }
            case STRING: {
                String defaultValueStr = entity.getDefaultValue();
                if(defaultValueStr != null && !"".equals(defaultValueStr)){
                    property.defaultValue = defaultValueStr;
                }
                break;
            }
            default: {

                break;
            }
        }

        return Result.ok(property);
    }


}
