delete from `thing_model` where id = 999;
delete from `thing_service` where thing_model_id = 999;

INSERT INTO `thing_model` (`id`, `name`, `model`, `descript`, `model_type`, `create_time`, `ENGINEERING_ID`, `SPACE_ID`, `MODULE_ID`, `CREATOR`, `CREATOR_ID`, `UPDATOR_ID`, `UPDATOR`, `UPDATE_TIME`, `VERSION`, `DELETED`, `tenant_id`) VALUES (999, 'Common_Type', '{\"jSON\": true, \"properties\": [{\"name\": \"deviceId1\", \"persist\": true, \"dataType\": {\"spec\": {\"max\": \"\", \"min\": \"\", \"expression\": \"labelGroupLevel3\", \"defaultValue\": \"\"}, \"type\": \"String\", \"isArray\": false}, \"descript\": \"设备ID\", \"readOnly\": true, \"required\": true, \"bindLabel\": true, \"reportType\": 1}, {\"id\": 1, \"name\": \"deviceType1\", \"persist\": true, \"dataType\": {\"spec\": {\"max\": \"\", \"min\": \"\", \"expression\": \"labelGroupLevel2\", \"defaultValue\": \"\"}, \"type\": \"String\", \"isArray\": false}, \"descript\": \"设备类型\", \"readOnly\": true, \"required\": true, \"bindLabel\": true, \"reportType\": 1}, {\"id\": 1, \"name\": \"State_Task\", \"persist\": true, \"dataType\": {\"spec\": {\"max\": \"\", \"min\": \"\", \"expression\": \"\", \"defaultValue\": \"\"}, \"type\": \"Boolean\", \"isArray\": false}, \"descript\": \"任务状态\", \"readOnly\": false, \"required\": false, \"bindLabel\": true, \"reportType\": 1}, {\"name\": \"State_Fault\", \"persist\": true, \"dataType\": {\"spec\": {\"max\": \"\", \"min\": \"\", \"expression\": \"\", \"defaultValue\": \"\"}, \"type\": \"Boolean\", \"isArray\": false}, \"descript\": \"故障状态\", \"readOnly\": false, \"required\": false, \"bindLabel\": true, \"reportType\": 1}, {\"id\": 1, \"name\": \"State\", \"persist\": true, \"dataType\": {\"spec\": {\"max\": \"\", \"min\": \"\", \"expression\": \"\", \"defaultValue\": \"\"}, \"type\": \"Short\", \"isArray\": false}, \"descript\": \"状态，1-任务，2-空闲，3-故障，4-离线\", \"readOnly\": false, \"required\": false, \"bindLabel\": true, \"reportType\": 1}, {\"id\": 3, \"name\": \"State_StandBy\", \"persist\": true, \"dataType\": {\"spec\": {\"max\": \"\", \"min\": \"\", \"expression\": \"\", \"defaultValue\": \"\"}, \"type\": \"Boolean\", \"isArray\": false}, \"descript\": \"空闲状态\", \"readOnly\": false, \"required\": false, \"bindLabel\": true, \"reportType\": 1}, {\"id\": 5, \"name\": \"State_OnLine\", \"persist\": true, \"dataType\": {\"spec\": {\"max\": \"\", \"min\": \"\", \"expression\": \"\", \"defaultValue\": \"\"}, \"type\": \"Boolean\", \"isArray\": false}, \"descript\": \"在线状态\", \"readOnly\": false, \"required\": false, \"bindLabel\": true, \"reportType\": 1}, {\"id\": 1, \"name\": \"ChannelName\", \"persist\": false, \"dataType\": {\"spec\": {\"max\": \"\", \"min\": \"\", \"expression\": \"channelName\", \"defaultValue\": \"\"}, \"type\": \"String\", \"isArray\": false}, \"descript\": \"通道名称\", \"readOnly\": true, \"required\": true, \"bindLabel\": true, \"reportType\": 1}, {\"name\": \"deviceType\", \"persist\": false, \"dataType\": {\"spec\": {\"max\": \"\", \"min\": \"\", \"expression\": \"labelGroupLevel1\", \"defaultValue\": \"\"}, \"type\": \"String\", \"isArray\": false}, \"descript\": \"驱动类型\", \"readOnly\": true, \"required\": true, \"bindLabel\": true, \"reportType\": 1}, {\"name\": \"deviceId\", \"persist\": false, \"dataType\": {\"spec\": {\"max\": \"\", \"min\": \"\", \"expression\": \"labelGroupLevel2\", \"defaultValue\": \"\"}, \"type\": \"String\", \"isArray\": false}, \"descript\": \"驱动ID\", \"readOnly\": true, \"required\": true, \"bindLabel\": true, \"reportType\": 1}]}', '基础计算模型', 1, '2025-01-01 00:00:00', 0, 0, 0, 'system', 999, 999, 'system', '2025-01-01 00:00:00', 1, 0, 999);
INSERT INTO `thing_service` (`ID`, `thing_model_id`, `service_name`, `service_type`, `descript`, `override`, `input_data`, `output_data`, `service_code`, `VERSION`, `DELETED`, `CREATOR_ID`, `CREATOR`, `CREATE_TIME`, `UPDATOR_ID`, `UPDATOR`, `UPDATE_TIME`, `ENGINEERING_ID`, `MODULE_ID`, `SPACE_ID`, `async`, `tenant_id`) VALUES (1000000000079936, 999, 'queryTaskTimeSum', 0, '查询累计任务时长', 0, '[{\"jSON\": true, \"name\": \"end\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"begin\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}]', '{\"jSON\": true, \"isArray\": null, \"dataType\": 31, \"descript\": \"\", \"dataModelId\": null, \"outputDataDescript\": \"\"}', 'var q =\n    \'queryBegin = time(v: \' + input.begin + \'+08:00)\' + \"\\n\" +\n    \'queryEnd = time(v: \' + input.end + \'+08:00)\' + \"\\n\" +\n    \'nowTime = now()\' + \"\\n\" +\n    \'nowOrEnd = if nowTime < queryEnd then nowTime else queryEnd\' + \"\\n\" +\n    \'nowOrBegin = if nowTime < queryBegin then nowTime else queryBegin\' + \"\\n\" +\n\n    \'t0 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: 0, stop: queryBegin)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and r[\"deviceId\"] == \"\' + me.id + \'\")\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Task\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrBegin  })) \' + \"\\n\" +\n\n    \'t1 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and r[\"deviceId\"] == \"\' + me.id + \'\")\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Task\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n\n    \'t2 = \' + \"\\n\" +\n    \'from(bucket: \"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and r[\"deviceId\"] == \"\' + me.id + \'\")\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Task\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrEnd  })) \' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _value: if r[\"_value\"] then false else true })) \' + \"\\n\" +\n\n    \'union(tables: [t0, t1, t2])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with val: int(v: r[\"_value\"])  })) \' + \"\\n\" +\n    \'|> difference(columns: [\"val\"], keepFirst: true)\' + \"\\n\" +\n    \'|> elapsed(unit: 1s, columnName: \"dur\", timeColumn: \"_time\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with type: \' + \"\\n\" +\n    \'        if r.property == \"State_Task\" and r.val == 1 then \"noTask\"\' + \"\\n\" +\n    \'        else if r.property == \"State_Task\" and r.val == -1 then \"task\"\' + \"\\n\" +\n    \'        else if r.property == \"State_Task\" and r.val == 0 then \' + \"\\n\" +\n    \'            if r._value then \"task\" else \"noTask\"\' + \"\\n\" +\n    \'        else \"unknow\"\' + \"\\n\" +\n    \'    })\' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\", \"dur\", \"type\"])\' + \"\\n\" +\n    \'|> filter(fn: (r) => \' + \"\\n\" +\n    \'    r[\"type\"] == \"task\" \' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> group(columns: [\"deviceId\",\"type\"])\' + \"\\n\" +\n    \'|> sum(column: \"dur\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with durSeconds: r[\"dur\"]} ))\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with durStr: string(v: duration( v: ( r[\"durSeconds\"]*1000*1000*1000 ) )) }))\' + \"\\n\";\nvar r = me.queryData(q);\nreturn r;', 1, 0, 1540174679756439554, '苏尚群', '2024-08-16 15:06:10', 1540174679756439554, '苏尚群', '2024-08-16 15:06:10', 0, 0, 0, 0, 999);
INSERT INTO `thing_service` (`ID`, `thing_model_id`, `service_name`, `service_type`, `descript`, `override`, `input_data`, `output_data`, `service_code`, `VERSION`, `DELETED`, `CREATOR_ID`, `CREATOR`, `CREATE_TIME`, `UPDATOR_ID`, `UPDATOR`, `UPDATE_TIME`, `ENGINEERING_ID`, `MODULE_ID`, `SPACE_ID`, `async`, `tenant_id`) VALUES (1000000000079937, 999, 'queryFaultTimeSum', 0, '查询累计故障时长', 0, '[{\"jSON\": true, \"name\": \"end\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"begin\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}]', '{\"jSON\": true, \"isArray\": null, \"dataType\": 31, \"descript\": \"\", \"dataModelId\": null, \"outputDataDescript\": \"\"}', 'var q =\n    \'queryBegin = time(v: \' + input.begin + \'+08:00)\' + \"\\n\" +\n    \'queryEnd = time(v: \' + input.end + \'+08:00)\' + \"\\n\" +\n    \'nowTime = now()\' + \"\\n\" +\n    \'nowOrEnd = if nowTime < queryEnd then nowTime else queryEnd\' + \"\\n\" +\n    \'nowOrBegin = if nowTime < queryBegin then nowTime else queryBegin\' + \"\\n\" +\n\n    \'t0 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: 0, stop: queryBegin)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and r[\"deviceId\"] == \"\' + me.id + \'\")\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Fault\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrBegin  })) \' + \"\\n\" +\n\n    \'t1 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and r[\"deviceId\"] == \"\' + me.id + \'\")\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Fault\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n\n    \'t2 = \' + \"\\n\" +\n    \'from(bucket: \"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and r[\"deviceId\"] == \"\' + me.id + \'\")\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Fault\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrEnd  })) \' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _value: if r[\"_value\"] then false else true })) \' + \"\\n\" +\n\n    \'union(tables: [t0, t1, t2])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with val: int(v: r[\"_value\"])  })) \' + \"\\n\" +\n    \'|> difference(columns: [\"val\"], keepFirst: true)\' + \"\\n\" +\n    \'|> elapsed(unit: 1s, columnName: \"dur\", timeColumn: \"_time\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with type: \' + \"\\n\" +\n    \'        if r.property == \"State_Fault\" and r.val == 1 then \"noFault\"\' + \"\\n\" +\n    \'        else if r.property == \"State_Fault\" and r.val == -1 then \"fault\"\' + \"\\n\" +\n    \'        else if r.property == \"State_Fault\" and r.val == 0 then \' + \"\\n\" +\n    \'            if r._value then \"fault\" else \"noFault\"\' + \"\\n\" +\n    \'        else \"unknow\"\' + \"\\n\" +\n    \'    })\' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\", \"dur\", \"type\"])\' + \"\\n\" +\n    \'|> filter(fn: (r) => \' + \"\\n\" +\n    \'    r[\"type\"] == \"fault\" \' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> group(columns: [\"deviceId\",\"type\"])\' + \"\\n\" +\n    \'|> sum(column: \"dur\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with durSeconds: r[\"dur\"]} ))\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with durStr: string(v: duration( v: ( r[\"durSeconds\"]*1000*1000*1000 ) )) }))\' + \"\\n\";\nvar r = me.queryData(q);\nreturn r;', 1, 0, 1540174679756439554, '苏尚群', '2024-08-16 15:06:10', 1540174679756439554, '苏尚群', '2024-08-16 15:06:10', 0, 0, 0, 0, 999);
INSERT INTO `thing_service` (`ID`, `thing_model_id`, `service_name`, `service_type`, `descript`, `override`, `input_data`, `output_data`, `service_code`, `VERSION`, `DELETED`, `CREATOR_ID`, `CREATOR`, `CREATE_TIME`, `UPDATOR_ID`, `UPDATOR`, `UPDATE_TIME`, `ENGINEERING_ID`, `MODULE_ID`, `SPACE_ID`, `async`, `tenant_id`) VALUES (1000000000079938, 999, 'queryFaultTimeAvg', 0, '查询平均故障时长', 0, '[{\"jSON\": true, \"name\": \"end\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"begin\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}]', '{\"jSON\": true, \"isArray\": null, \"dataType\": 31, \"descript\": \"\", \"dataModelId\": null, \"outputDataDescript\": \"\"}', 'var q =\n    \'queryBegin = time(v: \' + input.begin + \'+08:00)\' + \"\\n\" +\n    \'queryEnd = time(v: \' + input.end + \'+08:00)\' + \"\\n\" +\n    \'nowTime = now()\' + \"\\n\" +\n    \'nowOrEnd = if nowTime < queryEnd then nowTime else queryEnd\' + \"\\n\" +\n    \'nowOrBegin = if nowTime < queryBegin then nowTime else queryBegin\' + \"\\n\" +\n\n    \'t0 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: 0, stop: queryBegin)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and r[\"deviceId\"] == \"\' + me.id + \'\")\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Fault\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrBegin  })) \' + \"\\n\" +\n\n    \'t1 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and r[\"deviceId\"] == \"\' + me.id + \'\")\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Fault\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n\n    \'t2 = \' + \"\\n\" +\n    \'from(bucket: \"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and r[\"deviceId\"] == \"\' + me.id + \'\")\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Fault\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrEnd  })) \' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _value: if r[\"_value\"] then false else true })) \' + \"\\n\" +\n\n    \'union(tables: [t0, t1, t2])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with val: int(v: r[\"_value\"])  })) \' + \"\\n\" +\n    \'|> difference(columns: [\"val\"], keepFirst: true)\' + \"\\n\" +\n    \'|> elapsed(unit: 1s, columnName: \"dur\", timeColumn: \"_time\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with type: \' + \"\\n\" +\n    \'        if r.property == \"State_Fault\" and r.val == 1 then \"noFault\"\' + \"\\n\" +\n    \'        else if r.property == \"State_Fault\" and r.val == -1 then \"fault\"\' + \"\\n\" +\n    \'        else if r.property == \"State_Fault\" and r.val == 0 then \' + \"\\n\" +\n    \'            if r._value then \"fault\" else \"noFault\"\' + \"\\n\" +\n    \'        else \"unknow\"\' + \"\\n\" +\n    \'    })\' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\", \"dur\", \"type\"])\' + \"\\n\" +\n    \'|> filter(fn: (r) => \' + \"\\n\" +\n    \'    r[\"type\"] == \"fault\" \' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> reduce(\' + \"\\n\" +\n    \'    fn: (r, accumulator) => ({\' + \"\\n\" +\n    \'        countValue: accumulator.countValue + 1, \' + \"\\n\" +\n    \'        sumValue: r.dur + accumulator.sumValue\' + \"\\n\" +\n    \'    }),\' + \"\\n\" +\n    \'    identity: {countValue: 0, sumValue: 0},\' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with faultAvg: r[\"sumValue\"]/r[\"countValue\"] }))\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with faultAvgSeconds: r[\"faultAvg\"]} ))\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with faultAvgStr: string(v: duration( v: ( r[\"faultAvgSeconds\"]*1000*1000*1000 ) )) }))\' + \"\\n\";\n\nvar r = me.queryData(q);\nreturn r;', 1, 0, 1540174679756439554, '苏尚群', '2024-08-16 15:06:10', 1540174679756439554, '苏尚群', '2024-08-16 15:06:10', 0, 0, 0, 0, 999);
INSERT INTO `thing_service` (`ID`, `thing_model_id`, `service_name`, `service_type`, `descript`, `override`, `input_data`, `output_data`, `service_code`, `VERSION`, `DELETED`, `CREATOR_ID`, `CREATOR`, `CREATE_TIME`, `UPDATOR_ID`, `UPDATOR`, `UPDATE_TIME`, `ENGINEERING_ID`, `MODULE_ID`, `SPACE_ID`, `async`, `tenant_id`) VALUES (1000000000079939, 999, 'queryFaultRate', 0, '查询故障率', 0, '[{\"jSON\": true, \"name\": \"end\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"begin\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}]', '{\"jSON\": true, \"isArray\": null, \"dataType\": 31, \"descript\": \"\", \"dataModelId\": null, \"outputDataDescript\": \"\"}', 'var q =\n    \'queryBegin = time(v: \' + input.begin + \'+08:00)\' + \"\\n\" +\n    \'queryEnd = time(v: \' + input.end + \'+08:00)\' + \"\\n\" +\n    \'nowTime = now()\' + \"\\n\" +\n    \'nowOrEnd = if nowTime < queryEnd then nowTime else queryEnd\' + \"\\n\" +\n    \'nowOrBegin = if nowTime < queryBegin then nowTime else queryBegin\' + \"\\n\" +\n\n    \'t0 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: 0, stop: queryBegin)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and r[\"deviceId\"] == \"\' + me.id + \'\")\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Fault\" or r[\"property\"] == \"State_OnLine\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrBegin  })) \' + \"\\n\" +\n\n    \'t1 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and r[\"deviceId\"] == \"\' + me.id + \'\")\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Fault\" or r[\"property\"] == \"State_OnLine\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n\n    \'t2 = \' + \"\\n\" +\n    \'from(bucket: \"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and r[\"deviceId\"] == \"\' + me.id + \'\")\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Fault\" or r[\"property\"] == \"State_OnLine\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrEnd  })) \' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _value: if r[\"_value\"] then false else true })) \' + \"\\n\" +\n\n    \'union(tables: [t0, t1, t2])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with val: int(v: r[\"_value\"])  })) \' + \"\\n\" +\n    \'|> difference(columns: [\"val\"], keepFirst: true)\' + \"\\n\" +\n    \'|> elapsed(unit: 1s, columnName: \"dur\", timeColumn: \"_time\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with type: \' + \"\\n\" +\n    \'        if r.property == \"State_OnLine\" and r.val == 1 then \"offline\" \' + \"\\n\" +\n    \'        else if r.property == \"State_OnLine\" and r.val == -1 then \"online\"\' + \"\\n\" +\n    \'        else if r.property == \"State_OnLine\" and r.val == 0 then \' + \"\\n\" +\n    \'            if r._value then \"online\" else \"offline\"\' + \"\\n\" +\n    \'        else if r.property == \"State_Fault\" and r.val == 1 then \"noFault\"\' + \"\\n\" +\n    \'        else if r.property == \"State_Fault\" and r.val == -1 then \"fault\"\' + \"\\n\" +\n    \'        else if r.property == \"State_Fault\" and r.val == 0 then \' + \"\\n\" +\n    \'            if r._value then \"fault\" else \"noFault\"\' + \"\\n\" +\n    \'        else \"unknow\"\' + \"\\n\" +\n    \'    })\' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\", \"dur\", \"type\"])\' + \"\\n\" +\n    \'|> filter(fn: (r) => \' + \"\\n\" +\n    \'    r[\"type\"] == \"fault\" \' + \"\\n\" +\n    \'    or r[\"type\"] == \"online\" \' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> reduce(\' + \"\\n\" +\n    \'    fn: (r, accumulator) => ({\' + \"\\n\" +\n    \'        faultSum: (if r.type == \"fault\" then r.dur else 0) + accumulator.faultSum, \' + \"\\n\" +\n    \'        onlineSum: (if r.type == \"online\" then r.dur else 0) + accumulator.onlineSum\' + \"\\n\" +\n    \'    }),\' + \"\\n\" +\n    \'    identity: {faultSum: 0, onlineSum: 0},\' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with faultRate: float(v: r[\"faultSum\"])/float(v: r[\"onlineSum\"]) }))\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with faultRate: if r.faultRate > 1.0 then 1.0 else r.faultRate }))\' + \"\\n\";\n\nvar r = me.queryData(q);\nreturn r;', 1, 0, 1540174679756439554, '苏尚群', '2024-08-16 15:06:10', 1540174679756439554, '苏尚群', '2024-08-16 15:06:10', 0, 0, 0, 0, 999);
INSERT INTO `thing_service` (`ID`, `thing_model_id`, `service_name`, `service_type`, `descript`, `override`, `input_data`, `output_data`, `service_code`, `VERSION`, `DELETED`, `CREATOR_ID`, `CREATOR`, `CREATE_TIME`, `UPDATOR_ID`, `UPDATOR`, `UPDATE_TIME`, `ENGINEERING_ID`, `MODULE_ID`, `SPACE_ID`, `async`, `tenant_id`) VALUES (1000000000079940, 999, 'queryUseRate', 0, '查询利用率', 0, '[{\"jSON\": true, \"name\": \"end\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"begin\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}]', '{\"jSON\": true, \"isArray\": null, \"dataType\": 31, \"descript\": \"\", \"dataModelId\": null, \"outputDataDescript\": \"\"}', 'var q =\n    \'queryBegin = time(v: \' + input.begin + \'+08:00)\' + \"\\n\" +\n    \'queryEnd = time(v: \' + input.end + \'+08:00)\' + \"\\n\" +\n    \'nowTime = now()\' + \"\\n\" +\n    \'nowOrEnd = if nowTime < queryEnd then nowTime else queryEnd\' + \"\\n\" +\n    \'nowOrBegin = if nowTime < queryBegin then nowTime else queryBegin\' + \"\\n\" +\n\n    \'t0 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: 0, stop: queryBegin)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and r[\"deviceId\"] == \"\' + me.id + \'\")\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Task\" or r[\"property\"] == \"State_OnLine\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrBegin  })) \' + \"\\n\" +\n\n    \'t1 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and r[\"deviceId\"] == \"\' + me.id + \'\")\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Task\" or r[\"property\"] == \"State_OnLine\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n\n    \'t2 = \' + \"\\n\" +\n    \'from(bucket: \"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and r[\"deviceId\"] == \"\' + me.id + \'\")\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Task\" or r[\"property\"] == \"State_OnLine\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrEnd  })) \' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _value: if r[\"_value\"] then false else true })) \' + \"\\n\" +\n\n    \'union(tables: [t0, t1, t2])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with val: int(v: r[\"_value\"])  })) \' + \"\\n\" +\n    \'|> difference(columns: [\"val\"], keepFirst: true)\' + \"\\n\" +\n    \'|> elapsed(unit: 1s, columnName: \"dur\", timeColumn: \"_time\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with type: \' + \"\\n\" +\n    \'        if r.property == \"State_OnLine\" and r.val == 1 then \"offline\" \' + \"\\n\" +\n    \'        else if r.property == \"State_OnLine\" and r.val == -1 then \"online\"\' + \"\\n\" +\n    \'        else if r.property == \"State_OnLine\" and r.val == 0 then \' + \"\\n\" +\n    \'            if r._value then \"online\" else \"offline\"\' + \"\\n\" +\n    \'        else if r.property == \"State_Task\" and r.val == 1 then \"noTask\"\' + \"\\n\" +\n    \'        else if r.property == \"State_Task\" and r.val == -1 then \"task\"\' + \"\\n\" +\n    \'        else if r.property == \"State_Task\" and r.val == 0 then \' + \"\\n\" +\n    \'            if r._value then \"task\" else \"noTask\"\' + \"\\n\" +\n    \'        else \"unknow\"\' + \"\\n\" +\n    \'    })\' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\", \"dur\", \"type\"])\' + \"\\n\" +\n    \'|> filter(fn: (r) => \' + \"\\n\" +\n    \'    r[\"type\"] == \"task\" \' + \"\\n\" +\n    \'    or r[\"type\"] == \"online\" \' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> reduce(\' + \"\\n\" +\n    \'    fn: (r, accumulator) => ({\' + \"\\n\" +\n    \'        taskSum: (if r.type == \"task\" then r.dur else 0) + accumulator.taskSum, \' + \"\\n\" +\n    \'        onlineSum: (if r.type == \"online\" then r.dur else 0) + accumulator.onlineSum\' + \"\\n\" +\n    \'    }),\' + \"\\n\" +\n    \'    identity: {taskSum: 0, onlineSum: 0},\' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with useRate: if r.onlineSum > 0 then (float(v: r[\"taskSum\"])/float(v: r[\"onlineSum\"]))  else 0.0 }))\' + \"\\n\";\n\nvar r = me.queryData(q);\nreturn r;', 1, 0, 1540174679756439554, '苏尚群', '2024-08-16 15:06:10', 1540174679756439554, '苏尚群', '2024-08-16 15:06:10', 0, 0, 0, 0, 999);
INSERT INTO `thing_service` (`ID`, `thing_model_id`, `service_name`, `service_type`, `descript`, `override`, `input_data`, `output_data`, `service_code`, `VERSION`, `DELETED`, `CREATOR_ID`, `CREATOR`, `CREATE_TIME`, `UPDATOR_ID`, `UPDATOR`, `UPDATE_TIME`, `ENGINEERING_ID`, `MODULE_ID`, `SPACE_ID`, `async`, `tenant_id`) VALUES (1000000000079941, 999, 'queryFaultCountByWindow', 0, '查询分组故障次数', 0, '[{\"jSON\": true, \"name\": \"every\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"end\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"begin\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}]', '{\"jSON\": true, \"isArray\": null, \"dataType\": 31, \"descript\": \"\", \"dataModelId\": null, \"outputDataDescript\": \"\"}', 'var q =\n    \'import \"date\"\' + \"\\n\" +\n    \'queryBegin = time(v: \' + input.begin + \'+08:00)\' + \"\\n\" +\n    \'queryEnd = time(v: \' + input.end + \'+08:00)\' + \"\\n\" +\n    \'nowTime = now()\' + \"\\n\" +\n    \'nowOrEnd = if nowTime < queryEnd then nowTime else queryEnd\' + \"\\n\" +\n    \'nowOrBegin = if nowTime < queryBegin then nowTime else queryBegin\' + \"\\n\" +\n\n    \'t0 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: 0, stop: queryBegin)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and r[\"deviceId\"] == \"\' + me.id + \'\")\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Fault\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrBegin  })) \' + \"\\n\" +\n\n    \'t1 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and r[\"deviceId\"] == \"\' + me.id + \'\")\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Fault\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n\n    \'t2 = \' + \"\\n\" +\n    \'from(bucket: \"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and r[\"deviceId\"] == \"\' + me.id + \'\")\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Fault\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrEnd  })) \' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _value: if r[\"_value\"] then false else true })) \' + \"\\n\" +\n\n    \'union(tables: [t0, t1, t2])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with val: int(v: r[\"_value\"])  })) \' + \"\\n\" +\n    \'|> difference(columns: [\"val\"], keepFirst: true)\' + \"\\n\" +\n    \'|> elapsed(unit: 1s, columnName: \"dur\", timeColumn: \"_time\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with type: \' + \"\\n\" +\n    \'        if r.property == \"State_Fault\" and r.val == 1 then \"noFault\"\' + \"\\n\" +\n    \'        else if r.property == \"State_Fault\" and r.val == -1 then \"fault\"\' + \"\\n\" +\n    \'        else if r.property == \"State_Fault\" and r.val == 0 then \' + \"\\n\" +\n    \'            if r._value then \"fault\" else \"noFault\"\' + \"\\n\" +\n    \'        else \"unknow\"\' + \"\\n\" +\n    \'    })\' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\", \"dur\", \"type\"])\' + \"\\n\" +\n    \'|> filter(fn: (r) => \' + \"\\n\" +\n    \'    r[\"type\"] == \"fault\" \' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> aggregateWindow(every: \' + input.every + \', fn: count, createEmpty: false, offset: -8h, column: \"dur\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with faultCount: r[\"dur\"] }))\' + \"\\n\" +\n    \'|> drop(columns: [\"_start\",\"_stop\", \"dur\"])\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: date.sub(d: 1s, from: r[\"_time\"])  }))\';\nvar r = me.queryData(q);\nreturn r;', 1, 0, 1540174679756439554, '苏尚群', '2024-08-16 15:06:10', 1540174679756439554, '苏尚群', '2024-08-16 15:06:10', 0, 0, 0, 0, 999);
INSERT INTO `thing_service` (`ID`, `thing_model_id`, `service_name`, `service_type`, `descript`, `override`, `input_data`, `output_data`, `service_code`, `VERSION`, `DELETED`, `CREATOR_ID`, `CREATOR`, `CREATE_TIME`, `UPDATOR_ID`, `UPDATOR`, `UPDATE_TIME`, `ENGINEERING_ID`, `MODULE_ID`, `SPACE_ID`, `async`, `tenant_id`) VALUES (1000000000079942, 999, 'queryUseRateByWindow', 0, '查询分组利用率', 0, '[{\"jSON\": true, \"name\": \"every\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"end\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"begin\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}]', '{\"jSON\": true, \"isArray\": null, \"dataType\": 31, \"descript\": \"\", \"dataModelId\": null, \"outputDataDescript\": \"\"}', 'var q =\n    \'import \"date\"\' + \"\\n\" +\n    \'queryBegin = time(v: \' + input.begin + \'+08:00)\' + \"\\n\" +\n    \'queryEnd = time(v: \' + input.end + \'+08:00)\' + \"\\n\" +\n    \'nowTime = now()\' + \"\\n\" +\n    \'nowOrEnd = if nowTime < queryEnd then nowTime else queryEnd\' + \"\\n\" +\n    \'nowOrBegin = if nowTime < queryBegin then nowTime else queryBegin\' + \"\\n\" +\n\n    \'t0 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: 0, stop: queryBegin)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and r[\"deviceId\"] == \"\' + me.id + \'\")\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Task\" or r[\"property\"] == \"State_OnLine\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrBegin  })) \' + \"\\n\" +\n\n    \'t1 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and r[\"deviceId\"] == \"\' + me.id + \'\")\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Task\" or r[\"property\"] == \"State_OnLine\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n\n    \'t2 = \' + \"\\n\" +\n    \'from(bucket: \"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and r[\"deviceId\"] == \"\' + me.id + \'\")\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Task\" or r[\"property\"] == \"State_OnLine\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrEnd  })) \' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _value: if r[\"_value\"] then false else true })) \' + \"\\n\" +\n\n    \'t3 =\' + \"\\n\" +\n    \'union(tables: [t0, t1, t2])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n\n    \'t4 = t3\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> window(every: \' + input.every + \', offset: -8h)\' + \"\\n\" +\n    \'|> last()\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: date.sub(d: 1s, from: r[\"_stop\"])  }))\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n\n    \'t5 = \' + \"\\n\" +\n    \'union(tables: [t3, t4])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n\n    \'t5\' + \"\\n\" +\n\n    \'|> map(fn: (r) => ({ r with val: int(v: r[\"_value\"])  })) \' + \"\\n\" +\n    \'|> difference(columns: [\"val\"], keepFirst: true)\' + \"\\n\" +\n    \'|> elapsed(unit: 1s, columnName: \"dur\", timeColumn: \"_time\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with type: \' + \"\\n\" +\n    \'        if r.property == \"State_Task\" and r.val == 1 then \"offline\" \' + \"\\n\" +\n    \'        else if r.property == \"State_OnLine\" and r.val == -1 then \"online\"\' + \"\\n\" +\n    \'        else if r.property == \"State_OnLine\" and r.val == 0 then \' + \"\\n\" +\n    \'            if r._value then \"online\" else \"offline\"\' + \"\\n\" +\n    \'        else if r.property == \"State_Task\" and r.val == 1 then \"noTask\"\' + \"\\n\" +\n    \'        else if r.property == \"State_Task\" and r.val == -1 then \"task\"\' + \"\\n\" +\n    \'        else if r.property == \"State_Task\" and r.val == 0 then \' + \"\\n\" +\n    \'            if r._value then \"task\" else \"noTask\"\' + \"\\n\" +\n    \'        else \"unknow\"\' + \"\\n\" +\n    \'    })\' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\", \"dur\", \"type\"])\' + \"\\n\" +\n    \'|> filter(fn: (r) => \' + \"\\n\" +\n    \'    r[\"type\"] == \"task\" \' + \"\\n\" +\n    \'    or r[\"type\"] == \"online\" \' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> aggregateWindow(every: \' + input.every + \', createEmpty: false, offset: -8h, column: \"dur\", fn: (column, tables=<-) => tables \' + \"\\n\" +\n    \'|> reduce(\' + \"\\n\" +\n    \'        fn: (r, accumulator) => ({\' + \"\\n\" +\n    \'            taskSum: (if r.type == \"task\" then r.dur else 0) + accumulator.taskSum, \' + \"\\n\" +\n    \'            onlineSum: (if r.type == \"online\" then r.dur else 0) + accumulator.onlineSum\' + \"\\n\" +\n    \'        }),\' + \"\\n\" +\n    \'        identity: {taskSum: 0, onlineSum: 0},\' + \"\\n\" +\n    \'    )\' + \"\\n\" +\n    \'    |> map(fn: (r) => ({ r with useRate: if r.onlineSum > 0 then (float(v: r[\"taskSum\"])/float(v: r[\"onlineSum\"]))  else 0.0 }))\' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: date.sub(d: 1s, from: r[\"_time\"])  }))\';\nvar r = me.queryData(q);\nreturn r;', 1, 0, 1540174679756439554, '苏尚群', '2024-08-16 15:06:10', 1540174679756439554, '苏尚群', '2024-08-16 15:06:10', 0, 0, 0, 0, 999);
INSERT INTO `thing_service` (`ID`, `thing_model_id`, `service_name`, `service_type`, `descript`, `override`, `input_data`, `output_data`, `service_code`, `VERSION`, `DELETED`, `CREATOR_ID`, `CREATOR`, `CREATE_TIME`, `UPDATOR_ID`, `UPDATOR`, `UPDATE_TIME`, `ENGINEERING_ID`, `MODULE_ID`, `SPACE_ID`, `async`, `tenant_id`) VALUES (1000000000079943, 999, 'queryDevicesUseRateByWindow', 0, '查询多设备分组利用率', 0, '[{\"jSON\": true, \"name\": \"deviceIds\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"every\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"end\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"begin\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}]', '{\"jSON\": true, \"isArray\": null, \"dataType\": 31, \"descript\": \"\", \"dataModelId\": null, \"outputDataDescript\": \"\"}', 'var ids = input.deviceIds;\nvar idArray = ids.split(\',\');\n\n// var tmpArray = [];\n// for (var i = 0; i < idArray.length; i++) {\n//     tmpArray.push(\'r[\"deviceId\"] == \"\' + idArray[i] + \'\"\');\n// }\n// var idsStr = tmpArray.join(\' or \');\n\nvar idsStr = \'r[\"deviceId\"] =~ /\' + idArray.join(\'|\') + \'/\';\n\nvar q =\n    \'// queryDevicesUseRateByWindow\' + \"\\n\" +\n    \'import \"date\"\' + \"\\n\" +\n    \'queryBegin = time(v: \' + input.begin + \'+08:00)\' + \"\\n\" +\n    \'queryEnd = time(v: \' + input.end + \'+08:00)\' + \"\\n\" +\n    \'nowTime = now()\' + \"\\n\" +\n    \'nowOrEnd = if nowTime < queryEnd then nowTime else queryEnd\' + \"\\n\" +\n    \'nowOrBegin = if nowTime < queryBegin then nowTime else queryBegin\' + \"\\n\" +\n\n    \'t0 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: 0, stop: queryBegin)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and ( \' + idsStr + \' ) )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Task\" or r[\"property\"] == \"State_OnLine\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrBegin  })) \' + \"\\n\" +\n\n    \'t1 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and ( \' + idsStr + \' ) )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Task\" or r[\"property\"] == \"State_OnLine\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n\n    \'t2 = \' + \"\\n\" +\n    \'from(bucket: \"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and ( \' + idsStr + \' ) )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Task\" or r[\"property\"] == \"State_OnLine\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrEnd  })) \' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _value: if r[\"_value\"] then false else true })) \' + \"\\n\" +\n\n    \'t3 =\' + \"\\n\" +\n    \'union(tables: [t0, t1, t2])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n\n    \'t4 = t3\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> window(every: \' + input.every + \', offset: -8h)\' + \"\\n\" +\n    \'|> last()\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: date.sub(d: 1s, from: r[\"_stop\"])  }))\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n\n    \'t5 = \' + \"\\n\" +\n    \'union(tables: [t3, t4])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n\n    \'t5\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with val: int(v: r[\"_value\"])  })) \' + \"\\n\" +\n    \'|> difference(columns: [\"val\"], keepFirst: true)\' + \"\\n\" +\n    \'|> elapsed(unit: 1s, columnName: \"dur\", timeColumn: \"_time\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with type: \' + \"\\n\" +\n    \'        if r.property == \"State_OnLine\" and r.val == 1 then \"offline\" \' + \"\\n\" +\n    \'        else if r.property == \"State_OnLine\" and r.val == -1 then \"online\"\' + \"\\n\" +\n    \'        else if r.property == \"State_OnLine\" and r.val == 0 then \' + \"\\n\" +\n    \'            if r._value then \"online\" else \"offline\"\' + \"\\n\" +\n    \'        else if r.property == \"State_Task\" and r.val == 1 then \"noTask\"\' + \"\\n\" +\n    \'        else if r.property == \"State_Task\" and r.val == -1 then \"task\"\' + \"\\n\" +\n    \'        else if r.property == \"State_Task\" and r.val == 0 then \' + \"\\n\" +\n    \'            if r._value then \"task\" else \"noTask\"\' + \"\\n\" +\n    \'        else \"unknow\"\' + \"\\n\" +\n    \'    })\' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\", \"dur\", \"type\"])\' + \"\\n\" +\n    \'|> filter(fn: (r) => \' + \"\\n\" +\n    \'    r[\"type\"] == \"task\" \' + \"\\n\" +\n    \'    or r[\"type\"] == \"online\" \' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> aggregateWindow(every: \' + input.every + \', createEmpty: false, column: \"dur\", offset: -8h, fn: (column, tables=<-) => tables \' + \"\\n\" +\n    \'|> reduce(\' + \"\\n\" +\n    \'        fn: (r, accumulator) => ({\' + \"\\n\" +\n    \'            taskSum: (if r.type == \"task\" then r.dur else 0) + accumulator.taskSum, \' + \"\\n\" +\n    \'            onlineSum: (if r.type == \"online\" then r.dur else 0) + accumulator.onlineSum\' + \"\\n\" +\n    \'        }),\' + \"\\n\" +\n    \'        identity: {taskSum: 0, onlineSum: 0},\' + \"\\n\" +\n    \'    )\' + \"\\n\" +\n    \'    |> map(fn: (r) => ({ r with useRate: if r.onlineSum > 0 then (float(v: r[\"taskSum\"])/float(v: r[\"onlineSum\"]))  else 0.0 }))\' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: date.sub(d: 1s, from: r[\"_time\"])  }))\';\nvar r = me.queryData(q);\nreturn r;', 1, 0, 1540174679756439554, '苏尚群', '2024-08-16 15:06:10', 1540174679756439554, '苏尚群', '2024-08-16 15:06:10', 0, 0, 0, 0, 999);
INSERT INTO `thing_service` (`ID`, `thing_model_id`, `service_name`, `service_type`, `descript`, `override`, `input_data`, `output_data`, `service_code`, `VERSION`, `DELETED`, `CREATOR_ID`, `CREATOR`, `CREATE_TIME`, `UPDATOR_ID`, `UPDATOR`, `UPDATE_TIME`, `ENGINEERING_ID`, `MODULE_ID`, `SPACE_ID`, `async`, `tenant_id`) VALUES (1000000000079944, 999, 'queryOnLineTimeByWindow', 0, '查询分组累计在线时长', 0, '[{\"jSON\": true, \"name\": \"end\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"begin\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}]', '{\"jSON\": true, \"isArray\": null, \"dataType\": 31, \"descript\": \"\", \"dataModelId\": null, \"outputDataDescript\": \"\"}', 'var q =\n    \'import \"experimental/table\"\' + \"\\n\" +\n    \'import \"date\"\' + \"\\n\" +\n    \'import \"contrib/tomhollingworth/events\"\' + \"\\n\" +\n\n    \'queryBegin = time(v: \' + input.begin + \'+08:00)\' + \"\\n\" +\n    \'queryEnd = time(v: \' + input.end + \'+08:00)\' + \"\\n\" +\n    \'nowTime = now()\' + \"\\n\" +\n    \'nowOrEnd = if nowTime < queryEnd then nowTime else queryEnd\' + \"\\n\" +\n    \'nowOrBegin = if nowTime < queryBegin then nowTime else queryBegin\' + \"\\n\" +\n    \'t0 =\' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: 0, stop: queryBegin)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"deviceId\"] == \"\' + me.id + \'\")\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_OnLine\" )\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: queryBegin  }))\' + \"\\n\" +\n\n\n    \'t1 =\' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: nowOrEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"deviceId\"] == \"\' + me.id + \'\")\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_OnLine\")\' + \"\\n\" +\n    \'|> window(every: 1d, createEmpty: true)\' + \"\\n\" +\n    \'|> table.fill()\' + \"\\n\" +\n    \'|> fill(usePrevious: true)\' + \"\\n\" +\n\n\n    \'union(tables: [t0, t1])|> group()\' + \"\\n\" +\n    \'|> map (fn: (r) => ({ r with _time: if not(exists r._value) then r._start else r._time}))\' + \"\\n\" +\n    \'|> sort(columns:[\"deviceId\",\"_start\"])\' + \"\\n\" +\n    \'|> events.duration(unit: 1s,stop:nowOrEnd)\' + \"\\n\" +\n    \'|> map (fn: (r) => ({ r with\' + \"\\n\" +\n    \'       startTime: uint(v: r._start),\' + \"\\n\" +\n    \'       stopTime: uint(v: r._stop),\' + \"\\n\" +\n    \'       timeTime: uint(v: r._time),\' + \"\\n\" +\n    \'       time_to_stop: (int(v: r._stop) - int(v: r._time))/1000/1000/1000 ,\' + \"\\n\" +\n    \'       start_to_time: (int(v: r._time) - int(v: r._start))/1000/1000/1000,\' + \"\\n\" +\n    \'       duration: if r.duration<0 then  (int(v: nowOrEnd) - int(v: r._time))/1000/1000/1000 else r.duration\' + \"\\n\" +\n    \'        }))\' + \"\\n\" +\n    \'|> fill(usePrevious: true)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_start\"] > time(v: 0) )\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\", \"_value\",\"_start\",\"_stop\",\"duration\",\"start_to_time\",\"startTime\",\"stopTime\",\"time_to_stop\",\"timeTime\",\"deviceId\"])\' + \"\\n\" +\n    \'|> group(columns:[\"_start\",\"deviceId\"])\' + \"\\n\" +\n    \'|> reduce( fn: (r, accumulator) => ( {\' + \"\\n\" +\n    \'    start:r._start,\' + \"\\n\" +\n    \'    preItem: r._value,\' + \"\\n\" +\n    \'    deviceId: r.deviceId,\' + \"\\n\" +\n    \'    spend:\' + \"\\n\" +\n    \'        if r._value ==false and accumulator.preItem==false and r.start_to_time!=0 then accumulator.spend + r.start_to_time else\' + \"\\n\" +\n    \'        if r._value ==false and accumulator.preItem==true and r.start_to_time!=0 then accumulator.spend else\' + \"\\n\" +\n    \'        if r._value ==true  and r._time==r._start and r.start_to_time==0 then\' + \"\\n\" +\n    \'               if  r.duration>r.time_to_stop  then accumulator.spend+r.time_to_stop else accumulator.spend+r.duration\' + \"\\n\" +\n    \'        else\' + \"\\n\" +\n    \'        if r._value ==true  and accumulator.preItem==false and r.start_to_time!=0  then\' + \"\\n\" +\n    \'          if  r.duration>r.time_to_stop  then accumulator.spend+r.time_to_stop else accumulator.spend+r.duration\' + \"\\n\" +\n    \'        else\' + \"\\n\" +\n    \'          accumulator.spend\' + \"\\n\" +\n    \'} ), identity: {preItem:false,spend: 0,start:time(v: 2025-06-03T14:53:39+08:00),deviceId:\"\"})\' + \"\\n\" +\n    \'|> group(columns:[\"_start\"])\' + \"\\n\" +\n    \'|> mean(column:\"spend\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({r with _time: r._start,onlineSumSeconds:int(v:r.spend),onlineSumStr:string(v:duration(v:int(v:r.spend)*1000*1000*1000))}))\' + \"\\n\" +\n    \'|> group()\';\n\n\n\nvar r = me.queryData(q);\nreturn r;', 3, 0, 1540174679756439554, '苏尚群', '2024-08-16 15:06:10', 1566614544172888065, '苏尚群', '2025-06-12 16:15:31', 0, 0, 0, 0, 999);
INSERT INTO `thing_service` (`ID`, `thing_model_id`, `service_name`, `service_type`, `descript`, `override`, `input_data`, `output_data`, `service_code`, `VERSION`, `DELETED`, `CREATOR_ID`, `CREATOR`, `CREATE_TIME`, `UPDATOR_ID`, `UPDATOR`, `UPDATE_TIME`, `ENGINEERING_ID`, `MODULE_ID`, `SPACE_ID`, `async`, `tenant_id`) VALUES (1000000000079945, 999, 'queryDevicesOnLineTimeByWindow', 0, '查询多设备分组累计在线时长', 0, '[{\"jSON\": true, \"name\": \"deviceIds\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"every\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"end\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"begin\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}]', '{\"jSON\": true, \"isArray\": null, \"dataType\": 31, \"descript\": \"\", \"dataModelId\": null, \"outputDataDescript\": \"\"}', 'var ids = input.deviceIds;\nvar idArray = ids.split(\',\');\n\nvar idsStr = \'r[\"deviceId\"] =~ /\' + idArray.join(\'|\') + \'/\';\n\nvar q =\n    \'import \"experimental/table\"\' + \"\\n\" +\n    \'import \"date\"\' + \"\\n\" +\n    \'import \"contrib/tomhollingworth/events\"\' + \"\\n\" +\n\n    \'queryBegin = time(v: \' + input.begin + \'+08:00)\' + \"\\n\" +\n    \'queryEnd = time(v: \' + input.end + \'+08:00)\' + \"\\n\" +\n    \'nowTime = now()\' + \"\\n\" +\n    \'nowOrEnd = if nowTime < queryEnd then nowTime else queryEnd\' + \"\\n\" +\n    \'nowOrBegin = if nowTime < queryBegin then nowTime else queryBegin\' + \"\\n\" +\n    \'t0 =\' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: 0, stop: queryBegin)\' + \"\\n\" +\n    \'|> filter(fn: (r) =>  (\' + idsStr + \') )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_OnLine\" )\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: queryBegin  }))\' + \"\\n\" +\n\n\n    \'t1 =\' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: nowOrEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) =>  (\' + idsStr + \') )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_OnLine\")\' + \"\\n\" +\n    \'|> window(every: 1d, createEmpty: true)\' + \"\\n\" +\n    \'|> table.fill()\' + \"\\n\" +\n    \'|> fill(usePrevious: true)\' + \"\\n\" +\n\n\n    \'union(tables: [t0, t1])|> group()\' + \"\\n\" +\n    \'|> map (fn: (r) => ({ r with _time: if not(exists r._value) then r._start else r._time}))\' + \"\\n\" +\n    \'|> sort(columns:[\"deviceId\",\"_start\"])\' + \"\\n\" +\n    \'|> events.duration(unit: 1s,stop:nowOrEnd)\' + \"\\n\" +\n    \'|> map (fn: (r) => ({ r with\' + \"\\n\" +\n    \'       startTime: uint(v: r._start),\' + \"\\n\" +\n    \'       stopTime: uint(v: r._stop),\' + \"\\n\" +\n    \'       timeTime: uint(v: r._time),\' + \"\\n\" +\n    \'       time_to_stop: (int(v: r._stop) - int(v: r._time))/1000/1000/1000 ,\' + \"\\n\" +\n    \'       start_to_time: (int(v: r._time) - int(v: r._start))/1000/1000/1000,\' + \"\\n\" +\n    \'       duration: if r.duration<0 then  (int(v: nowOrEnd) - int(v: r._time))/1000/1000/1000 else r.duration\' + \"\\n\" +\n    \'        }))\' + \"\\n\" +\n    \'|> fill(usePrevious: true)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_start\"] > time(v: 0) )\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\", \"_value\",\"_start\",\"_stop\",\"duration\",\"start_to_time\",\"startTime\",\"stopTime\",\"time_to_stop\",\"timeTime\",\"deviceId\"])\' + \"\\n\" +\n    \'|> group(columns:[\"_start\",\"deviceId\"])\' + \"\\n\" +\n    \'|> reduce( fn: (r, accumulator) => ( {\' + \"\\n\" +\n    \'    start:r._start,\' + \"\\n\" +\n    \'    preItem: r._value,\' + \"\\n\" +\n    \'    deviceId: r.deviceId,\' + \"\\n\" +\n    \'    spend:\' + \"\\n\" +\n    \'        if r._value ==false and accumulator.preItem==false and r.start_to_time!=0 then accumulator.spend + r.start_to_time else\' + \"\\n\" +\n    \'        if r._value ==false and accumulator.preItem==true and r.start_to_time!=0 then accumulator.spend else\' + \"\\n\" +\n    \'        if r._value ==true  and r._time==r._start and r.start_to_time==0 then\' + \"\\n\" +\n    \'               if  r.duration>r.time_to_stop  then accumulator.spend+r.time_to_stop else accumulator.spend+r.duration\' + \"\\n\" +\n    \'        else\' + \"\\n\" +\n    \'        if r._value ==true  and accumulator.preItem==false and r.start_to_time!=0  then\' + \"\\n\" +\n    \'          if  r.duration>r.time_to_stop  then accumulator.spend+r.time_to_stop else accumulator.spend+r.duration\' + \"\\n\" +\n    \'        else\' + \"\\n\" +\n    \'          accumulator.spend\' + \"\\n\" +\n    \'} ), identity: {preItem:false,spend: 0,start:time(v: 2025-06-03T14:53:39+08:00),deviceId:\"\"})\' + \"\\n\" +\n    \'|> group(columns:[\"_start\"])\' + \"\\n\" +\n    \'|> mean(column:\"spend\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({r with _time: r._start,onlineSumSeconds:int(v:r.spend),onlineSumStr:string(v:duration(v:int(v:r.spend)*1000*1000*1000))}))\' + \"\\n\" +\n	\'|> group()\';\n\n\n\nvar r = me.queryData(q);\nreturn r;', 3, 0, 1540174679756439554, '苏尚群', '2024-08-16 15:06:10', 1443471710403244034, '苏尚群', '2025-06-12 14:40:18', 0, 0, 0, 0, 999);
INSERT INTO `thing_service` (`ID`, `thing_model_id`, `service_name`, `service_type`, `descript`, `override`, `input_data`, `output_data`, `service_code`, `VERSION`, `DELETED`, `CREATOR_ID`, `CREATOR`, `CREATE_TIME`, `UPDATOR_ID`, `UPDATOR`, `UPDATE_TIME`, `ENGINEERING_ID`, `MODULE_ID`, `SPACE_ID`, `async`, `tenant_id`) VALUES (1000000000079946, 999, 'queryTaskTimeByWindow', 0, '查询分组累计任务时长', 0, '[{\"jSON\": true, \"name\": \"end\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"begin\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}]', '{\"jSON\": true, \"isArray\": null, \"dataType\": 31, \"descript\": \"\", \"dataModelId\": null, \"outputDataDescript\": \"\"}', 'var q =\n    \'import \"date\"\' + \"\\n\" +\n    \'queryBegin = time(v: \' + input.begin + \'+08:00)\' + \"\\n\" +\n    \'queryEnd = time(v: \' + input.end + \'+08:00)\' + \"\\n\" +\n    \'nowTime = now()\' + \"\\n\" +\n    \'nowOrEnd = if nowTime < queryEnd then nowTime else queryEnd\' + \"\\n\" +\n    \'nowOrBegin = if nowTime < queryBegin then nowTime else queryBegin\' + \"\\n\" +\n\n    \'t0 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: 0, stop: queryBegin)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and r[\"deviceId\"] == \"\' + me.id + \'\")\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Task\" )\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrBegin  })) \' + \"\\n\" +\n\n    \'t1 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and r[\"deviceId\"] == \"\' + me.id + \'\")\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Task\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n\n    \'t2 = \' + \"\\n\" +\n    \'from(bucket: \"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and r[\"deviceId\"] == \"\' + me.id + \'\")\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Task\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrEnd  })) \' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _value: if r[\"_value\"] then false else true })) \' + \"\\n\" +\n\n    \'t3 =\' + \"\\n\" +\n    \'union(tables: [t0, t1, t2])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n\n    \'t4 = t3\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> window(every: \' + input.every + \', offset: -8h)\' + \"\\n\" +\n    \'|> last()\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: date.sub(d: 1s, from: r[\"_stop\"])  }))\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n\n    \'t5 = \' + \"\\n\" +\n    \'union(tables: [t3, t4])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n\n    \'t5\' + \"\\n\" +\n\n    \'|> map(fn: (r) => ({ r with val: int(v: r[\"_value\"])  })) \' + \"\\n\" +\n    \'|> difference(columns: [\"val\"], keepFirst: true)\' + \"\\n\" +\n    \'|> elapsed(unit: 1s, columnName: \"dur\", timeColumn: \"_time\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with type: \' + \"\\n\" +\n    \'        if r.property == \"State_Task\" and r.val == 1 then \"noTask\"\' + \"\\n\" +\n    \'        else if r.property == \"State_Task\" and r.val == -1 then \"task\"\' + \"\\n\" +\n    \'        else if r.property == \"State_Task\" and r.val == 0 then \' + \"\\n\" +\n    \'            if r._value then \"task\" else \"noTask\"\' + \"\\n\" +\n    \'        else \"unknow\"\' + \"\\n\" +\n    \'    })\' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\", \"dur\", \"type\"])\' + \"\\n\" +\n    \'|> filter(fn: (r) => \' + \"\\n\" +\n    \'    r[\"type\"] == \"task\" \' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> aggregateWindow(every: \' + input.every + \', fn: sum, offset: -8h, createEmpty: false, column: \"dur\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with taskSum: r[\"dur\"]}))\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with taskSumSeconds: r[\"dur\"]} ))\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with taskSumStr: string(v: duration( v: ( r[\"taskSumSeconds\"]*1000*1000*1000 ) )) }))\' + \"\\n\" +\n    \'|> drop(columns: [\"_start\",\"_stop\", \"dur\"])\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: date.sub(d: 1s, from: r[\"_time\"])  }))\';\n\nvar r = me.queryData(q);\nreturn r;', 1, 0, 1540174679756439554, '苏尚群', '2024-08-16 15:06:10', 1540174679756439554, '苏尚群', '2024-08-16 15:06:10', 0, 0, 0, 0, 999);
INSERT INTO `thing_service` (`ID`, `thing_model_id`, `service_name`, `service_type`, `descript`, `override`, `input_data`, `output_data`, `service_code`, `VERSION`, `DELETED`, `CREATOR_ID`, `CREATOR`, `CREATE_TIME`, `UPDATOR_ID`, `UPDATOR`, `UPDATE_TIME`, `ENGINEERING_ID`, `MODULE_ID`, `SPACE_ID`, `async`, `tenant_id`) VALUES (1000000000079947, 999, 'queryDevicesTaskTimeByWindow', 0, '查询多设备分组累计任务时长', 0, '[{\"jSON\": true, \"name\": \"every\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"deviceIds\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"end\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"begin\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}]', '{\"jSON\": true, \"isArray\": null, \"dataType\": 31, \"descript\": \"\", \"dataModelId\": null, \"outputDataDescript\": \"\"}', 'var ids = input.deviceIds;\nvar idArray = ids.split(\',\');\n\n// var tmpArray = [];\n// for (var i = 0; i < idArray.length; i++) {\n//     tmpArray.push(\'r[\"deviceId\"] == \"\' + idArray[i] + \'\"\');\n// }\n// var idsStr = tmpArray.join(\' or \');\n\nvar idsStr = \'r[\"deviceId\"] =~ /\' + idArray.join(\'|\') + \'/\';\n\nvar q =\n    \'// queryDevicesTaskTimeByWindow\' + \"\\n\" +\n    \'import \"date\"\' + \"\\n\" +\n    \'queryBegin = time(v: \' + input.begin + \'+08:00)\' + \"\\n\" +\n    \'queryEnd = time(v: \' + input.end + \'+08:00)\' + \"\\n\" +\n    \'nowTime = now()\' + \"\\n\" +\n    \'nowOrEnd = if nowTime < queryEnd then nowTime else queryEnd\' + \"\\n\" +\n    \'nowOrBegin = if nowTime < queryBegin then nowTime else queryBegin\' + \"\\n\" +\n\n    \'t0 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: 0, stop: queryBegin)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and ( \' + idsStr + \' ) )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Task\" )\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrBegin  })) \' + \"\\n\" +\n\n    \'t1 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and ( \' + idsStr + \' ) )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Task\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n\n    \'t2 = \' + \"\\n\" +\n    \'from(bucket: \"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and ( \' + idsStr + \' ) )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Task\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrEnd  })) \' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _value: if r[\"_value\"] then false else true })) \' + \"\\n\" +\n\n    \'t3 =\' + \"\\n\" +\n    \'union(tables: [t0, t1, t2])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n\n    \'t4 = t3\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> window(every: \' + input.every + \', offset: -8h)\' + \"\\n\" +\n    \'|> last()\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: date.sub(d: 1s, from: r[\"_stop\"])  }))\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n\n    \'t5 = \' + \"\\n\" +\n    \'union(tables: [t3, t4])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n\n    \'t5\' + \"\\n\" +\n\n    \'|> map(fn: (r) => ({ r with val: int(v: r[\"_value\"])  })) \' + \"\\n\" +\n    \'|> difference(columns: [\"val\"], keepFirst: true)\' + \"\\n\" +\n    \'|> elapsed(unit: 1s, columnName: \"dur\", timeColumn: \"_time\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with type: \' + \"\\n\" +\n    \'        if r.property == \"State_Task\" and r.val == 1 then \"noTask\"\' + \"\\n\" +\n    \'        else if r.property == \"State_Task\" and r.val == -1 then \"task\"\' + \"\\n\" +\n    \'        else if r.property == \"State_Task\" and r.val == 0 then \' + \"\\n\" +\n    \'            if r._value then \"task\" else \"noTask\"\' + \"\\n\" +\n    \'        else \"unknow\"\' + \"\\n\" +\n    \'    })\' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"dur\", \"type\"])\' + \"\\n\" +\n    \'|> filter(fn: (r) => \' + \"\\n\" +\n    \'    r[\"type\"] == \"task\" \' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> aggregateWindow(every: \' + input.every + \', fn: sum, createEmpty: false, offset: -8h, column: \"dur\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with taskSum: r[\"dur\"]/\' + idArray.length + \'}))\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with taskSumSeconds: r[\"dur\"]/\' + idArray.length + \'}))\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with taskSumStr: string(v: duration( v: ( r[\"taskSumSeconds\"]*1000*1000*1000) )) }))\' + \"\\n\" +\n    \'|> drop(columns: [\"_start\",\"_stop\", \"dur\"])\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: date.sub(d: 1s, from: r[\"_time\"])  }))\';\n\nvar r = me.queryData(q);\nreturn r;', 1, 0, 1540174679756439554, '苏尚群', '2024-08-16 15:06:10', 1540174679756439554, '苏尚群', '2024-08-16 15:06:10', 0, 0, 0, 0, 999);
INSERT INTO `thing_service` (`ID`, `thing_model_id`, `service_name`, `service_type`, `descript`, `override`, `input_data`, `output_data`, `service_code`, `VERSION`, `DELETED`, `CREATOR_ID`, `CREATOR`, `CREATE_TIME`, `UPDATOR_ID`, `UPDATOR`, `UPDATE_TIME`, `ENGINEERING_ID`, `MODULE_ID`, `SPACE_ID`, `async`, `tenant_id`) VALUES (1000000000079948, 999, 'queryStandByTimeByWindow', 0, '查询分组累计空闲时长', 0, '[{\"jSON\": true, \"name\": \"every\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"end\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"begin\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}]', '{\"jSON\": true, \"isArray\": null, \"dataType\": 31, \"descript\": \"\", \"dataModelId\": null, \"outputDataDescript\": \"\"}', 'var q =\n    \'import \"date\"\' + \"\\n\" +\n    \'queryBegin = time(v: \' + input.begin + \'+08:00)\' + \"\\n\" +\n    \'queryEnd = time(v: \' + input.end + \'+08:00)\' + \"\\n\" +\n    \'nowTime = now()\' + \"\\n\" +\n    \'nowOrEnd = if nowTime < queryEnd then nowTime else queryEnd\' + \"\\n\" +\n    \'nowOrBegin = if nowTime < queryBegin then nowTime else queryBegin\' + \"\\n\" +\n\n    \'t0 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: 0, stop: queryBegin)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and r[\"deviceId\"] == \"\' + me.id + \'\")\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_StandBy\" )\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrBegin  })) \' + \"\\n\" +\n\n    \'t1 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and r[\"deviceId\"] == \"\' + me.id + \'\")\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_StandBy\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n\n    \'t2 = \' + \"\\n\" +\n    \'from(bucket: \"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and r[\"deviceId\"] == \"\' + me.id + \'\")\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_StandBy\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrEnd  })) \' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _value: if r[\"_value\"] then false else true })) \' + \"\\n\" +\n\n    \'t3 =\' + \"\\n\" +\n    \'union(tables: [t0, t1, t2])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n\n    \'t4 = t3\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> window(every: \' + input.every + \', offset: -8h)\' + \"\\n\" +\n    \'|> last()\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: date.sub(d: 1s, from: r[\"_stop\"])  }))\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n\n    \'t5 = \' + \"\\n\" +\n    \'union(tables: [t3, t4])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n\n    \'t5\' + \"\\n\" +\n\n    \'|> map(fn: (r) => ({ r with val: int(v: r[\"_value\"])  })) \' + \"\\n\" +\n    \'|> difference(columns: [\"val\"], keepFirst: true)\' + \"\\n\" +\n    \'|> elapsed(unit: 1s, columnName: \"dur\", timeColumn: \"_time\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with type: \' + \"\\n\" +\n    \'        if r.property == \"State_StandBy\" and r.val == 1 then \"noStandBy\"\' + \"\\n\" +\n    \'        else if r.property == \"State_StandBy\" and r.val == -1 then \"standBy\"\' + \"\\n\" +\n    \'        else if r.property == \"State_StandBy\" and r.val == 0 then \' + \"\\n\" +\n    \'            if r._value then \"standBy\" else \"noStandBy\"\' + \"\\n\" +\n    \'        else \"unknow\"\' + \"\\n\" +\n    \'    })\' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\", \"dur\", \"type\"])\' + \"\\n\" +\n    \'|> filter(fn: (r) => \' + \"\\n\" +\n    \'    r[\"type\"] == \"standBy\" \' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> aggregateWindow(every: \' + input.every + \', fn: sum, createEmpty: false, offset: -8h, column: \"dur\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with standBySum: r[\"dur\"]}))\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with standBySumSeconds: r[\"dur\"]}))\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with standBySumStr: string(v: duration( v: ( r[\"standBySumSeconds\"]*1000*1000*1000 ) )) }))\' + \"\\n\" +\n    \'|> drop(columns: [\"_start\",\"_stop\", \"dur\"])\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: date.sub(d: 1s, from: r[\"_time\"])  }))\';\n\nvar r = me.queryData(q);\nreturn r;', 1, 0, 1540174679756439554, '苏尚群', '2024-08-16 15:06:10', 1540174679756439554, '苏尚群', '2024-08-16 15:06:10', 0, 0, 0, 0, 999);
INSERT INTO `thing_service` (`ID`, `thing_model_id`, `service_name`, `service_type`, `descript`, `override`, `input_data`, `output_data`, `service_code`, `VERSION`, `DELETED`, `CREATOR_ID`, `CREATOR`, `CREATE_TIME`, `UPDATOR_ID`, `UPDATOR`, `UPDATE_TIME`, `ENGINEERING_ID`, `MODULE_ID`, `SPACE_ID`, `async`, `tenant_id`) VALUES (1000000000079949, 999, 'queryDevicesStandByTimeByWindow', 0, '查询多设备分组累计空闲时长', 0, '[{\"jSON\": true, \"name\": \"deviceIds\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"every\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"end\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"begin\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}]', '{\"jSON\": true, \"isArray\": null, \"dataType\": 31, \"descript\": \"\", \"dataModelId\": null, \"outputDataDescript\": \"\"}', 'var ids = input.deviceIds;\nvar idArray = ids.split(\',\');\n\n// var tmpArray = [];\n// for (var i = 0; i < idArray.length; i++) {\n//     tmpArray.push(\'r[\"deviceId\"] == \"\' + idArray[i] + \'\"\');\n// }\n// var idsStr = tmpArray.join(\' or \');\n\nvar idsStr = \'r[\"deviceId\"] =~ /\' + idArray.join(\'|\') + \'/\';\n\nvar q =\n    \'import \"date\"\' + \"\\n\" +\n    \'queryBegin = time(v: \' + input.begin + \'+08:00)\' + \"\\n\" +\n    \'queryEnd = time(v: \' + input.end + \'+08:00)\' + \"\\n\" +\n    \'nowTime = now()\' + \"\\n\" +\n    \'nowOrEnd = if nowTime < queryEnd then nowTime else queryEnd\' + \"\\n\" +\n    \'nowOrBegin = if nowTime < queryBegin then nowTime else queryBegin\' + \"\\n\" +\n\n    \'t0 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: 0, stop: queryBegin)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and ( \' + idsStr + \' ) )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_StandBy\" )\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrBegin  })) \' + \"\\n\" +\n\n    \'t1 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and ( \' + idsStr + \' ) )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_StandBy\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n\n    \'t2 = \' + \"\\n\" +\n    \'from(bucket: \"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and ( \' + idsStr + \' ) )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_StandBy\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrEnd  })) \' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _value: if r[\"_value\"] then false else true })) \' + \"\\n\" +\n\n    \'t3 =\' + \"\\n\" +\n    \'union(tables: [t0, t1, t2])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n\n    \'t4 = t3\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> window(every: \' + input.every + \', offset: -8h)\' + \"\\n\" +\n    \'|> last()\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: date.sub(d: 1s, from: r[\"_stop\"])  }))\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n\n    \'t5 = \' + \"\\n\" +\n    \'union(tables: [t3, t4])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n\n    \'t5\' + \"\\n\" +\n\n    \'|> map(fn: (r) => ({ r with val: int(v: r[\"_value\"])  })) \' + \"\\n\" +\n    \'|> difference(columns: [\"val\"], keepFirst: true)\' + \"\\n\" +\n    \'|> elapsed(unit: 1s, columnName: \"dur\", timeColumn: \"_time\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with type: \' + \"\\n\" +\n    \'        if r.property == \"State_StandBy\" and r.val == 1 then \"noStandBy\"\' + \"\\n\" +\n    \'        else if r.property == \"State_StandBy\" and r.val == -1 then \"standBy\"\' + \"\\n\" +\n    \'        else if r.property == \"State_StandBy\" and r.val == 0 then \' + \"\\n\" +\n    \'            if r._value then \"standBy\" else \"noStandBy\"\' + \"\\n\" +\n    \'        else \"unknow\"\' + \"\\n\" +\n    \'    })\' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"dur\", \"type\"])\' + \"\\n\" +\n    \'|> filter(fn: (r) => \' + \"\\n\" +\n    \'    r[\"type\"] == \"standBy\" \' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> aggregateWindow(every: \' + input.every + \', fn: sum, createEmpty: false, offset: -8h, column: \"dur\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with standBySum: r[\"dur\"]/\' + idArray.length + \'}))\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with standBySumSeconds: r[\"dur\"]/\' + idArray.length + \'}))\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with standBySumStr: string(v: duration( v: ( r[\"standBySumSeconds\"]*1000*1000*1000 ) )) }))\' + \"\\n\" +\n    \'|> drop(columns: [\"_start\",\"_stop\", \"dur\"])\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: date.sub(d: 1s, from: r[\"_time\"])  }))\';\n\n\nvar r = me.queryData(q);\nreturn r;', 1, 0, 1540174679756439554, '苏尚群', '2024-08-16 15:06:10', 1540174679756439554, '苏尚群', '2024-08-16 15:06:10', 0, 0, 0, 0, 999);
INSERT INTO `thing_service` (`ID`, `thing_model_id`, `service_name`, `service_type`, `descript`, `override`, `input_data`, `output_data`, `service_code`, `VERSION`, `DELETED`, `CREATOR_ID`, `CREATOR`, `CREATE_TIME`, `UPDATOR_ID`, `UPDATOR`, `UPDATE_TIME`, `ENGINEERING_ID`, `MODULE_ID`, `SPACE_ID`, `async`, `tenant_id`) VALUES (1000000000079950, 999, 'queryFaultTimeByWindow', 0, '查询分组累计故障时长', 0, '[{\"jSON\": true, \"name\": \"every\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"end\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"begin\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}]', '{\"jSON\": true, \"isArray\": null, \"dataType\": 31, \"descript\": \"\", \"dataModelId\": null, \"outputDataDescript\": \"\"}', 'var q =\n    \'import \"date\"\' + \"\\n\" +\n    \'queryBegin = time(v: \' + input.begin + \'+08:00)\' + \"\\n\" +\n    \'queryEnd = time(v: \' + input.end + \'+08:00)\' + \"\\n\" +\n    \'nowTime = now()\' + \"\\n\" +\n    \'nowOrEnd = if nowTime < queryEnd then nowTime else queryEnd\' + \"\\n\" +\n    \'nowOrBegin = if nowTime < queryBegin then nowTime else queryBegin\' + \"\\n\" +\n\n    \'t0 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: 0, stop: queryBegin)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and r[\"deviceId\"] == \"\' + me.id + \'\")\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Task\" )\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrBegin  })) \' + \"\\n\" +\n\n    \'t1 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and r[\"deviceId\"] == \"\' + me.id + \'\")\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Fault\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n\n    \'t2 = \' + \"\\n\" +\n    \'from(bucket: \"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and r[\"deviceId\"] == \"\' + me.id + \'\")\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Fault\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrEnd  })) \' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _value: if r[\"_value\"] then false else true })) \' + \"\\n\" +\n\n    \'t3 =\' + \"\\n\" +\n    \'union(tables: [t0, t1, t2])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n\n    \'t4 = t3\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> window(every: \' + input.every + \', offset: -8h)\' + \"\\n\" +\n    \'|> last()\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: date.sub(d: 1s, from: r[\"_stop\"])  }))\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n\n    \'t5 = \' + \"\\n\" +\n    \'union(tables: [t3, t4])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n\n    \'t5\' + \"\\n\" +\n\n    \'|> map(fn: (r) => ({ r with val: int(v: r[\"_value\"])  })) \' + \"\\n\" +\n    \'|> difference(columns: [\"val\"], keepFirst: true)\' + \"\\n\" +\n    \'|> elapsed(unit: 1s, columnName: \"dur\", timeColumn: \"_time\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with type: \' + \"\\n\" +\n    \'        if r.property == \"State_Fault\" and r.val == 1 then \"noFault\"\' + \"\\n\" +\n    \'        else if r.property == \"State_Fault\" and r.val == -1 then \"fault\"\' + \"\\n\" +\n    \'        else if r.property == \"State_Fault\" and r.val == 0 then \' + \"\\n\" +\n    \'            if r._value then \"fault\" else \"noFault\"\' + \"\\n\" +\n    \'        else \"unknow\"\' + \"\\n\" +\n    \'    })\' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\", \"dur\", \"type\"])\' + \"\\n\" +\n    \'|> filter(fn: (r) => \' + \"\\n\" +\n    \'    r[\"type\"] == \"fault\" \' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> aggregateWindow(every: \' + input.every + \', fn: sum, createEmpty: false, offset: -8h, column: \"dur\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with faultSum: r[\"dur\"]}))\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with faultSumSeconds: r[\"dur\"]}))\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with faultSumStr: string(v: duration( v: ( r[\"faultSumSeconds\"]*1000*1000*1000 ) )) }))\' + \"\\n\" +\n    \'|> drop(columns: [\"_start\",\"_stop\", \"dur\"])\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: date.sub(d: 1s, from: r[\"_time\"])  }))\';\nvar r = me.queryData(q);\nreturn r;', 1, 0, 1540174679756439554, '苏尚群', '2024-08-16 15:06:10', 1540174679756439554, '苏尚群', '2024-08-16 15:06:10', 0, 0, 0, 0, 999);
INSERT INTO `thing_service` (`ID`, `thing_model_id`, `service_name`, `service_type`, `descript`, `override`, `input_data`, `output_data`, `service_code`, `VERSION`, `DELETED`, `CREATOR_ID`, `CREATOR`, `CREATE_TIME`, `UPDATOR_ID`, `UPDATOR`, `UPDATE_TIME`, `ENGINEERING_ID`, `MODULE_ID`, `SPACE_ID`, `async`, `tenant_id`) VALUES (1000000000079951, 999, 'queryDevicesFaultTimeByWindow', 0, '查询多设备分组累计故障时长', 0, '[{\"jSON\": true, \"name\": \"deviceIds\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"every\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"end\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"begin\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}]', '{\"jSON\": true, \"isArray\": null, \"dataType\": 31, \"descript\": \"\", \"dataModelId\": null, \"outputDataDescript\": \"\"}', 'var ids = input.deviceIds;\nvar idArray = ids.split(\',\');\n\n// var tmpArray = [];\n// for (var i = 0; i < idArray.length; i++) {\n//     tmpArray.push(\'r[\"deviceId\"] == \"\' + idArray[i] + \'\"\');\n// }\n// var idsStr = tmpArray.join(\' or \');\n\nvar idsStr = \'r[\"deviceId\"] =~ /\' + idArray.join(\'|\') + \'/\';\n\nvar q =\n    \'import \"date\"\' + \"\\n\" +\n    \'queryBegin = time(v: \' + input.begin + \'+08:00)\' + \"\\n\" +\n    \'queryEnd = time(v: \' + input.end + \'+08:00)\' + \"\\n\" +\n    \'nowTime = now()\' + \"\\n\" +\n    \'nowOrEnd = if nowTime < queryEnd then nowTime else queryEnd\' + \"\\n\" +\n    \'nowOrBegin = if nowTime < queryBegin then nowTime else queryBegin\' + \"\\n\" +\n\n    \'t0 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: 0, stop: queryBegin)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and ( \' + idsStr + \' ) )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Fault\" )\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrBegin  })) \' + \"\\n\" +\n\n    \'t1 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and ( \' + idsStr + \' ) )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Fault\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n\n    \'t2 = \' + \"\\n\" +\n    \'from(bucket: \"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and ( \' + idsStr + \' ) )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Fault\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrEnd  })) \' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _value: if r[\"_value\"] then false else true })) \' + \"\\n\" +\n\n    \'t3 =\' + \"\\n\" +\n    \'union(tables: [t0, t1, t2])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n\n    \'t4 = t3\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> window(every: \' + input.every + \', offset: -8h)\' + \"\\n\" +\n    \'|> last()\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: date.sub(d: 1s, from: r[\"_stop\"])  }))\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n\n    \'t5 = \' + \"\\n\" +\n    \'union(tables: [t3, t4])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n\n    \'t5\' + \"\\n\" +\n\n    \'|> map(fn: (r) => ({ r with val: int(v: r[\"_value\"])  })) \' + \"\\n\" +\n    \'|> difference(columns: [\"val\"], keepFirst: true)\' + \"\\n\" +\n    \'|> elapsed(unit: 1s, columnName: \"dur\", timeColumn: \"_time\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with type: \' + \"\\n\" +\n    \'        if r.property == \"State_Fault\" and r.val == 1 then \"noFault\"\' + \"\\n\" +\n    \'        else if r.property == \"State_Fault\" and r.val == -1 then \"fault\"\' + \"\\n\" +\n    \'        else if r.property == \"State_Fault\" and r.val == 0 then \' + \"\\n\" +\n    \'            if r._value then \"fault\" else \"noFault\"\' + \"\\n\" +\n    \'        else \"unknow\"\' + \"\\n\" +\n    \'    })\' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"dur\", \"type\"])\' + \"\\n\" +\n    \'|> filter(fn: (r) => \' + \"\\n\" +\n    \'    r[\"type\"] == \"fault\" \' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> aggregateWindow(every: \' + input.every + \', fn: sum, createEmpty: false, offset: -8h, column: \"dur\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with faultSum: r[\"dur\"]/\' + idArray.length + \'}))\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with faultSumSeconds: r[\"dur\"]/\' + idArray.length + \'}))\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with faultSumStr: string(v: duration( v: ( r[\"faultSumSeconds\"]*1000*1000*1000 ) )) }))\' + \"\\n\" +\n    \'|> drop(columns: [\"_start\",\"_stop\", \"dur\"])\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: date.sub(d: 1s, from: r[\"_time\"])  }))\';\n\nvar r = me.queryData(q);\nreturn r;', 1, 0, 1540174679756439554, '苏尚群', '2024-08-16 15:06:10', 1540174679756439554, '苏尚群', '2024-08-16 15:06:10', 0, 0, 0, 0, 999);
INSERT INTO `thing_service` (`ID`, `thing_model_id`, `service_name`, `service_type`, `descript`, `override`, `input_data`, `output_data`, `service_code`, `VERSION`, `DELETED`, `CREATOR_ID`, `CREATOR`, `CREATE_TIME`, `UPDATOR_ID`, `UPDATOR`, `UPDATE_TIME`, `ENGINEERING_ID`, `MODULE_ID`, `SPACE_ID`, `async`, `tenant_id`) VALUES (1000000000088128, 999, 'queryOnLineTimeSum', 0, '查询累计在线时长', 0, '[{\"jSON\": true, \"name\": \"end\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"begin\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}]', '{\"jSON\": true, \"isArray\": null, \"dataType\": 31, \"descript\": \"\", \"dataModelId\": null, \"outputDataDescript\": \"\"}', 'var q =\n    \'import \"contrib/tomhollingworth/events\"\' + \"\\n\" +\n    \'queryBegin = time(v: \' + input.begin + \'+08:00)\' + \"\\n\" +\n    \'queryEnd = time(v: \' + input.end + \'+08:00)\' + \"\\n\" +\n    \'nowTime = now()\' + \"\\n\" +\n    \'nowOrEnd = if nowTime < queryEnd then nowTime else queryEnd\' + \"\\n\" +\n    \'nowOrBegin = if nowTime < queryBegin then nowTime else queryBegin\' + \"\\n\" +\n    \'t0 =\' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: 0, stop: queryBegin)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and r[\"deviceId\"] == \"\' + me.id + \'\")\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_OnLine\")\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: queryBegin  }))\' + \"\\n\" +\n    \'t1 =\' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: nowOrEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and r[\"deviceId\"] == \"\' + me.id + \'\")\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_OnLine\")\' + \"\\n\" +\n\n\n    \'t=union(tables: [t0, t1])\' + \"\\n\" +\n    \'|> group()\' + \"\\n\" +\n    \'|> events.duration(unit: 1s,stop:nowOrEnd)\' + \"\\n\" +\n\n    \'onlineTimeFunc=(tables=<-) => tables |> reduce( fn: (r, accumulator) => ( { online:  if r._value ==true  then accumulator.online + r.duration else accumulator.online } ), identity: {online: 0}, )\' + \"\\n\" +\n\n    \'onlineTimeFunc(tables: t)\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with durStr: string(v: duration( v: ( r[\"online\"]*1000*1000*1000 ) )) }))\' + \"\\n\" +\n    \'|> keep(columns: [\"durStr\",\"deviceId\",\"online\"])\'\n\n\n\nvar r = me.queryData(q);\nreturn r;', 5, 0, 1540174679756439554, '苏尚群', '2024-08-16 15:06:10', 1443471710403244034, '苏尚群', '2025-06-10 14:10:06', 0, 0, 0, 0, 999);
INSERT INTO `thing_service` (`ID`, `thing_model_id`, `service_name`, `service_type`, `descript`, `override`, `input_data`, `output_data`, `service_code`, `VERSION`, `DELETED`, `CREATOR_ID`, `CREATOR`, `CREATE_TIME`, `UPDATOR_ID`, `UPDATOR`, `UPDATE_TIME`, `ENGINEERING_ID`, `MODULE_ID`, `SPACE_ID`, `async`, `tenant_id`) VALUES (1000000000088129, 999, 'queryDevicesStandByRate', 0, '查询多设备空闲率', 0, '[{\"jSON\": true, \"name\": \"deviceIds\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"end\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"begin\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}]', '{\"jSON\": true, \"isArray\": null, \"dataType\": 31, \"descript\": \"\", \"dataModelId\": null, \"outputDataDescript\": \"\"}', 'var ids = input.deviceIds;\nvar idArray = ids.split(\',\');\n\n// var tmpArray = [];\n// for (var i = 0; i < idArray.length; i++) {\n//     tmpArray.push(\'r[\"deviceId\"] == \"\' + idArray[i] + \'\"\');\n// }\n// var idsStr = tmpArray.join(\' or \');\n\nvar idsStr = \'r[\"deviceId\"] =~ /\' + idArray.join(\'|\') + \'/\';\n\nvar q =\n    \'queryBegin = time(v: \' + input.begin + \'+08:00)\' + \"\\n\" +\n    \'queryEnd = time(v: \' + input.end + \'+08:00)\' + \"\\n\" +\n    \'nowTime = now()\' + \"\\n\" +\n    \'nowOrEnd = if nowTime < queryEnd then nowTime else queryEnd\' + \"\\n\" +\n    \'nowOrBegin = if nowTime < queryBegin then nowTime else queryBegin\' + \"\\n\" +\n\n    \'t0 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: 0, stop: queryBegin)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and ( \' + idsStr + \' ) )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_StandBy\" or r[\"property\"] == \"State_OnLine\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrBegin  })) \' + \"\\n\" +\n\n    \'t1 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and ( \' + idsStr + \' ) )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_StandBy\" or r[\"property\"] == \"State_OnLine\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n\n    \'t2 = \' + \"\\n\" +\n    \'from(bucket: \"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and ( \' + idsStr + \' ) )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_StandBy\" or r[\"property\"] == \"State_OnLine\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrEnd  })) \' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _value: if r[\"_value\"] then false else true })) \' + \"\\n\" +\n\n    \'union(tables: [t0, t1, t2])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with val: int(v: r[\"_value\"])  })) \' + \"\\n\" +\n    \'|> difference(columns: [\"val\"], keepFirst: true)\' + \"\\n\" +\n    \'|> elapsed(unit: 1s, columnName: \"dur\", timeColumn: \"_time\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with type: \' + \"\\n\" +\n    \'        if r.property == \"State_OnLine\" and r.val == 1 then \"offline\" \' + \"\\n\" +\n    \'        else if r.property == \"State_OnLine\" and r.val == -1 then \"online\"\' + \"\\n\" +\n    \'        else if r.property == \"State_OnLine\" and r.val == 0 then \' + \"\\n\" +\n    \'            if r._value then \"online\" else \"offline\"\' + \"\\n\" +\n    \'        else if r.property == \"State_StandBy\" and r.val == 1 then \"noStandBy\"\' + \"\\n\" +\n    \'        else if r.property == \"State_StandBy\" and r.val == -1 then \"standBy\"\' + \"\\n\" +\n    \'        else if r.property == \"State_StandBy\" and r.val == 0 then \' + \"\\n\" +\n    \'            if r._value then \"standBy\" else \"noStandBy\"\' + \"\\n\" +\n    \'        else \"unknow\"\' + \"\\n\" +\n    \'    })\' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\", \"dur\", \"type\"])\' + \"\\n\" +\n    \'|> filter(fn: (r) => \' + \"\\n\" +\n    \'    r[\"type\"] == \"standBy\" \' + \"\\n\" +\n    \'    or r[\"type\"] == \"online\" \' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> reduce(\' + \"\\n\" +\n    \'    fn: (r, accumulator) => ({\' + \"\\n\" +\n    \'        standBySum: (if r.type == \"standBy\" then r.dur else 0) + accumulator.standBySum, \' + \"\\n\" +\n    \'        onlineSum: (if r.type == \"online\" then r.dur else 0) + accumulator.onlineSum\' + \"\\n\" +\n    \'    }),\' + \"\\n\" +\n    \'    identity: {standBySum: 0, onlineSum: 0},\' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with standByRate: float(v: r[\"standBySum\"])/float(v: r[\"onlineSum\"]) }))\' + \"\\n\";\n\nvar r = me.queryData(q);\nreturn r;', 1, 0, 1540174679756439554, '苏尚群', '2024-08-16 15:06:10', 1540174679756439554, '苏尚群', '2024-08-16 15:06:10', 0, 0, 0, 0, 999);
INSERT INTO `thing_service` (`ID`, `thing_model_id`, `service_name`, `service_type`, `descript`, `override`, `input_data`, `output_data`, `service_code`, `VERSION`, `DELETED`, `CREATOR_ID`, `CREATOR`, `CREATE_TIME`, `UPDATOR_ID`, `UPDATOR`, `UPDATE_TIME`, `ENGINEERING_ID`, `MODULE_ID`, `SPACE_ID`, `async`, `tenant_id`) VALUES (1000000000088130, 999, 'queryDevicesUseRate', 0, '查询多设备利用率', 0, '[{\"jSON\": true, \"name\": \"deviceIds\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"end\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"begin\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}]', '{\"jSON\": true, \"isArray\": null, \"dataType\": 31, \"descript\": \"\", \"dataModelId\": null, \"outputDataDescript\": \"\"}', 'var ids = input.deviceIds;\nvar idArray = ids.split(\',\');\n\n// var tmpArray = [];\n// for (var i = 0; i < idArray.length; i++) {\n//     tmpArray.push(\'r[\"deviceId\"] == \"\' + idArray[i] + \'\"\');\n// }\n// var idsStr = tmpArray.join(\' or \');\n\nvar idsStr = \'r[\"deviceId\"] =~ /\' + idArray.join(\'|\') + \'/\';\n\nvar q =\n    \'queryBegin = time(v: \' + input.begin + \'+08:00)\' + \"\\n\" +\n    \'queryEnd = time(v: \' + input.end + \'+08:00)\' + \"\\n\" +\n    \'nowTime = now()\' + \"\\n\" +\n    \'nowOrEnd = if nowTime < queryEnd then nowTime else queryEnd\' + \"\\n\" +\n    \'nowOrBegin = if nowTime < queryBegin then nowTime else queryBegin\' + \"\\n\" +\n\n    \'t0 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: 0, stop: queryBegin)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and ( \' + idsStr + \' ) )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Task\" or r[\"property\"] == \"State_OnLine\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrBegin  })) \' + \"\\n\" +\n\n    \'t1 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and ( \' + idsStr + \' ) )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Task\" or r[\"property\"] == \"State_OnLine\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n\n    \'t2 = \' + \"\\n\" +\n    \'from(bucket: \"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and ( \' + idsStr + \' ) )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Task\" or r[\"property\"] == \"State_OnLine\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrEnd  })) \' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _value: if r[\"_value\"] then false else true })) \' + \"\\n\" +\n\n    \'union(tables: [t0, t1, t2])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with val: int(v: r[\"_value\"])  })) \' + \"\\n\" +\n    \'|> difference(columns: [\"val\"], keepFirst: true)\' + \"\\n\" +\n    \'|> elapsed(unit: 1s, columnName: \"dur\", timeColumn: \"_time\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with type: \' + \"\\n\" +\n    \'        if r.property == \"State_OnLine\" and r.val == 1 then \"offline\" \' + \"\\n\" +\n    \'        else if r.property == \"State_OnLine\" and r.val == -1 then \"online\"\' + \"\\n\" +\n    \'        else if r.property == \"State_OnLine\" and r.val == 0 then \' + \"\\n\" +\n    \'            if r._value then \"online\" else \"offline\"\' + \"\\n\" +\n    \'        else if r.property == \"State_Task\" and r.val == 1 then \"noTask\"\' + \"\\n\" +\n    \'        else if r.property == \"State_Task\" and r.val == -1 then \"task\"\' + \"\\n\" +\n    \'        else if r.property == \"State_Task\" and r.val == 0 then \' + \"\\n\" +\n    \'            if r._value then \"task\" else \"noTask\"\' + \"\\n\" +\n    \'        else \"unknow\"\' + \"\\n\" +\n    \'    })\' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\", \"dur\", \"type\"])\' + \"\\n\" +\n    \'|> filter(fn: (r) => \' + \"\\n\" +\n    \'    r[\"type\"] == \"task\" \' + \"\\n\" +\n    \'    or r[\"type\"] == \"online\" \' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> reduce(\' + \"\\n\" +\n    \'    fn: (r, accumulator) => ({\' + \"\\n\" +\n    \'        taskSum: (if r.type == \"task\" then r.dur else 0) + accumulator.taskSum, \' + \"\\n\" +\n    \'        onlineSum: (if r.type == \"online\" then r.dur else 0) + accumulator.onlineSum\' + \"\\n\" +\n    \'    }),\' + \"\\n\" +\n    \'    identity: {taskSum: 0, onlineSum: 0},\' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with useRate: if r.onlineSum > 0 then (float(v: r[\"taskSum\"])/float(v: r[\"onlineSum\"]))  else 0.0 }))\' + \"\\n\";\n\nvar r = me.queryData(q);\nreturn r;', 1, 0, 1540174679756439554, '苏尚群', '2024-08-16 15:06:10', 1540174679756439554, '苏尚群', '2024-08-16 15:06:10', 0, 0, 0, 0, 999);
INSERT INTO `thing_service` (`ID`, `thing_model_id`, `service_name`, `service_type`, `descript`, `override`, `input_data`, `output_data`, `service_code`, `VERSION`, `DELETED`, `CREATOR_ID`, `CREATOR`, `CREATE_TIME`, `UPDATOR_ID`, `UPDATOR`, `UPDATE_TIME`, `ENGINEERING_ID`, `MODULE_ID`, `SPACE_ID`, `async`, `tenant_id`) VALUES (1000000000088131, 999, 'queryDevicesFaultRate', 0, '查询多设备故障率', 0, '[{\"jSON\": true, \"name\": \"deviceIds\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"end\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"begin\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}]', '{\"jSON\": true, \"isArray\": null, \"dataType\": 31, \"descript\": \"\", \"dataModelId\": null, \"outputDataDescript\": \"\"}', 'var ids = input.deviceIds;\nvar idArray = ids.split(\',\');\n\n// var tmpArray = [];\n// for (var i = 0; i < idArray.length; i++) {\n//     tmpArray.push(\'r[\"deviceId\"] == \"\' + idArray[i] + \'\"\');\n// }\n// var idsStr = tmpArray.join(\' or \');\n\nvar idsStr = \'r[\"deviceId\"] =~ /\' + idArray.join(\'|\') + \'/\';\n\nvar q =\n    \'queryBegin = time(v: \' + input.begin + \'+08:00)\' + \"\\n\" +\n    \'queryEnd = time(v: \' + input.end + \'+08:00)\' + \"\\n\" +\n    \'nowTime = now()\' + \"\\n\" +\n    \'nowOrEnd = if nowTime < queryEnd then nowTime else queryEnd\' + \"\\n\" +\n    \'nowOrBegin = if nowTime < queryBegin then nowTime else queryBegin\' + \"\\n\" +\n\n    \'t0 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: 0, stop: queryBegin)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and ( \' + idsStr + \' ) )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Fault\" or r[\"property\"] == \"State_OnLine\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrBegin  })) \' + \"\\n\" +\n\n    \'t1 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and ( \' + idsStr + \' ) )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Fault\" or r[\"property\"] == \"State_OnLine\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n\n    \'t2 = \' + \"\\n\" +\n    \'from(bucket: \"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and ( \' + idsStr + \' ) )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Fault\" or r[\"property\"] == \"State_OnLine\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrEnd  })) \' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _value: if r[\"_value\"] then false else true })) \' + \"\\n\" +\n\n    \'union(tables: [t0, t1, t2])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with val: int(v: r[\"_value\"])  })) \' + \"\\n\" +\n    \'|> difference(columns: [\"val\"], keepFirst: true)\' + \"\\n\" +\n    \'|> elapsed(unit: 1s, columnName: \"dur\", timeColumn: \"_time\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with type: \' + \"\\n\" +\n    \'        if r.property == \"State_OnLine\" and r.val == 1 then \"offline\" \' + \"\\n\" +\n    \'        else if r.property == \"State_OnLine\" and r.val == -1 then \"online\"\' + \"\\n\" +\n    \'        else if r.property == \"State_OnLine\" and r.val == 0 then \' + \"\\n\" +\n    \'            if r._value then \"online\" else \"offline\"\' + \"\\n\" +\n    \'        else if r.property == \"State_Fault\" and r.val == 1 then \"noFault\"\' + \"\\n\" +\n    \'        else if r.property == \"State_Fault\" and r.val == -1 then \"fault\"\' + \"\\n\" +\n    \'        else if r.property == \"State_Fault\" and r.val == 0 then \' + \"\\n\" +\n    \'            if r._value then \"fault\" else \"noFault\"\' + \"\\n\" +\n    \'        else \"unknow\"\' + \"\\n\" +\n    \'    })\' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\", \"dur\", \"type\"])\' + \"\\n\" +\n    \'|> filter(fn: (r) => \' + \"\\n\" +\n    \'    r[\"type\"] == \"fault\" \' + \"\\n\" +\n    \'    or r[\"type\"] == \"online\" \' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> reduce(\' + \"\\n\" +\n    \'    fn: (r, accumulator) => ({\' + \"\\n\" +\n    \'        faultSum: (if r.type == \"fault\" then r.dur else 0) + accumulator.faultSum, \' + \"\\n\" +\n    \'        onlineSum: (if r.type == \"online\" then r.dur else 0) + accumulator.onlineSum\' + \"\\n\" +\n    \'    }),\' + \"\\n\" +\n    \'    identity: {faultSum: 0, onlineSum: 0},\' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with faultRate: float(v: r[\"faultSum\"])/float(v: r[\"onlineSum\"]) }))\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with faultRate: if r.faultRate > 1.0 then 1.0 else r.faultRate }))\' + \"\\n\";\n\nvar r = me.queryData(q);\nreturn r;', 1, 0, 1540174679756439554, '苏尚群', '2024-08-16 15:06:10', 1540174679756439554, '苏尚群', '2024-08-16 15:06:10', 0, 0, 0, 0, 999);
INSERT INTO `thing_service` (`ID`, `thing_model_id`, `service_name`, `service_type`, `descript`, `override`, `input_data`, `output_data`, `service_code`, `VERSION`, `DELETED`, `CREATOR_ID`, `CREATOR`, `CREATE_TIME`, `UPDATOR_ID`, `UPDATOR`, `UPDATE_TIME`, `ENGINEERING_ID`, `MODULE_ID`, `SPACE_ID`, `async`, `tenant_id`) VALUES (1000000000088132, 999, 'queryStandByTimeSum', 0, '查询累计空闲时长', 0, '[{\"jSON\": true, \"name\": \"end\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"begin\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}]', '{\"jSON\": true, \"isArray\": null, \"dataType\": 31, \"descript\": \"\", \"dataModelId\": null, \"outputDataDescript\": \"\"}', 'var q =\n    \'queryBegin = time(v: \' + input.begin + \'+08:00)\' + \"\\n\" +\n    \'queryEnd = time(v: \' + input.end + \'+08:00)\' + \"\\n\" +\n    \'nowTime = now()\' + \"\\n\" +\n    \'nowOrEnd = if nowTime < queryEnd then nowTime else queryEnd\' + \"\\n\" +\n    \'nowOrBegin = if nowTime < queryBegin then nowTime else queryBegin\' + \"\\n\" +\n\n    \'t0 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: 0, stop: queryBegin)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and r[\"deviceId\"] == \"\' + me.id + \'\")\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_StandBy\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrBegin  })) \' + \"\\n\" +\n\n    \'t1 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and r[\"deviceId\"] == \"\' + me.id + \'\")\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_StandBy\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n\n    \'t2 = \' + \"\\n\" +\n    \'from(bucket: \"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and r[\"deviceId\"] == \"\' + me.id + \'\")\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_StandBy\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrEnd  })) \' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _value: if r[\"_value\"] then false else true })) \' + \"\\n\" +\n\n    \'union(tables: [t0, t1, t2])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with val: int(v: r[\"_value\"])  })) \' + \"\\n\" +\n    \'|> difference(columns: [\"val\"], keepFirst: true)\' + \"\\n\" +\n    \'|> elapsed(unit: 1s, columnName: \"dur\", timeColumn: \"_time\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with type: \' + \"\\n\" +\n    \'        if r.property == \"State_StandBy\" and r.val == 1 then \"noStandBy\"\' + \"\\n\" +\n    \'        else if r.property == \"State_StandBy\" and r.val == -1 then \"standBy\"\' + \"\\n\" +\n    \'        else if r.property == \"State_StandBy\" and r.val == 0 then \' + \"\\n\" +\n    \'            if r._value then \"standBy\" else \"noStandBy\"\' + \"\\n\" +\n    \'        else \"unknow\"\' + \"\\n\" +\n    \'    })\' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\", \"dur\", \"type\"])\' + \"\\n\" +\n    \'|> filter(fn: (r) => \' + \"\\n\" +\n    \'    r[\"type\"] == \"standBy\" \' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> group(columns: [\"deviceId\",\"type\"])\' + \"\\n\" +\n    \'|> sum(column: \"dur\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with durSeconds: r[\"dur\"]} ))\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with durStr: string(v: duration( v: ( r[\"durSeconds\"]*1000*1000*1000 ) )) }))\' + \"\\n\";\n\nvar r = me.queryData(q);\nreturn r;', 1, 0, 1540174679756439554, '苏尚群', '2024-08-16 15:06:10', 1540174679756439554, '苏尚群', '2024-08-16 15:06:10', 0, 0, 0, 0, 999);
INSERT INTO `thing_service` (`ID`, `thing_model_id`, `service_name`, `service_type`, `descript`, `override`, `input_data`, `output_data`, `service_code`, `VERSION`, `DELETED`, `CREATOR_ID`, `CREATOR`, `CREATE_TIME`, `UPDATOR_ID`, `UPDATOR`, `UPDATE_TIME`, `ENGINEERING_ID`, `MODULE_ID`, `SPACE_ID`, `async`, `tenant_id`) VALUES (1000000000088133, 999, 'queryDevicesOnLineTimeSum', 0, '查询多设备累计在线时长', 0, '[{\"jSON\": true, \"name\": \"deviceIds\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"end\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"begin\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}]', '{\"jSON\": true, \"isArray\": null, \"dataType\": 31, \"descript\": \"\", \"dataModelId\": null, \"outputDataDescript\": \"\"}', 'var ids = input.deviceIds;\nvar idArray = ids.split(\',\');\n\n// var tmpArray = [];\n// for (var i = 0; i < idArray.length; i++) {\n//     tmpArray.push(\'r[\"deviceId\"] == \"\' + idArray[i] + \'\"\');\n// }\n// var idsStr = tmpArray.join(\' or \');\n\nvar idsStr = \'r[\"deviceId\"] =~ /\' + idArray.join(\'|\') + \'/\';\n\nvar q =\n    \'queryBegin = time(v: \' + input.begin + \'+08:00)\' + \"\\n\" +\n    \'queryEnd = time(v: \' + input.end + \'+08:00)\' + \"\\n\" +\n    \'nowTime = now()\' + \"\\n\" +\n    \'nowOrEnd = if nowTime < queryEnd then nowTime else queryEnd\' + \"\\n\" +\n    \'nowOrBegin = if nowTime < queryBegin then nowTime else queryBegin\' + \"\\n\" +\n\n    \'t0 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: 0, stop: queryBegin)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and ( \' + idsStr + \' ) )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_OnLine\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrBegin  })) \' + \"\\n\" +\n\n    \'t1 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and ( \' + idsStr + \' ) )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_OnLine\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n\n    \'t2 = \' + \"\\n\" +\n    \'from(bucket: \"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and ( \' + idsStr + \' ) )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_OnLine\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrEnd  })) \' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _value: if r[\"_value\"] then false else true })) \' + \"\\n\" +\n\n    \'union(tables: [t0, t1, t2])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with val: int(v: r[\"_value\"])  })) \' + \"\\n\" +\n    \'|> difference(columns: [\"val\"], keepFirst: true)\' + \"\\n\" +\n    \'|> elapsed(unit: 1s, columnName: \"dur\", timeColumn: \"_time\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with type: \' + \"\\n\" +\n    \'        if r.property == \"State_OnLine\" and r.val == 1 then \"offline\"\' + \"\\n\" +\n    \'        else if r.property == \"State_OnLine\" and r.val == -1 then \"online\"\' + \"\\n\" +\n    \'        else if r.property == \"State_OnLine\" and r.val == 0 then \' + \"\\n\" +\n    \'            if r._value then \"online\" else \"offline\"\' + \"\\n\" +\n    \'        else \"unknow\"\' + \"\\n\" +\n    \'    })\' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\", \"dur\", \"type\"])\' + \"\\n\" +\n    \'|> filter(fn: (r) => \' + \"\\n\" +\n    \'    r[\"type\"] == \"online\" \' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> group(columns: [\"type\"])\' + \"\\n\" +\n    \'|> sum(column: \"dur\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with durSeconds: r[\"dur\"]} ))\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with durStr: string(v: duration( v: ( r[\"durSeconds\"]*1000*1000*1000 ) )) }))\' + \"\\n\";\n\nvar r = me.queryData(q);\nreturn r;', 1, 0, 1540174679756439554, '苏尚群', '2024-08-16 15:06:10', 1540174679756439554, '苏尚群', '2024-08-16 15:06:10', 0, 0, 0, 0, 999);
INSERT INTO `thing_service` (`ID`, `thing_model_id`, `service_name`, `service_type`, `descript`, `override`, `input_data`, `output_data`, `service_code`, `VERSION`, `DELETED`, `CREATOR_ID`, `CREATOR`, `CREATE_TIME`, `UPDATOR_ID`, `UPDATOR`, `UPDATE_TIME`, `ENGINEERING_ID`, `MODULE_ID`, `SPACE_ID`, `async`, `tenant_id`) VALUES (1000000000088134, 999, 'queryDevicesTaskTimeSum', 0, '查询多设备累计任务时长', 0, '[{\"jSON\": true, \"name\": \"deviceIds\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"end\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"begin\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}]', '{\"jSON\": true, \"isArray\": null, \"dataType\": 31, \"descript\": \"\", \"dataModelId\": null, \"outputDataDescript\": \"\"}', 'var ids = input.deviceIds;\nvar idArray = ids.split(\',\');\n\n// var tmpArray = [];\n// for (var i = 0; i < idArray.length; i++) {\n//     tmpArray.push(\'r[\"deviceId\"] == \"\' + idArray[i] + \'\"\');\n// }\n// var idsStr = tmpArray.join(\' or \');\n\nvar idsStr = \'r[\"deviceId\"] =~ /\' + idArray.join(\'|\') + \'/\';\n\nvar q =\n    \'queryBegin = time(v: \' + input.begin + \'+08:00)\' + \"\\n\" +\n    \'queryEnd = time(v: \' + input.end + \'+08:00)\' + \"\\n\" +\n    \'nowTime = now()\' + \"\\n\" +\n    \'nowOrEnd = if nowTime < queryEnd then nowTime else queryEnd\' + \"\\n\" +\n    \'nowOrBegin = if nowTime < queryBegin then nowTime else queryBegin\' + \"\\n\" +\n\n    \'t0 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: 0, stop: queryBegin)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and ( \' + idsStr + \' ) )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Task\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrBegin  })) \' + \"\\n\" +\n\n    \'t1 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and ( \' + idsStr + \' ) )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Task\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n\n    \'t2 = \' + \"\\n\" +\n    \'from(bucket: \"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and ( \' + idsStr + \' ) )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Task\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrEnd  })) \' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _value: if r[\"_value\"] then false else true })) \' + \"\\n\" +\n\n    \'union(tables: [t0, t1, t2])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with val: int(v: r[\"_value\"])  })) \' + \"\\n\" +\n    \'|> difference(columns: [\"val\"], keepFirst: true)\' + \"\\n\" +\n    \'|> elapsed(unit: 1s, columnName: \"dur\", timeColumn: \"_time\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with type: \' + \"\\n\" +\n    \'        if r.property == \"State_Task\" and r.val == 1 then \"noTask\"\' + \"\\n\" +\n    \'        else if r.property == \"State_Task\" and r.val == -1 then \"task\"\' + \"\\n\" +\n    \'        else if r.property == \"State_Task\" and r.val == 0 then \' + \"\\n\" +\n    \'            if r._value then \"task\" else \"noTask\"\' + \"\\n\" +\n    \'        else \"unknow\"\' + \"\\n\" +\n    \'    })\' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"dur\", \"type\"])\' + \"\\n\" +\n    \'|> filter(fn: (r) => \' + \"\\n\" +\n    \'    r[\"type\"] == \"task\" \' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> group(columns: [\"type\"])\' + \"\\n\" +\n    \'|> sum(column: \"dur\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with durSeconds: r[\"dur\"]} ))\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with durStr: string(v: duration( v: ( r[\"durSeconds\"]*1000*1000*1000 ) )) }))\' + \"\\n\";\n\nvar r = me.queryData(q);\nreturn r;', 1, 0, 1540174679756439554, '苏尚群', '2024-08-16 15:06:10', 1540174679756439554, '苏尚群', '2024-08-16 15:06:10', 0, 0, 0, 0, 999);
INSERT INTO `thing_service` (`ID`, `thing_model_id`, `service_name`, `service_type`, `descript`, `override`, `input_data`, `output_data`, `service_code`, `VERSION`, `DELETED`, `CREATOR_ID`, `CREATOR`, `CREATE_TIME`, `UPDATOR_ID`, `UPDATOR`, `UPDATE_TIME`, `ENGINEERING_ID`, `MODULE_ID`, `SPACE_ID`, `async`, `tenant_id`) VALUES (1000000000088135, 999, 'queryDevicesFaultTimeSum', 0, '查询多设备累计故障时长', 0, '[{\"jSON\": true, \"name\": \"deviceIds\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"end\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"begin\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}]', '{\"jSON\": true, \"isArray\": null, \"dataType\": 31, \"descript\": \"\", \"dataModelId\": null, \"outputDataDescript\": \"\"}', 'var ids = input.deviceIds;\nvar idArray = ids.split(\',\');\n\n// var tmpArray = [];\n// for (var i = 0; i < idArray.length; i++) {\n//     tmpArray.push(\'r[\"deviceId\"] == \"\' + idArray[i] + \'\"\');\n// }\n// var idsStr = tmpArray.join(\' or \');\n\nvar idsStr = \'r[\"deviceId\"] =~ /\' + idArray.join(\'|\') + \'/\';\n\nvar q =\n    \'queryBegin = time(v: \' + input.begin + \'+08:00)\' + \"\\n\" +\n    \'queryEnd = time(v: \' + input.end + \'+08:00)\' + \"\\n\" +\n    \'nowTime = now()\' + \"\\n\" +\n    \'nowOrEnd = if nowTime < queryEnd then nowTime else queryEnd\' + \"\\n\" +\n    \'nowOrBegin = if nowTime < queryBegin then nowTime else queryBegin\' + \"\\n\" +\n\n    \'t0 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: 0, stop: queryBegin)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and ( \' + idsStr + \' ) )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Fault\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrBegin  })) \' + \"\\n\" +\n\n    \'t1 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and ( \' + idsStr + \' ) )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Fault\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n\n    \'t2 = \' + \"\\n\" +\n    \'from(bucket: \"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and ( \' + idsStr + \' ) )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Fault\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrEnd  })) \' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _value: if r[\"_value\"] then false else true })) \' + \"\\n\" +\n\n    \'union(tables: [t0, t1, t2])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with val: int(v: r[\"_value\"])  })) \' + \"\\n\" +\n    \'|> difference(columns: [\"val\"], keepFirst: true)\' + \"\\n\" +\n    \'|> elapsed(unit: 1s, columnName: \"dur\", timeColumn: \"_time\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with type: \' + \"\\n\" +\n    \'        if r.property == \"State_Fault\" and r.val == 1 then \"noFault\"\' + \"\\n\" +\n    \'        else if r.property == \"State_Fault\" and r.val == -1 then \"fault\"\' + \"\\n\" +\n    \'        else if r.property == \"State_Fault\" and r.val == 0 then \' + \"\\n\" +\n    \'            if r._value then \"fault\" else \"noFault\"\' + \"\\n\" +\n    \'        else \"unknow\"\' + \"\\n\" +\n    \'    })\' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\", \"dur\", \"type\"])\' + \"\\n\" +\n    \'|> filter(fn: (r) => \' + \"\\n\" +\n    \'    r[\"type\"] == \"fault\" \' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> group(columns: [\"type\"])\' + \"\\n\" +\n    \'|> sum(column: \"dur\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with durSeconds: r[\"dur\"]} ))\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with durStr: string(v: duration( v: ( r[\"durSeconds\"]*1000*1000*1000 ) )) }))\' + \"\\n\";\n\nvar r = me.queryData(q);\nreturn r;', 1, 0, 1540174679756439554, '苏尚群', '2024-08-16 15:06:10', 1540174679756439554, '苏尚群', '2024-08-16 15:06:10', 0, 0, 0, 0, 999);
INSERT INTO `thing_service` (`ID`, `thing_model_id`, `service_name`, `service_type`, `descript`, `override`, `input_data`, `output_data`, `service_code`, `VERSION`, `DELETED`, `CREATOR_ID`, `CREATOR`, `CREATE_TIME`, `UPDATOR_ID`, `UPDATOR`, `UPDATE_TIME`, `ENGINEERING_ID`, `MODULE_ID`, `SPACE_ID`, `async`, `tenant_id`) VALUES (1000000000088136, 999, 'queryDevicesFaultCount', 0, '查询多设备故障次数', 0, '[{\"jSON\": true, \"name\": \"deviceIds\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"end\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"begin\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}]', '{\"jSON\": true, \"isArray\": null, \"dataType\": 31, \"descript\": \"\", \"dataModelId\": null, \"outputDataDescript\": \"\"}', 'var ids = input.deviceIds;\nvar idArray = ids.split(\',\');\n\n// var tmpArray = [];\n// for (var i = 0; i < idArray.length; i++) {\n//     tmpArray.push(\'r[\"deviceId\"] == \"\' + idArray[i] + \'\"\');\n// }\n// var idsStr = tmpArray.join(\' or \');\n\nvar idsStr = \'r[\"deviceId\"] =~ /\' + idArray.join(\'|\') + \'/\';\n\nvar q =\n    \'queryBegin = time(v: \' + input.begin + \'+08:00)\' + \"\\n\" +\n    \'queryEnd = time(v: \' + input.end + \'+08:00)\' + \"\\n\" +\n    \'nowTime = now()\' + \"\\n\" +\n    \'nowOrEnd = if nowTime < queryEnd then nowTime else queryEnd\' + \"\\n\" +\n    \'nowOrBegin = if nowTime < queryBegin then nowTime else queryBegin\' + \"\\n\" +\n\n    \'t0 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: 0, stop: queryBegin)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and ( \' + idsStr + \' ) )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Fault\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrBegin  })) \' + \"\\n\" +\n\n    \'t1 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and ( \' + idsStr + \' ) )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Fault\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n\n    \'t2 = \' + \"\\n\" +\n    \'from(bucket: \"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and ( \' + idsStr + \' ) )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Fault\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrEnd  })) \' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _value: if r[\"_value\"] then false else true })) \' + \"\\n\" +\n\n    \'union(tables: [t0, t1, t2])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with val: int(v: r[\"_value\"])  })) \' + \"\\n\" +\n    \'|> difference(columns: [\"val\"], keepFirst: true)\' + \"\\n\" +\n    \'|> elapsed(unit: 1s, columnName: \"dur\", timeColumn: \"_time\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with type: \' + \"\\n\" +\n    \'        if r.property == \"State_Fault\" and r.val == 1 then \"noFault\"\' + \"\\n\" +\n    \'        else if r.property == \"State_Fault\" and r.val == -1 then \"fault\"\' + \"\\n\" +\n    \'        else if r.property == \"State_Fault\" and r.val == 0 then \' + \"\\n\" +\n    \'            if r._value then \"fault\" else \"noFault\"\' + \"\\n\" +\n    \'        else \"unknow\"\' + \"\\n\" +\n    \'    })\' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\", \"dur\", \"type\"])\' + \"\\n\" +\n    \'|> filter(fn: (r) => \' + \"\\n\" +\n    \'    r[\"type\"] == \"fault\" \' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> count(column: \"dur\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with faultCount: r[\"dur\"] }))\';\n\n\nvar r = me.queryData(q);\nreturn r;', 1, 0, 1540174679756439554, '苏尚群', '2024-08-16 15:06:10', 1540174679756439554, '苏尚群', '2024-08-16 15:06:10', 0, 0, 0, 0, 999);
INSERT INTO `thing_service` (`ID`, `thing_model_id`, `service_name`, `service_type`, `descript`, `override`, `input_data`, `output_data`, `service_code`, `VERSION`, `DELETED`, `CREATOR_ID`, `CREATOR`, `CREATE_TIME`, `UPDATOR_ID`, `UPDATOR`, `UPDATE_TIME`, `ENGINEERING_ID`, `MODULE_ID`, `SPACE_ID`, `async`, `tenant_id`) VALUES (1000000000088137, 999, 'queryFaultDevicesNumber', 0, '查询故障设备数量', 0, '[{\"jSON\": true, \"name\": \"deviceIds\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"end\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"begin\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}]', '{\"jSON\": true, \"isArray\": null, \"dataType\": 31, \"descript\": \"\", \"dataModelId\": null, \"outputDataDescript\": \"\"}', 'var ids = input.deviceIds;\nvar idArray = ids.split(\',\');\n\n// var tmpArray = [];\n// for (var i = 0; i < idArray.length; i++) {\n//     tmpArray.push(\'r[\"deviceId\"] == \"\' + idArray[i] + \'\"\');\n// }\n// var idsStr = tmpArray.join(\' or \');\n\nvar idsStr = \'r[\"deviceId\"] =~ /\' + idArray.join(\'|\') + \'/\';\n\nvar q =\n    \'queryBegin = time(v: \' + input.begin + \'+08:00)\' + \"\\n\" +\n    \'queryEnd = time(v: \' + input.end + \'+08:00)\' + \"\\n\" +\n    \'nowTime = now()\' + \"\\n\" +\n    \'nowOrEnd = if nowTime < queryEnd then nowTime else queryEnd\' + \"\\n\" +\n    \'nowOrBegin = if nowTime < queryBegin then nowTime else queryBegin\' + \"\\n\" +\n\n    \'t0 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: 0, stop: queryBegin)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and ( \' + idsStr + \' ) )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Fault\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrBegin  })) \' + \"\\n\" +\n\n    \'t1 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and ( \' + idsStr + \' ) )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Fault\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n\n    \'t2 = \' + \"\\n\" +\n    \'from(bucket: \"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and ( \' + idsStr + \' ) )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Fault\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrEnd  })) \' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _value: if r[\"_value\"] then false else true })) \' + \"\\n\" +\n\n\n    \'union(tables: [t0, t1, t2])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_value\"] == true)\' + \"\\n\" +\n    \'|> distinct()\' + \"\\n\" +\n    \'|> keep(columns: [\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> count()\';\n\nvar r = me.queryData(q);\nreturn r;', 1, 0, 1540174679756439554, '苏尚群', '2024-08-16 15:06:10', 1540174679756439554, '苏尚群', '2024-08-16 15:06:10', 0, 0, 0, 0, 999);
INSERT INTO `thing_service` (`ID`, `thing_model_id`, `service_name`, `service_type`, `descript`, `override`, `input_data`, `output_data`, `service_code`, `VERSION`, `DELETED`, `CREATOR_ID`, `CREATOR`, `CREATE_TIME`, `UPDATOR_ID`, `UPDATOR`, `UPDATE_TIME`, `ENGINEERING_ID`, `MODULE_ID`, `SPACE_ID`, `async`, `tenant_id`) VALUES (1000000000088138, 999, 'queryDevicesFaultTimeSumTopn', 0, '查询设备累计故障时长前n名', 0, '[{\"jSON\": true, \"name\": \"n\", \"isArray\": false, \"dataType\": 4, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"deviceIds\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"end\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"begin\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}]', '{\"jSON\": true, \"isArray\": null, \"dataType\": 31, \"descript\": \"\", \"dataModelId\": null, \"outputDataDescript\": \"\"}', 'var ids = input.deviceIds;\nvar idArray = ids.split(\',\');\n\n// var tmpArray = [];\n// for (var i = 0; i < idArray.length; i++) {\n//     tmpArray.push(\'r[\"deviceId\"] == \"\' + idArray[i] + \'\"\');\n// }\n// var idsStr = tmpArray.join(\' or \');\n\nvar idsStr = \'r[\"deviceId\"] =~ /\' + idArray.join(\'|\') + \'/\';\n\nvar n = input.n;\nvar q =\n    \'queryBegin = time(v: \' + input.begin + \'+08:00)\' + \"\\n\" +\n    \'queryEnd = time(v: \' + input.end + \'+08:00)\' + \"\\n\" +\n    \'nowTime = now()\' + \"\\n\" +\n    \'nowOrEnd = if nowTime < queryEnd then nowTime else queryEnd\' + \"\\n\" +\n    \'nowOrBegin = if nowTime < queryBegin then nowTime else queryBegin\' + \"\\n\" +\n\n    \'t0 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: 0, stop: queryBegin)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and ( \' + idsStr + \' ) )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Fault\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrBegin  })) \' + \"\\n\" +\n\n    \'t1 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and ( \' + idsStr + \' ) )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Fault\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n\n    \'t2 = \' + \"\\n\" +\n    \'from(bucket: \"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and ( \' + idsStr + \' ) )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Fault\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrEnd  })) \' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _value: if r[\"_value\"] then false else true })) \' + \"\\n\" +\n\n    \'union(tables: [t0, t1, t2])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with val: int(v: r[\"_value\"])  })) \' + \"\\n\" +\n    \'|> difference(columns: [\"val\"], keepFirst: true)\' + \"\\n\" +\n    \'|> elapsed(unit: 1s, columnName: \"dur\", timeColumn: \"_time\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with type: \' + \"\\n\" +\n    \'        if r.property == \"State_Fault\" and r.val == 1 then \"noFault\"\' + \"\\n\" +\n    \'        else if r.property == \"State_Fault\" and r.val == -1 then \"fault\"\' + \"\\n\" +\n    \'        else if r.property == \"State_Fault\" and r.val == 0 then \' + \"\\n\" +\n    \'            if r._value then \"fault\" else \"noFault\"\' + \"\\n\" +\n    \'        else \"unknow\"\' + \"\\n\" +\n    \'    })\' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\", \"dur\", \"type\"])\' + \"\\n\" +\n    \'|> filter(fn: (r) => \' + \"\\n\" +\n    \'    r[\"type\"] == \"fault\" \' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> group(columns: [\"deviceId\",\"type\"])\' + \"\\n\" +\n    \'|> sum(column: \"dur\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with durSeconds: r[\"dur\"]} ))\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with durStr: string(v: duration( v: ( r[\"durSeconds\"]*1000*1000*1000 ) )) }))\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with id: r[\"deviceId\"] })) \' + \"\\n\" +\n    \'|> drop(columns: [\"type\", \"deviceId\"]) \' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with dura: r[\"dur\"] })) \' + \"\\n\" +\n    \'|> sort(columns: [\"dura\"], desc: true)\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with deviceId: r[\"id\"] })) \' + \"\\n\" +\n    \'|> limit(n: \' + n + \')\';\n\n\nvar r = me.queryData(q);\nreturn r;', 1, 0, 1540174679756439554, '苏尚群', '2024-08-16 15:06:10', 1540174679756439554, '苏尚群', '2024-08-16 15:06:10', 0, 0, 0, 0, 999);
INSERT INTO `thing_service` (`ID`, `thing_model_id`, `service_name`, `service_type`, `descript`, `override`, `input_data`, `output_data`, `service_code`, `VERSION`, `DELETED`, `CREATOR_ID`, `CREATOR`, `CREATE_TIME`, `UPDATOR_ID`, `UPDATOR`, `UPDATE_TIME`, `ENGINEERING_ID`, `MODULE_ID`, `SPACE_ID`, `async`, `tenant_id`) VALUES (1000000000088139, 999, 'queryDevicesFaultCountByWindow', 0, '查询多设备分组故障次数', 0, '[{\"jSON\": true, \"name\": \"deviceIds\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"every\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"end\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"begin\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}]', '{\"jSON\": true, \"isArray\": null, \"dataType\": 31, \"descript\": \"\", \"dataModelId\": null, \"outputDataDescript\": \"\"}', 'var ids = input.deviceIds;\nvar idArray = ids.split(\',\');\n\n// var tmpArray = [];\n// for (var i = 0; i < idArray.length; i++) {\n//     tmpArray.push(\'r[\"deviceId\"] == \"\' + idArray[i] + \'\"\');\n// }\n// var idsStr = tmpArray.join(\' or \');\n\nvar idsStr = \'r[\"deviceId\"] =~ /\' + idArray.join(\'|\') + \'/\';\n\nvar q =\n    \'queryBegin = time(v: \' + input.begin + \'+08:00)\' + \"\\n\" +\n    \'queryEnd = time(v: \' + input.end + \'+08:00)\' + \"\\n\" +\n    \'nowTime = now()\' + \"\\n\" +\n    \'nowOrEnd = if nowTime < queryEnd then nowTime else queryEnd\' + \"\\n\" +\n    \'nowOrBegin = if nowTime < queryBegin then nowTime else queryBegin\' + \"\\n\" +\n\n    \'t0 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: 0, stop: queryBegin)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and ( \' + idsStr + \' ) )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Fault\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrBegin  })) \' + \"\\n\" +\n\n    \'t1 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and ( \' + idsStr + \' ) )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Fault\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n\n    \'t2 = \' + \"\\n\" +\n    \'from(bucket: \"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and ( \' + idsStr + \' ) )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Fault\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrEnd  })) \' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _value: if r[\"_value\"] then false else true })) \' + \"\\n\" +\n\n    \'union(tables: [t0, t1, t2])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with val: int(v: r[\"_value\"])  })) \' + \"\\n\" +\n    \'|> difference(columns: [\"val\"], keepFirst: true)\' + \"\\n\" +\n    \'|> elapsed(unit: 1s, columnName: \"dur\", timeColumn: \"_time\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with type: \' + \"\\n\" +\n    \'        if r.property == \"State_Fault\" and r.val == 1 then \"noFault\"\' + \"\\n\" +\n    \'        else if r.property == \"State_Fault\" and r.val == -1 then \"fault\"\' + \"\\n\" +\n    \'        else if r.property == \"State_Fault\" and r.val == 0 then \' + \"\\n\" +\n    \'            if r._value then \"fault\" else \"noFault\"\' + \"\\n\" +\n    \'        else \"unknow\"\' + \"\\n\" +\n    \'    })\' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\", \"dur\", \"type\"])\' + \"\\n\" +\n    \'|> filter(fn: (r) => \' + \"\\n\" +\n    \'    r[\"type\"] == \"fault\" \' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> aggregateWindow(every: \' + input.every + \', fn: count, createEmpty: false, offset: -8h, column: \"dur\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with faultCount: r[\"dur\"] }))\' + \"\\n\" +\n    \'|> drop(columns: [\"_start\",\"_stop\", \"dur\"])\' + \"\\n\";\nvar r = me.queryData(q);\nreturn r;', 1, 0, 1540174679756439554, '苏尚群', '2024-08-16 15:06:10', 1540174679756439554, '苏尚群', '2024-08-16 15:06:10', 0, 0, 0, 0, 999);
INSERT INTO `thing_service` (`ID`, `thing_model_id`, `service_name`, `service_type`, `descript`, `override`, `input_data`, `output_data`, `service_code`, `VERSION`, `DELETED`, `CREATOR_ID`, `CREATOR`, `CREATE_TIME`, `UPDATOR_ID`, `UPDATOR`, `UPDATE_TIME`, `ENGINEERING_ID`, `MODULE_ID`, `SPACE_ID`, `async`, `tenant_id`) VALUES (1000000000088140, 999, 'queryDevicesFaultCountTopn', 0, '查询设备累计故障次数前n名', 0, '[{\"jSON\": true, \"name\": \"n\", \"isArray\": false, \"dataType\": 4, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"deviceIds\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"end\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"begin\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}]', '{\"jSON\": true, \"isArray\": null, \"dataType\": 31, \"descript\": \"\", \"dataModelId\": null, \"outputDataDescript\": \"\"}', 'var ids = input.deviceIds;\nvar idArray = ids.split(\',\');\n\n// var tmpArray = [];\n// for (var i = 0; i < idArray.length; i++) {\n//     tmpArray.push(\'r[\"deviceId\"] == \"\' + idArray[i] + \'\"\');\n// }\n// var idsStr = tmpArray.join(\' or \');\n\nvar idsStr = \'r[\"deviceId\"] =~ /\' + idArray.join(\'|\') + \'/\';\n\nvar n = input.n;\nvar q =\n    \'queryBegin = time(v: \' + input.begin + \'+08:00)\' + \"\\n\" +\n    \'queryEnd = time(v: \' + input.end + \'+08:00)\' + \"\\n\" +\n    \'nowTime = now()\' + \"\\n\" +\n    \'nowOrEnd = if nowTime < queryEnd then nowTime else queryEnd\' + \"\\n\" +\n    \'nowOrBegin = if nowTime < queryBegin then nowTime else queryBegin\' + \"\\n\" +\n\n    \'t0 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: 0, stop: queryBegin)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and ( \' + idsStr + \' ) )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Fault\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrBegin  })) \' + \"\\n\" +\n\n    \'t1 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and ( \' + idsStr + \' ) )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Fault\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n\n    \'t2 = \' + \"\\n\" +\n    \'from(bucket: \"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and ( \' + idsStr + \' ) )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Fault\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrEnd  })) \' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _value: if r[\"_value\"] then false else true })) \' + \"\\n\" +\n\n    \'union(tables: [t0, t1, t2])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with val: int(v: r[\"_value\"])  })) \' + \"\\n\" +\n    \'|> difference(columns: [\"val\"], keepFirst: true)\' + \"\\n\" +\n    \'|> elapsed(unit: 1s, columnName: \"dur\", timeColumn: \"_time\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with type: \' + \"\\n\" +\n    \'        if r.property == \"State_Fault\" and r.val == 1 then \"noFault\"\' + \"\\n\" +\n    \'        else if r.property == \"State_Fault\" and r.val == -1 then \"fault\"\' + \"\\n\" +\n    \'        else if r.property == \"State_Fault\" and r.val == 0 then \' + \"\\n\" +\n    \'            if r._value then \"fault\" else \"noFault\"\' + \"\\n\" +\n    \'        else \"unknow\"\' + \"\\n\" +\n    \'    })\' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\", \"dur\", \"type\"])\' + \"\\n\" +\n    \'|> filter(fn: (r) => \' + \"\\n\" +\n    \'    r[\"type\"] == \"fault\" \' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> group(columns: [\"deviceId\",\"type\"])\' + \"\\n\" +\n    \'|> count(column: \"dur\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with id: r[\"deviceId\"] })) \' + \"\\n\" +\n    \'|> drop(columns: [\"type\", \"deviceId\"]) \' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with dura: r[\"dur\"] })) \' + \"\\n\" +\n    \'|> sort(columns: [\"dura\"], desc: true)\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with deviceId: r[\"id\"] })) \' + \"\\n\" +\n    \'|> limit(n: \' + n + \')\';\n\n\nvar r = me.queryData(q);\nreturn r;', 1, 0, 1540174679756439554, '苏尚群', '2024-08-16 15:06:10', 1540174679756439554, '苏尚群', '2024-08-16 15:06:10', 0, 0, 0, 0, 999);
INSERT INTO `thing_service` (`ID`, `thing_model_id`, `service_name`, `service_type`, `descript`, `override`, `input_data`, `output_data`, `service_code`, `VERSION`, `DELETED`, `CREATOR_ID`, `CREATOR`, `CREATE_TIME`, `UPDATOR_ID`, `UPDATOR`, `UPDATE_TIME`, `ENGINEERING_ID`, `MODULE_ID`, `SPACE_ID`, `async`, `tenant_id`) VALUES (1000000000088141, 999, 'queryHealthDegree', 0, '查询健康度', 0, '[{\"jSON\": true, \"name\": \"end\", \"isArray\": false, \"dataType\": 14, \"descript\": \"结束\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"begin\", \"isArray\": false, \"dataType\": 14, \"descript\": \"开始\", \"editable\": null, \"dataModelId\": null}]', '{\"jSON\": true, \"isArray\": null, \"dataType\": 31, \"descript\": \"\", \"dataModelId\": null, \"outputDataDescript\": \"\"}', 'var q =\n    \'queryBegin = time(v: \' + input.begin + \'+08:00)\' + \"\\n\" +\n    \'queryEnd = time(v: \' + input.end + \'+08:00)\' + \"\\n\" +\n    \'nowTime = now()\' + \"\\n\" +\n    \'nowOrEnd = if nowTime < queryEnd then nowTime else queryEnd\' + \"\\n\" +\n    \'nowOrBegin = if nowTime < queryBegin then nowTime else queryBegin\' + \"\\n\" +\n\n    \'t0 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: 0, stop: queryBegin)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and r[\"deviceId\"] == \"\' + me.id + \'\")\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Fault\" or r[\"property\"] == \"State_OnLine\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrBegin  })) \' + \"\\n\" +\n\n    \'t1 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and r[\"deviceId\"] == \"\' + me.id + \'\")\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Fault\" or r[\"property\"] == \"State_OnLine\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n\n    \'t2 = \' + \"\\n\" +\n    \'from(bucket: \"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and r[\"deviceId\"] == \"\' + me.id + \'\")\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Fault\" or r[\"property\"] == \"State_OnLine\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrEnd  })) \' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _value: if r[\"_value\"] then false else true })) \' + \"\\n\" +\n\n    \'t3 =\' + \"\\n\" +\n    \'union(tables: [t0, t1, t2])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n\n    \'t3\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Fault\" or r[\"property\"] == \"State_OnLine\")\' + \"\\n\" +\n    \'\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with val: int(v: r[\"_value\"])  })) \' + \"\\n\" +\n    \'|> difference(columns: [\"val\"], keepFirst: true)\' + \"\\n\" +\n    \'|> elapsed(unit: 1s, columnName: \"dur\", timeColumn: \"_time\")\' + \"\\n\" +\n    \'\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with type: \' + \"\\n\" +\n    \'        if r.property == \"State_OnLine\" and r.val == 1 then \"offline\" \' + \"\\n\" +\n    \'        else if r.property == \"State_OnLine\" and r.val == -1 then \"online\"\' + \"\\n\" +\n    \'        else if r.property == \"State_OnLine\" and r.val == 0 then \' + \"\\n\" +\n    \'            if r._value then \"online\" else \"offline\"\' + \"\\n\" +\n    \'        else if r.property == \"State_Fault\" and r.val == 1 then \"noFault\"\' + \"\\n\" +\n    \'        else if r.property == \"State_Fault\" and r.val == -1 then \"fault\"\' + \"\\n\" +\n    \'        else if r.property == \"State_Fault\" and r.val == 0 then \' + \"\\n\" +\n    \'            if r._value then \"fault\" else \"noFault\"\' + \"\\n\" +\n    \'        else \"unknow\"\' + \"\\n\" +\n    \'    })\' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\", \"dur\", \"type\"])\' + \"\\n\" +\n    \'|> filter(fn: (r) => \' + \"\\n\" +\n    \'    r[\"type\"] == \"fault\" \' + \"\\n\" +\n    \'    or r[\"type\"] == \"online\" \' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> reduce(\' + \"\\n\" +\n    \'    fn: (r, accumulator) => ({\' + \"\\n\" +\n    \'        faultCount: (if r.type == \"fault\" then 1 else 0) + accumulator.faultCount,\' + \"\\n\" +\n    \'        faultSum: (if r.type == \"fault\" then r.dur else 0) + accumulator.faultSum, \' + \"\\n\" +\n    \'        onlineSum: (if r.type == \"online\" then r.dur else 0) + accumulator.onlineSum\' + \"\\n\" +\n    \'    }),\' + \"\\n\" +\n    \'    identity: {faultCount: 0, faultSum: 0, onlineSum: 0},\' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with mtbf: if r.faultCount > 0 then (float(v: r[\"onlineSum\"])/60.0/60.0/float(v: r[\"faultCount\"])) else 1.0 }))\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with mttr: if r.faultCount > 0 then (float(v:r[\"faultSum\"])/60.0/60.0/float(v:r[\"faultCount\"])) else 1.0 }))\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with availability: if r.faultCount > 0 then (r[\"mtbf\"] / (r[\"mtbf\"] + r[\"mttr\"])) else 1.0 }))\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with faultFrequency: if r.onlineSum > 0 then (1.0 - (float(v:r[\"faultCount\"]) / (float(v: r[\"onlineSum\"]) /60.00/60.00 ))) else 1.0 }))\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with faultDegree: 1.0 - (r[\"mttr\"] / r[\"mtbf\"]) }))\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with healthDegree: r[\"availability\"] * r[\"faultFrequency\"] * r[\"faultDegree\"]}))\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with healthDegree: if r.healthDegree > 0 then r.healthDegree else 1.0 }))\';\n\nvar r = me.queryData(q);\nreturn r;', 1, 0, 1540174679756439554, '苏尚群', '2024-08-16 15:06:10', 1540174679756439554, '苏尚群', '2024-08-16 15:06:10', 0, 0, 0, 0, 999);
INSERT INTO `thing_service` (`ID`, `thing_model_id`, `service_name`, `service_type`, `descript`, `override`, `input_data`, `output_data`, `service_code`, `VERSION`, `DELETED`, `CREATOR_ID`, `CREATOR`, `CREATE_TIME`, `UPDATOR_ID`, `UPDATOR`, `UPDATE_TIME`, `ENGINEERING_ID`, `MODULE_ID`, `SPACE_ID`, `async`, `tenant_id`) VALUES (1000000000088142, 999, 'queryHealthDegreeByWindow', 0, '查询分组健康度', 0, '[{\"jSON\": true, \"name\": \"every\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"end\", \"isArray\": false, \"dataType\": 14, \"descript\": \"结束\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"begin\", \"isArray\": false, \"dataType\": 14, \"descript\": \"开始\", \"editable\": null, \"dataModelId\": null}]', '{\"jSON\": true, \"isArray\": null, \"dataType\": 31, \"descript\": \"\", \"dataModelId\": null, \"outputDataDescript\": \"\"}', 'var q =\n    \'import \"date\"\' + \"\\n\" +\n    \'queryBegin = time(v: \' + input.begin + \'+08:00)\' + \"\\n\" +\n    \'queryEnd = time(v: \' + input.end + \'+08:00)\' + \"\\n\" +\n    \'nowTime = now()\' + \"\\n\" +\n    \'nowOrEnd = if nowTime < queryEnd then nowTime else queryEnd\' + \"\\n\" +\n    \'nowOrBegin = if nowTime < queryBegin then nowTime else queryBegin\' + \"\\n\" +\n\n    \'t0 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: 0, stop: queryBegin)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and r[\"deviceId\"] == \"\' + me.id + \'\")\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Fault\" or r[\"property\"] == \"State_OnLine\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrBegin  })) \' + \"\\n\" +\n\n    \'t1 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and r[\"deviceId\"] == \"\' + me.id + \'\")\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Fault\" or r[\"property\"] == \"State_OnLine\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n\n    \'t2 = \' + \"\\n\" +\n    \'from(bucket: \"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and r[\"deviceId\"] == \"\' + me.id + \'\")\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Fault\" or r[\"property\"] == \"State_OnLine\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrEnd  })) \' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _value: if r[\"_value\"] then false else true })) \' + \"\\n\" +\n\n    \'t3 =\' + \"\\n\" +\n    \'union(tables: [t0, t1, t2])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'t4 = t3\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> window(every: \' + input.every + \', offset: -8h)\' + \"\\n\" +\n    \'|> last()\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: date.sub(d: 1s, from: r[\"_stop\"])  }))\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'t5 = \' + \"\\n\" +\n    \'union(tables: [t3, t4])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n\n    \'t5\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Fault\" or r[\"property\"] == \"State_OnLine\")\' + \"\\n\" +\n    \'\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with val: int(v: r[\"_value\"])  })) \' + \"\\n\" +\n    \'|> difference(columns: [\"val\"], keepFirst: true)\' + \"\\n\" +\n    \'|> elapsed(unit: 1s, columnName: \"dur\", timeColumn: \"_time\")\' + \"\\n\" +\n    \'\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with type: \' + \"\\n\" +\n    \'        if r.property == \"State_OnLine\" and r.val == 1 then \"offline\" \' + \"\\n\" +\n    \'        else if r.property == \"State_OnLine\" and r.val == -1 then \"online\"\' + \"\\n\" +\n    \'        else if r.property == \"State_OnLine\" and r.val == 0 then \' + \"\\n\" +\n    \'            if r._value then \"online\" else \"offline\"\' + \"\\n\" +\n    \'        else if r.property == \"State_Fault\" and r.val == 1 then \"noFault\"\' + \"\\n\" +\n    \'        else if r.property == \"State_Fault\" and r.val == -1 then \"fault\"\' + \"\\n\" +\n    \'        else if r.property == \"State_Fault\" and r.val == 0 then \' + \"\\n\" +\n    \'            if r._value then \"fault\" else \"noFault\"\' + \"\\n\" +\n    \'        else \"unknow\"\' + \"\\n\" +\n    \'    })\' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\", \"dur\", \"type\"])\' + \"\\n\" +\n    \'|> filter(fn: (r) => \' + \"\\n\" +\n    \'    r[\"type\"] == \"fault\" \' + \"\\n\" +\n    \'    or r[\"type\"] == \"online\" \' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> aggregateWindow(every: \' + input.every + \', createEmpty: false, offset: -8h, column: \"dur\", fn: (column, tables=<-) => tables\' + \"\\n\" +\n    \'|> reduce(\' + \"\\n\" +\n    \'    fn: (r, accumulator) => ({\' + \"\\n\" +\n    \'        faultCount: (if r.type == \"fault\" then 1 else 0) + accumulator.faultCount,\' + \"\\n\" +\n    \'        faultSum: (if r.type == \"fault\" then r.dur else 0) + accumulator.faultSum, \' + \"\\n\" +\n    \'        onlineSum: (if r.type == \"online\" then r.dur else 0) + accumulator.onlineSum\' + \"\\n\" +\n    \'    }),\' + \"\\n\" +\n    \'    identity: {faultCount: 0, faultSum: 0, onlineSum: 0},\' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with mtbf: if r.faultCount > 0 then (float(v: r[\"onlineSum\"])/60.0/60.0/float(v: r[\"faultCount\"])) else 1.0 }))\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with mttr: if r.faultCount > 0 then (float(v:r[\"faultSum\"])/60.0/60.0/float(v:r[\"faultCount\"])) else 1.0 }))\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with availability: if r.faultCount > 0 then (r[\"mtbf\"] / (r[\"mtbf\"] + r[\"mttr\"])) else 1.0 }))\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with faultFrequency: if r.onlineSum > 0 then (1.0 - (float(v:r[\"faultCount\"]) / (float(v: r[\"onlineSum\"]) /60.00/60.00 ))) else 1.0 }))\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with faultDegree: 1.0 - (r[\"mttr\"] / r[\"mtbf\"]) }))\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with healthDegree: r[\"availability\"] * r[\"faultFrequency\"] * r[\"faultDegree\"]}))\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with healthDegree: if r.healthDegree > 0 then r.healthDegree else 1.0 }))\' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: date.sub(d: 1s, from: r[\"_time\"])  }))\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"healthDegree\"] > 0 or r[\"healthDegree\"] < 0)\' + \"\\n\" +\n    \'|> drop(columns: [\"_start\", \"_stop\"])\';\n\nvar r = me.queryData(q);\nreturn r;', 2, 0, 1540174679756439554, '苏尚群', '2024-08-16 15:06:10', 1443471710403244034, '苏尚群', '2024-09-26 10:09:08', 0, 0, 0, 0, 999);
INSERT INTO `thing_service` (`ID`, `thing_model_id`, `service_name`, `service_type`, `descript`, `override`, `input_data`, `output_data`, `service_code`, `VERSION`, `DELETED`, `CREATOR_ID`, `CREATOR`, `CREATE_TIME`, `UPDATOR_ID`, `UPDATOR`, `UPDATE_TIME`, `ENGINEERING_ID`, `MODULE_ID`, `SPACE_ID`, `async`, `tenant_id`) VALUES (1000000000088143, 999, 'queryDevicesUseRate_new', 0, '查询多设备利用率', 0, '[{\"jSON\": true, \"name\": \"begin\", \"isArray\": false, \"dataType\": 14, \"descript\": \"开始时间，格式：2025-05-25T00:00:00\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"end\", \"isArray\": false, \"dataType\": 14, \"descript\": \"结束日期，格式：2025-05-26T00:00:00\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"deviceIds\", \"isArray\": false, \"dataType\": 14, \"descript\": \"设备id列表，多个用逗号隔开\", \"editable\": null, \"dataModelId\": null}]', '{\"jSON\": true, \"isArray\": null, \"dataType\": 31, \"descript\": \"\", \"dataModelId\": null, \"outputDataDescript\": \"\"}', 'var ids = input.deviceIds;\nvar idArray = ids.split(\',\');\n\nvar idsStr = \'r[\"deviceId\"] =~ /\' + idArray.join(\'|\') + \'/\';\n\nvar q =\n    \'import \"date\"\' + \"\\n\" +\n    \'import \"contrib/tomhollingworth/events\"\' + \"\\n\" +\n    \'queryBegin = time(v: \' + input.begin + \'+08:00)\' + \"\\n\" +\n    \'queryEnd = time(v: \' + input.end + \'+08:00)\' + \"\\n\" +\n    \'nowTime = now()\' + \"\\n\" +\n    \'nowOrEnd = if nowTime < queryEnd then nowTime else queryEnd\' + \"\\n\" +\n    \'nowOrBegin = if nowTime < queryBegin then nowTime else queryBegin\' + \"\\n\" +\n    \'state0 =from(bucket: \"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: 0 ,stop:queryBegin)\' + \"\\n\" +\n    \'|> filter(fn: (r) =>  ( \' + idsStr + \' ) )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State\" )\' + \"\\n\" +\n    \'|> last()\' + \"\\n\" +\n    \'|> map (fn: (r) => ({ r with _time: if r._time < queryBegin and r._value==1 then queryBegin else r._time }))\' + \"\\n\" +\n\n    \'state1 =from(bucket: \"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin ,stop:queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => ( \' + idsStr + \' ) )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State\" )\' + \"\\n\" +\n\n    \'state = union(tables: [state0, state1])\' + \"\\n\" +\n    \'|> group(columns:[\"deviceId\"])\' + \"\\n\" +\n    \'|> events.duration(unit: 1s,stop:nowOrEnd)\' + \"\\n\" +\n    \'|> group(columns:[\"deviceId\",\"_value\"])\' + \"\\n\" +\n    \'|> filter(fn: (r) => ( r[\"_value\"] == 1 ) )\' + \"\\n\" +\n    \'|> sum(column:\"duration\")\' + \"\\n\" +\n\n    \'status0 =from(bucket: \"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: 0 ,stop:queryBegin)\' + \"\\n\" +\n    \'|> filter(fn: (r) => ( \' + idsStr + \' ) )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"status\" )\' + \"\\n\" +\n    \'|> last()\' + \"\\n\" +\n    \'|> map (fn: (r) => ({ r with _time: if r._time < queryBegin and r._value==2 then queryBegin else r._time }))\' + \"\\n\" +\n\n    \'status1 =from(bucket: \"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin ,stop:queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => ( \' + idsStr + \' ) )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"status\" )\' + \"\\n\" +\n\n    \'status = union(tables: [status0, status1])\' + \"\\n\" +\n    \'|> group(columns:[\"deviceId\"])\' + \"\\n\" +\n    \'|> events.duration(unit: 1s,stop:nowOrEnd)\' + \"\\n\" +\n    \'|> group(columns:[\"deviceId\",\"_value\"])\' + \"\\n\" +\n    \'|> filter(fn: (r) => ( r[\"_value\"] == 2 ) )\' + \"\\n\" +\n    \'|> sum(column:\"duration\")\' + \"\\n\" +\n\n    \'onlineTimeFunc=(tables=<-) => tables |> reduce( fn: (r, accumulator) => ( { online:  if r._value ==2  then accumulator.online + r.duration else accumulator.online } ), identity: {online: 0}, )\' + \"\\n\" +\n    \'taskSpendFunc = (tables=<-) => tables |> reduce( fn: (r, accumulator) => ({ spend:  if r._value ==1  then accumulator.spend + r.duration else accumulator.spend }), identity: {spend: 0})\' + \"\\n\" +\n\n    \'sumTimeFunc = (tables=<-) => tables |> group() |> map(fn: (r) => ({r with online: if r.online < r.spend then r.spend else r.online  })) |> reduce( fn: (r, accumulator) => ({ taskSum:  accumulator.taskSum + r.spend , onlineSum:  accumulator.onlineSum + r.online , }), identity: {taskSum: 0,onlineSum: 0}, ) |> map(fn: (r) => ({ r with useRate:  float(v: r.taskSum) / float(v: r.onlineSum)}))\' + \"\\n\" +\n    \'taskSpend=taskSpendFunc(tables: state)\' + \"\\n\" +\n    \'onlineTime=onlineTimeFunc(tables: status)\' + \"\\n\" +\n    \'joinedTable = join(tables: {t1: taskSpend, t2: onlineTime}, on: [\"deviceId\"])\' + \"\\n\" +\n    \'sumTime=sumTimeFunc(tables:joinedTable)\' + \"\\n\" +\n    \'sumTime\' + \"\\n\";\n\nvar r = me.queryData(q);\nreturn r;', 1, 0, 1443471710403244034, '郑立凡', '2025-05-29 09:03:04', 1443471710403244034, '郑立凡', '2025-05-29 09:03:04', 0, 0, 0, 0, 999);
INSERT INTO `thing_service` (`ID`, `thing_model_id`, `service_name`, `service_type`, `descript`, `override`, `input_data`, `output_data`, `service_code`, `VERSION`, `DELETED`, `CREATOR_ID`, `CREATOR`, `CREATE_TIME`, `UPDATOR_ID`, `UPDATOR`, `UPDATE_TIME`, `ENGINEERING_ID`, `MODULE_ID`, `SPACE_ID`, `async`, `tenant_id`) VALUES (1000000000088144, 999, 'queryDeviceListRunningInfo', 0, '查询多设备运行情况', 0, '[{\"jSON\": true, \"name\": \"deviceIds\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"end\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"begin\", \"isArray\": false, \"dataType\": 14, \"descript\": \"\", \"editable\": null, \"dataModelId\": null}]', '{\"jSON\": true, \"isArray\": null, \"dataType\": 31, \"descript\": \"\", \"dataModelId\": null, \"outputDataDescript\": \"\"}', 'var ids = input.deviceIds;\nvar idArray = ids.split(\',\');\n\nvar idsStr = \'r[\"deviceId\"] =~ /\' + idArray.join(\'|\') + \'/\';\n\n// 构建 Flux 查询字符串\nvar q =\n    \'import \"date\"\\n\' +\n    \'import \"contrib/tomhollingworth/events\"\\n\' +\n    \'queryBegin = time(v: \' + input.begin + \'+08:00)\\n\' +\n    \'queryEnd = time(v: \' + input.end + \'+08:00)\\n\' +\n    \'nowTime = now()\\n\' +\n    \'nowOrEnd = if nowTime < queryEnd then nowTime else queryEnd\\n\' +\n    \'nowOrBegin = if nowTime < queryBegin then nowTime else queryBegin\\n\' +\n\n    // 查询 state 数据   // 查询初始状态（合并所有状态）\n    \'initialState = from(bucket: \"\' + me.influxBucket + \'\")\\n\' +\n    \'  |> range(start: 0, stop: queryBegin)\\n\' +\n    \'  |> filter(fn: (r) => \' + idsStr + \')\\n\' +\n    \'  |> filter(fn: (r) => r[\"property\"] == \"State\")\\n\' +\n    \'  |> last()\\n\' +\n    // 计算该状态在查询开始前的持续时间，并截断到queryBegin\n    \'|> map (fn: (r) => ({ r with _time: if r._time < queryBegin  then queryBegin else r._time})) \\n\' +\n\n    // 查询实时状态数据\n    \'state1 = from(bucket: \"\' + me.influxBucket + \'\")\\n\' +\n    \'  |> range(start: queryBegin, stop: nowOrEnd)\\n\' +\n    \'  |> filter(fn: (r) => \' + idsStr + \')\\n\' +\n    \'  |> filter(fn: (r) => r[\"property\"] == \"State\")\\n\' +\n\n    // 合并数据并计算持续时间\n    \'state = union(tables: [initialState, state1])\\n\' +\n    \' |> group(columns:[\"deviceId\"])\\n\' +\n    \' |> events.duration(unit: 1s,stop:nowOrEnd)\\n\' +\n\n    // 按状态分类\n    \'taskSpendData = state |> group() \' + \"\\n\" +\n    \'   |> group(columns:[\"deviceId\",\"_value\"])\\n\' +\n    \'  |> filter(fn: (r) => ( r[\"_value\"] == 1 ) )\\n\' +\n    \'  |> sum(column: \"duration\")\\n\' +\n\n    \'freeData = state |> group() \' + \"\\n\" +\n    \'   |> group(columns:[\"deviceId\",\"_value\"])\\n\' +\n    \'  |> filter(fn: (r) => ( r[\"_value\"] == 2 ) )\\n\' +\n    \'  |> sum(column: \"duration\")\\n\' +\n\n    \'faultData = state |> group() \' + \"\\n\" +\n    \'   |> group(columns:[\"deviceId\",\"_value\"])\\n\' +\n    \'  |> filter(fn: (r) => ( r[\"_value\"] == 3 ) )\\n\' +\n    \'  |> sum(column: \"duration\")\\n\' +\n\n    \'faultCountData = state |> group() \' + \"\\n\" +\n    \'   |> group(columns:[\"deviceId\",\"_value\"])\\n\' +\n    \'  |> filter(fn: (r) => ( r[\"_value\"] == 3 ) )\\n\' +\n\n    // 查询 status 数据\n    \'status0 = from(bucket: \"\' + me.influxBucket + \'\")\\n\' +\n    \'|> range(start: 0, stop: queryBegin)\\n\' +\n    \'|> filter(fn: (r) => \' + idsStr + \')\\n\' +\n    \'|> filter(fn: (r) => r[\"property\"] == \"status\")\\n\' +\n    \'|> last()\\n\' +\n    \'|> map(fn: (r) => ({ r with _time: if r._time < queryBegin and r._value == 2 then queryBegin else r._time }))\\n\' +\n\n    \'status1 = from(bucket: \"\' + me.influxBucket + \'\")\\n\' +\n    \'|> range(start: queryBegin, stop: nowOrEnd)\\n\' +\n    \'|> filter(fn: (r) => \' + idsStr + \')\\n\' +\n    \'|> filter(fn: (r) => r[\"property\"] == \"status\")\\n\' +\n\n    \'status = union(tables: [status0, status1])\\n\' +\n    \'|> group(columns:[\"deviceId\"])\' + \"\\n\" +\n    \'|> events.duration(unit: 1s, stop: nowOrEnd)\\n\' +\n    \'|> group(columns:[\"deviceId\",\"_value\"])\' + \"\\n\" +\n    \'|> filter(fn: (r) => ( r[\"_value\"] == 2 ) )\' + \"\\n\" +\n    \'|> sum(column:\"duration\")\' + \"\\n\" +\n\n    // 定义函数计算在线时长、任务时长和使用率\n    \'onlineTimeFunc = (tables=<-) => tables\\n\' +\n    \'|> group(columns: [\"deviceId\"])\\n\' +\n    \'|> reduce(fn: (r, accumulator) => ({ online: if r._value == 2 then accumulator.online + r.duration else accumulator.online }), identity: { online: 0 })\\n\' +\n\n\n\n    \'taskSpendFunc = (tables=<-) => tables\\n\' +\n    \'|> group(columns: [\"deviceId\"])\\n\' +\n    \'|> reduce(fn: (r, accumulator) => ({ spend: if r._value == 1 then accumulator.spend + r.duration else accumulator.spend }), identity: { spend: 0 })\\n\' +\n\n    \'freeFunc = (tables=<-) => tables\\n\' +\n    \'|> group(columns: [\"deviceId\"])\\n\' +\n    \'|> reduce(fn: (r, accumulator) => ({ free: if r._value == 2 then accumulator.free + r.duration else accumulator.free }), identity: { free: 0 })\\n\' +\n\n    \'faultFunc = (tables=<-) => tables\\n\' +\n    \'|> group(columns: [\"deviceId\"])\\n\' +\n    \'|> reduce(fn: (r, accumulator) => ({ fault: if r._value == 3 then accumulator.fault + r.duration else accumulator.fault }), identity: { fault: 0 })\\n\' +\n\n\n    \'faultCountFunc = (tables=<-) => tables\\n\' +\n    \'|> group(columns: [\"deviceId\"])\\n\' +\n    \'|> reduce(fn: (r, accumulator) => ({ faultCount: accumulator.faultCount + (if r._value == 3 then 1 else 0) }), identity: { faultCount: 0 })\\n\' +\n\n    // 执行计算\n    \'onlineTime = onlineTimeFunc(tables: status)\\n\' +\n    \'taskSpend = taskSpendFunc(tables: taskSpendData)\\n\' +\n    \'freeTime = freeFunc(tables: freeData)\\n\' +\n    \'faultTime = faultFunc(tables: faultData)\\n\' +\n    \'faultCount = faultCountFunc(tables: faultCountData)\\n\' +\n\n    \'result = union(tables: [\\n\' +\n    \'    taskSpend    |> map(fn: (r) => ({ r with _field: \"spend\", _value: r.spend })),\\n\' +\n    \'    onlineTime   |> map(fn: (r) => ({ r with _field: \"online\", _value: r.online })),\\n\' +\n    \'    freeTime     |> map(fn: (r) => ({ r with _field: \"free\", _value: r.free })),\\n\' +\n    \'    faultTime    |> map(fn: (r) => ({ r with _field: \"fault\", _value: r.fault })),\\n\' +\n    \'    faultCount   |> map(fn: (r) => ({ r with _field: \"faultCount\", _value: r.faultCount }))\\n\' +\n    \'])\\n\' +\n\n    // 按设备分组并聚合数据\n    \'|> group(columns: [\"deviceId\"])\\n\' +\n    \'|> reduce(\\n\' +\n    \'    fn: (r, accumulator) => ({\\n\' +\n    \'        online:    accumulator.online + (if r._field == \"online\" then r._value else 0),\\n\' +\n    \'        spend:     accumulator.spend + (if r._field == \"spend\" then r._value else 0),\\n\' +\n    \'        free:      accumulator.free + (if r._field == \"free\" then r._value else 0),\\n\' +\n    \'        fault:     accumulator.fault + (if r._field == \"fault\" then r._value else 0),\\n\' +\n    \'        faultCount:accumulator.faultCount + (if r._field == \"faultCount\" then r._value else 0)\\n\' +\n    \'    }),\\n\' +\n    \'    identity: { online: 0, spend: 0, free: 0, fault: 0, faultCount: 0 }\\n\' +\n    \')\\n\' +\n\n    // 计算最终指标\n    \'|> map(fn: (r) => ({\\n\' +\n    \'    deviceId: r.deviceId,\\n\' +\n    \'    onlineTime: r.online,\\n\' +\n    \'    taskSpend: r.spend,\\n\' +\n    \'    freeTime: r.free,\\n\' +\n    \'    faultTime: r.fault,\\n\' +\n    \'    faultCount: r.faultCount,\\n\' +\n    \'    useRate:  if r.online == 0 then 0.0 else float(v: r.spend) / float(v: r.online),\\n\' +\n    \'    faultRate:if r.online == 0 then 0.0 else float(v: r.fault) / float(v: r.online)\\n\' +\n    \'}))\\n\' +\n    \'result\\n\';\n\n\n\n\n// 执行查询并返回结果\nvar r = me.queryData(q);\nreturn r;', 1, 0, 1513746159913050114, '周亮超', '2025-05-29 10:20:28', 1513746159913050114, '周亮超', '2025-05-29 10:20:28', 0, 0, 0, 0, 999);
INSERT INTO `thing_service` (`ID`, `thing_model_id`, `service_name`, `service_type`, `descript`, `override`, `input_data`, `output_data`, `service_code`, `VERSION`, `DELETED`, `CREATOR_ID`, `CREATOR`, `CREATE_TIME`, `UPDATOR_ID`, `UPDATOR`, `UPDATE_TIME`, `ENGINEERING_ID`, `MODULE_ID`, `SPACE_ID`, `async`, `tenant_id`) VALUES (1000000000088145, 999, 'queryDevicesOnLineTimeByWindowO', 0, '查询多设备分组累计在线时长', 0, '[{\"jSON\": true, \"name\": \"begin\", \"isArray\": false, \"dataType\": 14, \"descript\": \"开始时间，如：2025-06-03T14:53:39\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"end\", \"isArray\": false, \"dataType\": 14, \"descript\": \"结束时间，如：2025-06-03T14:53:39\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"every\", \"isArray\": false, \"dataType\": 14, \"descript\": \"分组参数，如3y代表3年\", \"editable\": null, \"dataModelId\": null}, {\"jSON\": true, \"name\": \"deviceIds\", \"isArray\": false, \"dataType\": 14, \"descript\": \"设备id,多个都逗号分隔\", \"editable\": null, \"dataModelId\": null}]', '{\"jSON\": true, \"isArray\": null, \"dataType\": 31, \"descript\": \"\", \"dataModelId\": null, \"outputDataDescript\": \"\"}', 'var ids = input.deviceIds;\nvar idArray = ids.split(\',\');\n\n// var tmpArray = [];\n// for (var i = 0; i < idArray.length; i++) {\n//     tmpArray.push(\'r[\"deviceId\"] == \"\' + idArray[i] + \'\"\');\n// }\n// var idsStr = tmpArray.join(\' or \');\n\nvar idsStr = \'r[\"deviceId\"] =~ /\' + idArray.join(\'|\') + \'/\';\n\nvar q =\n    \'import \"date\"\' + \"\\n\" +\n    \'queryBegin = time(v: \' + input.begin + \'+08:00)\' + \"\\n\" +\n    \'queryEnd = time(v: \' + input.end + \'+08:00)\' + \"\\n\" +\n    \'nowTime = now()\' + \"\\n\" +\n    \'nowOrEnd = if nowTime < queryEnd then nowTime else queryEnd\' + \"\\n\" +\n    \'nowOrBegin = if nowTime < queryBegin then nowTime else queryBegin\' + \"\\n\" +\n\n    \'t0 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: 0, stop: queryBegin)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and ( \' + idsStr + \' ) )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_OnLine\" )\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrBegin  })) \' + \"\\n\" +\n\n    \'t1 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and ( \' + idsStr + \' ) )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_OnLine\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n\n    \'t2 = \' + \"\\n\" +\n    \'from(bucket: \"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and ( \' + idsStr + \' ) )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_OnLine\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrEnd  })) \' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _value: if r[\"_value\"] then false else true })) \' + \"\\n\" +\n\n    \'t3 =\' + \"\\n\" +\n    \'union(tables: [t0, t1, t2])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n\n    \'t4 = t3\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> window(every: \' + input.every + \', offset: -8h)\' + \"\\n\" +\n    \'|> last()\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: date.sub(d: 1s, from: r[\"_stop\"])  }))\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n\n    \'t5 = \' + \"\\n\" +\n    \'union(tables: [t3, t4])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n\n    \'t5\' + \"\\n\" +\n\n    \'|> map(fn: (r) => ({ r with val: int(v: r[\"_value\"])  })) \' + \"\\n\" +\n    \'|> difference(columns: [\"val\"], keepFirst: true)\' + \"\\n\" +\n    \'|> elapsed(unit: 1s, columnName: \"dur\", timeColumn: \"_time\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with type: \' + \"\\n\" +\n    \'        if r.property == \"State_OnLine\" and r.val == 1 then \"offline\"\' + \"\\n\" +\n    \'        else if r.property == \"State_OnLine\" and r.val == -1 then \"online\"\' + \"\\n\" +\n    \'        else if r.property == \"State_OnLine\" and r.val == 0 then \' + \"\\n\" +\n    \'            if r._value then \"online\" else \"offline\"\' + \"\\n\" +\n    \'        else \"unknow\"\' + \"\\n\" +\n    \'    })\' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"dur\", \"type\"])\' + \"\\n\" +\n    \'|> filter(fn: (r) => \' + \"\\n\" +\n    \'    r[\"type\"] == \"online\" \' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> aggregateWindow(every: \' + input.every + \', fn: sum, offset: -8h, createEmpty: false, column: \"dur\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with onlineSumSeconds: r[\"dur\"]/\' + idArray.length + \'}))\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with onlineSumStr: string(v: duration( v: ( r[\"onlineSumSeconds\"]*1000*1000*1000) )) }))\' + \"\\n\" +\n    \'|> drop(columns: [\"_start\",\"_stop\", \"dur\"])\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: date.sub(d: 1s, from: r[\"_time\"])  }))\';\n\n\nvar r = me.queryData(q);\nreturn r;', 1, 0, 1443471710403244034, '郑立凡', '2025-06-12 14:18:01', 1443471710403244034, '郑立凡', '2025-06-12 14:18:01', 0, 0, 0, 0, 999);
