package com.nti56.nlink.product.device.client.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 标签分组表
 * 
 * author: sushangqun
 * create time: 2022-03-15 09:02:13
 */ 
@Data
@Schema(description = "标签分组")
public class LabelGroupDTO implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * id
     */ 
    private Long id;
    /**
     * 所属通道id
     */
    @Schema(description = "所属通道id")
    private Long channelId;
    /**
     * 分组名称
     */
    @Schema(description = "分组名称")
    private String name;
    /**
     * 分组标识,逗号分隔
     */
    @Schema(description = "分组标识,逗号分隔")
    private String tag;
    /**
     * 使用策略id
     */
    @Schema(description = "使用策略id")
    private Long strategyId;

    /**
     * 标签描述
     */
    @Schema(description = "标签描述")
    private String descript;

    @Schema(description = "租户id")
    private Long tenantId;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

}
