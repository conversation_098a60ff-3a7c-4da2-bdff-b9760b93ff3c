package com.nti56.nlink.product.device.client.model.dto.json.custom;
import java.io.Serializable;

import lombok.Data;

@Data
public class SpecialFlagCarJimiElm implements Serializable {
    private static final long serialVersionUID = 1L;
    
    private Boolean enabled; //true
    private String conditionValue1; //"0x7878"
    private Integer lengthType1; //"Byte"
    private String conditionValue2; //"0x7979"
    private Integer lengthType2; //"Word"

}
