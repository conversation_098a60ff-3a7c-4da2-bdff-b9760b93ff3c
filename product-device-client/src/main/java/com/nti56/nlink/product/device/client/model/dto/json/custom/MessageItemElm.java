package com.nti56.nlink.product.device.client.model.dto.json.custom;

import java.io.Serializable;
import java.util.List;

import com.nti56.nlink.product.device.client.model.dto.json.CustomFieldTransformField;

import lombok.Data;

/**
 * 类说明: 报文元素
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-11-04 10:30:34
 * @since JDK 1.8
 */
@Data
public class MessageItemElm implements Serializable {
    private static final long serialVersionUID = 1L;
    
    private String fieldName;
    private String fieldDescript;
    private Integer startByte;
    private Integer startBit;
    private Integer endByte;
    private Integer endBit;
    private String dataType;
    private Integer bytes;
    private String fieldType;
    private String checkType;
    private String constValue;
    private String ipValue;
    private List<String> byteLengthFields;
    private List<String> countFields;
    private List<String> checkFields;
    private List<String> repeatFields;
    private List<DynamicLengthConditionElm> dynamicLengthConditions;
    private CustomFieldTransformField fieldTransform;
}
