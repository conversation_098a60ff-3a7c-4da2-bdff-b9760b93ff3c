package com.nti56.nlink.product.device.client.model.dto.json.enumerate;

import lombok.Getter;

public enum WarningLevelEnum {
//    告警级别 1一般 2告警 3严重 4 致命

    NORMAL_WARNING(1,"normal"),
    ALERT_WARNING(2,"alert"),
    SERIOUS_WARNING(3,"serious"),
    FATAL_WARNING(4,"fatal");

    @Getter
    private Integer code;

    @Getter
    private String typeName;

    WarningLevelEnum(Integer code, String typeName) {
        this.code = code;
        this.typeName = typeName;
    }

    public static WarningLevelEnum typeOfCode(byte code){
        WarningLevelEnum[] values = WarningLevelEnum.values();
        for (WarningLevelEnum v : values) {
            if (v.code == code) {
                return v;
            }
        }
        return null;
    }

}
