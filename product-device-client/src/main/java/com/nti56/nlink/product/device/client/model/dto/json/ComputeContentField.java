package com.nti56.nlink.product.device.client.model.dto.json;

import com.alibaba.fastjson.JSON;
import com.nti56.nlink.common.export.json.AbstractExport;
import com.nti56.nlink.product.device.client.model.dto.json.compute.ComputeChangeItem;
import com.nti56.nlink.product.device.client.model.dto.json.compute.ComputeEventItem;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-11 16:41:42
 * @since JDK 1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ComputeContentField extends AbstractExport implements Serializable {
  private static final long serialVersionUID = 1L;

  //device|rule
  private String computeType; 

  private Long id;

  private String topic;
  
  private List<ComputeChangeItem> changeList;

  private List<ComputeEventItem> faultList;

  private List<ComputeEventItem> triggerList;

  @Override
  public boolean isJSON() {
    return true;
  }

  @Override
  public String toSqlString() {
    if (isJSON()) {
      return  JSON.toJSONString(this);
    }
    return null;
  }
}
