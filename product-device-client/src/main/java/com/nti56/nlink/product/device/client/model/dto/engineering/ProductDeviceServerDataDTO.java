package com.nti56.nlink.product.device.client.model.dto.engineering;

import com.nti56.nlink.product.device.client.model.dto.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/11/3 13:40<br/>
 * @since JDK 1.8
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ProductDeviceServerDataDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private List<ChannelDTO> channelList;

    private List<ChannelParamDTO> channelParamList;

//    private List<ComputeTaskDTO> computeTaskList;

    private List<DataModelDTO> dataModelList;

    private List<DataModelPropertyDTO> dataModelPropertyList;

    private List<DeviceDTO> deviceList;

    private List<DeviceModelInheritDTO> deviceModelInheritList;

    private List<DeviceServiceDTO> deviceServiceList;

    private List<EdgeGatewayDTO> edgeGatewayList;

    private List<LabelBindRelationDTO> labelBindRelationList;

    private List<LabelDTO> labelList;

    private List<LabelGroupDTO> labelGroupList;

    private List<ResourceRelationDTO> resourceRelationList;

    private List<SubscriptionDTO> subscriptionList;

    private List<TagBindRelationDTO> tagBindRelationList;

    private List<ThingModelDTO> thingModelList;

    private List<ThingModelInheritDTO> thingModelInheritList;

    private List<ThingServiceDTO> thingServiceList;

    private List<CustomDriverDTO> customDriverList;
    private List<CustomMessageDTO> customMessageList;
    private List<CustomFieldDTO> customFieldList;
    
    private List<ConnectorDTO> connectorDTOList;
    private List<ConnectorItemDTO> connectorItemDTOList;
    
}
