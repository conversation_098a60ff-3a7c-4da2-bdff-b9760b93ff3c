package com.nti56.nlink.product.device.client.model.dto.json.compute;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ComputeChangeItem implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    private Long channelId;
    private Long labelId;
    private Long deviceId;
    private String property;

    private String labelName;
    private Boolean isArray;
    private String dataType;
    private Integer length;
    private Integer stringBytes;

}
