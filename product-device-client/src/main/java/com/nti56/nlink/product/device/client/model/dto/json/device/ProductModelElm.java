package com.nti56.nlink.product.device.client.model.dto.json.device;

import com.nti56.nlink.product.device.client.model.dto.json.ModelField;
import com.nti56.nlink.product.device.client.model.dto.json.modelfield.ServiceElm;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName ProductModelElm
 * @date 2022/5/13 13:40
 * @Version 1.0
 */
@Schema
@Data
public class ProductModelElm  extends ModelField implements Serializable {

    private static final long serialVersionUID = 1L;
    @Schema(description = "服务")
    private List<ServiceElm> services;

}
