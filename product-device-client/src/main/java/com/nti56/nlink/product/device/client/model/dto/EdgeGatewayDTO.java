package com.nti56.nlink.product.device.client.model.dto;

import com.nti56.nlink.product.device.client.model.dto.json.EdgeGatewayRuntimeInfoField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 边缘网关表
 * 
 * author: sushangqun
 * create time: 2022-03-15 09:02:13
 */ 
@Data
@Schema(description = "网关")
public class EdgeGatewayDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */ 
    private Long id;
    /**
     * 边缘网关名称
     */ 
    private String name;
    /**
     * 描述
     */
    @Schema(description = "描述")
    private String descript;

    @Schema(description = "是否访问公共MQTT接口")
    private Boolean visitPublicMqtt;
    /**
     * 心跳uuid
     */
    @Schema(description = "心跳uuid")
    private String heartbeatUuid;
    /**
     * host
     */
    @Schema(description = "主机")
    private String host;
    /**
     * port
     */
    @Schema(description = "端口")
    private Integer port;

    @Schema(description = "IMEI")
    private String imei;

    @Schema(description = "流量卡号")
    private String trafficCard;

    @Schema(description = "运营商 1-移动，2-电信，3-联通")
    private Integer operators;

    @Schema(description = "网关类型 虚拟网关/网关设备 ")
    private Integer type;

    @Schema(description = "同步时间")
    private LocalDateTime syncTime;

    @Schema(description = "设备同步状态，1-已最新，0-有更新")
    private Integer syncStatus;

    @Schema(description = "租户id")
    private Long tenantId;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    /**
     * 网关运行时信息
     */
    @Schema(description = "网关运行时信息")
    private EdgeGatewayRuntimeInfoField runtimeInfo;
    
    /**
     * 目标版本号
     */
    @Schema(description = "目标版本号")
    private String targetVersion;
    
    /**
     * 升级开始时间
     */
    @Schema(description = "升级开始时间")
    private LocalDateTime upgradeBeginTime;
    
    /**
     * 升级状态
     */
    @Schema(description = "升级状态")
    private Integer upgradeStatus;

    /**
     * 内存监控百分比
     */
    @Schema(description = "网关内存监控百分比")
    private Integer memoryMonitor;

    /**
     * 硬件空间监控百分比
     */
    @Schema(description = "网关空间监控百分比")
    private Integer spaceMonitor;

}
