package com.nti56.nlink.product.device.client.model.dto.json.Upgrade;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 类说明: 升级范围
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023-3-18 11:03:38
 * @since JDK 1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpgradeRangeElm implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    private Long tenantId;
    
    private Long edgeGatewayId;
    
}
