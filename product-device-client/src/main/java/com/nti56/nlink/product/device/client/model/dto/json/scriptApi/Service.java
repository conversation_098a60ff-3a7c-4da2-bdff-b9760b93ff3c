package com.nti56.nlink.product.device.client.model.dto.json.scriptApi;

import com.nti56.nlink.product.device.client.model.dto.json.InputDataField;
import com.nti56.nlink.product.device.client.model.dto.json.OutputDataField;
import lombok.Data;

import java.io.Serializable;

/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName Service
 * @date 2022/4/15 13:04
 * @Version 1.0
 */
@Data
public class Service implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 服务名
     */
    private String serviceName;

    /**
     * 是否是异步
     */
    private Boolean isAsync;

    /**
     * 服务代码
     */
    private String serviceCode;

    /**
     * 入参定义
     */
    private InputDataField[] inputDataDefined;

    /**
     * 出参定义
     */
    private OutputDataField outputDataDefined;


}
