package com.nti56.nlink.product.device.client.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 所属数据模型id 实体类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2022-04-13 14:23:50
 * @since JDK 1.8
 */
@Data

@Schema( description = "数据模型属性表")
public class DataModelPropertyDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    private Long id;

    /**
     * 属性名称，英文数字下划线，英文开头
     */
    @Schema(description = "属性名称，英文数字下划线，英文开头")
    private String name;

    /**
     * 描述
     */
    @Schema(description = "描述")
    private String descript;

    /**
     * 数据类型，1-bool,2-byte,3-short,4-int,5-float,6-string,7-dataModel
     */
    @Schema(description = "数据类型，1-bool,2-byte,3-short,4-int,5-float,6-string,7-dataModel")
    private Integer dataType;

    /**
     * 是否是数组，0-否，1-是
     */
    @Schema(description = "是否是数组，0-否，1-是")
    private Boolean isArray;

    /**
     * 属性数据模型id，dataModel类型才能设置
     */
    @Schema(description = "属性数据模型id，dataModel类型才能设置")
    private Long propertyDataModelId;

    /**
     * 默认值，基础类型才能设置
     */
    @Schema(description = "默认值，基础类型才能设置")
    private String defaultValue;

    /**
     * 所属数据模型id
     */
    @Schema(description = "所属数据模型id")
    private Long dataModelId;

    @Schema(description = "租户id")
    private Long tenantId;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;


}
