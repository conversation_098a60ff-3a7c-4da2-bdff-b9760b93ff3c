package com.nti56.nlink.product.device.client.model.dto.json.modelfield;

import com.nti56.nlink.product.device.client.model.dto.json.InputDataField;
import com.nti56.nlink.product.device.client.model.dto.json.OutputDataField;
import lombok.Data;

import java.io.Serializable;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-02 13:08:07
 * @since JDK 1.8
 */
@Data
public class ServiceElm implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;

    private String name;

    private String descript;

    private Boolean override;

    private Boolean async;

    private InputDataField[] inputData;

    private OutputDataField outputData;

    private String outputDataDescript;

    private String serviceCode;
    
}
