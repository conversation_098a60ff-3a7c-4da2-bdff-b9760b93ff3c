package com.nti56.nlink.product.device.client.model.dto.json.condition;

import lombok.Data;

import java.io.Serializable;

/**
 * 类说明: 条件属性<br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/4/11 15:50<br/>
 * @since JDK 1.8
 */
@Data
public class ConditionProperty implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 设备id
     */
    private String deviceId;
    /**
     * 属性全称
     */
    private String propertyName;
    /**
     * 标签Id
     */
    private String labelId;
    /**
     * 过期时间 ms
     */
    private Integer ttl;
    /**
     * 值
     */
    private Object value;
}

