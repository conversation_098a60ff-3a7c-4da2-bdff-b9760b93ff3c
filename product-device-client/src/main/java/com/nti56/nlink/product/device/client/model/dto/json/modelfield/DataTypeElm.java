package com.nti56.nlink.product.device.client.model.dto.json.modelfield;

import lombok.Data;

import java.io.Serializable;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-02 13:07:41
 * @since JDK 1.8
 */
@Data
public class DataTypeElm implements Serializable {
    private static final long serialVersionUID = 1L;
    private String type;
    private Long dataModelId;
    private Boolean isArray;
    private SpecElm spec;
}
