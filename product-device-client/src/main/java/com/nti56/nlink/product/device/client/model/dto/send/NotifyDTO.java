package com.nti56.nlink.product.device.client.model.dto.send;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/3/8 22:19<br/>
 * @since JDK 1.8
 */
@Data
@Schema(description = "发送通知dto")
public class NotifyDTO {

    @Schema(description = "模板id")
    @NotNull(message = "模板id不能为空")
    private Long templateId;

    @Schema(description = "接收账号，用逗号隔开")
    private String receivers;

    @Schema(description = "订阅ID")
    private Long subscriptionId;

    @Schema(description = "填充参数，json字符串")
    private String params;

}
