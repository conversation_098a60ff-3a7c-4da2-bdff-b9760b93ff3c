package com.nti56.nlink.product.device.client.model.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/3/7 11:19<br/>
 * @since JDK 1.8
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(name = "TagRsp", description = "标记返回对象")
public class TagRsp {


  @Schema(name = "id", description = "id", defaultValue = "1")
  private Long id;
  @Schema(name = "tenantId", description = "租户id", defaultValue = "1")
  private Long tenantId;
  @Schema(name = "spaceId", description = "空间id")
  private Long spaceId;
  @Schema(name = "engineeringId", description = "工程id", defaultValue = "1")
  private Long engineeringId;

  @Schema(name = "moduleId", description = "模块id", defaultValue = "2")
  private Long moduleId;
  @Schema(name = "tagKey", description = "键", defaultValue = "key")
  private String tagKey;
  @Schema(name = "tagValue", description = "值", defaultValue = "value")
  private String tagValue;
  @Schema(name = "creator", description = "创建者", defaultValue = "1")
  private String creator;
  @Schema(name = "createTime", description = "创建时间")
  private LocalDateTime createTime;
  @Schema(name = "updator", description = "变更者")
  private String updator;
  @Schema(name = "updateTime", description = "变更时间")
  private LocalDateTime updateTime;
  @Schema(name = "version", description = "版本号")
  private Integer version;
  @Schema(name = "deleted", description = "删除标记")
  private Byte deleted;

}
