package com.nti56.nlink.product.device.client.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 数据模型表 实体类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2022-04-13 11:49:35
 * @since JDK 1.8
 */
@Data

@Schema( description = "数据模型表")
public class DataModelDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    private Long id;

    /**
     * 数据模型名称
     */
    @Schema(description = "数据模型名称")
    private String name;

    /**
     * 描述
     */
    @Schema(description = "描述")
    private String descript;

    @Schema(description = "租户id")
    private Long tenantId;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

}
