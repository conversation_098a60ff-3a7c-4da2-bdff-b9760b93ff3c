package com.nti56.nlink.product.device.client.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 类说明：自定义协议导出类
 *
 * @ClassName CustomDriverExportDTO
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/1/18 9:47
 * @Version 1.0
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CustomDriverExportDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private CustomDriverDTO customDriverDTO;

    private List<CustomMessageDTO> customMessageDTOS;

    private Map<Long, List<CustomFieldDTO>> customFieldsMap;


}
