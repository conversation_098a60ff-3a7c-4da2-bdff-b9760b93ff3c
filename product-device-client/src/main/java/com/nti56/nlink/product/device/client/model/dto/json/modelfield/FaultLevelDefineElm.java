package com.nti56.nlink.product.device.client.model.dto.json.modelfield;

import com.nti56.nlink.product.device.client.model.dto.json.condition.TriggerConditionElm;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 类说明：
 *
 * @ClassName FaultLevelDefineElm
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/2/8 14:16
 * @Version 1.0
 */
@Data
@Schema
public class FaultLevelDefineElm implements Serializable {

    private static final long serialVersionUID = 1L;

    private Boolean enableDefine;

    //normal alert serious fatal
    private Map<String, List<TriggerConditionElm>> levelDefine;
}
