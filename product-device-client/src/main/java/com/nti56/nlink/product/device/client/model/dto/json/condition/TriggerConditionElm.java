package com.nti56.nlink.product.device.client.model.dto.json.condition;

import com.nti56.nlink.product.device.client.model.dto.json.enumerate.ThingDataTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 类说明: 触发条件<br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/4/8 09:45<br/>
 * @since JDK 1.8
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TriggerConditionElm implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * "type": "compare", //
     *     "level": 1,
     *     "left": {
     *       "deviceId": 123,
     *       "property": "temperature",
     *     },
     *     "operator": ">", //运算符 >, <, <=, >=, !=, ==
     *     "right": {
     *       value: "90", //可以是bool/short/int/float/string类型的数据的字符串
     *     },
     *     "logic": "and", //逻辑符 and, or
     */
    /**
     * 类型，compare-对比{ operator,left,right 不为空}, bracket-括号{bracket }, single-单个{ single}
     */
    private String type;
    /**
     * level
     */
    private Integer level;
    /**
     * 运算符 >, <, <=, >=, !=, ==
     */
    private String operator;
    /**
     * 逻辑符 and, or
     */
    private String logic;

    /**
     * 括号
     */
    private String bracket;

    /**
     *  操作左侧
     */
    private LeftElm left;

    /**
     * 操作右侧
     */
    private RightElm right;

    /**
     * 事件
     */
    private LeftElm single;



    /**
     * 有效时间(单位：）
     */
    private Integer ttl;


    
    //前端冗余字段 
    private Boolean isArray;

    //前端冗余字段 
    private String propteType; 

    //前端冗余字段 
    private Boolean isPropteType;
    
    
    //左边数据类型，后端冗余字段
    private ThingDataTypeEnum propertyType;


    /**
     * 属性值
     */
    private Object value;
}
