package com.nti56.nlink.product.device.client.model.dto.send;

import com.nti56.nlink.common.dto.TenantIsolation;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/4/14 11:50<br/>
 * @since JDK 1.8
 */

@Data
@Schema(description = "批量发送通知dto")
public class BatchSendNotifyDTO {

    @Valid
    @NotEmpty(message = "至少要有一个通知体")
    private List<NotifyDTO> notifyList;

    @Schema(description = "租户隔离信息")
    @NotNull(message = "租户隔离信息不能为空")
    private TenantIsolation tenantIsolation;
}
