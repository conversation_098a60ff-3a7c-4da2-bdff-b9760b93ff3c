package com.nti56.nlink.product.device.client.model.dto.json.enumerate;

import lombok.Getter;

import java.io.Serializable;


/**
 * 类说明: 物模型数据类型，即java端类型
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-24 14:07:13
 * @since JDK 1.8
 */
public enum ThingDataTypeEnum implements Serializable {
    BOOLEAN(1, "Boolean", "布尔型,1", null),
    CHAR(2, "Char", "字符,整型,有符号,8", 1),
    BYTE(3, "Byte", "字节,整型,无符号,8", 1),
    SHORT(4, "Short", "短整型,有符号,16", 2),
    WORD(5, "Word", "字,短整型,无符号,16", 2),
    LONG(6, "Long", "长整型,有符号,32", 4),
    DWORD(7, "DWord", "双字,长整型,无符号,32", 4),
    LLONG(8, "LLong", "大长整型,有符号,64", 8),
    QWORD(9, "QWord", "大长整型,无符号,64", 8),
    FLOAT(10, "Float", "浮点型,有符号,32", 4),
    DOUBLE(11, "Double", "双浮点型,双精度,有符号,64", 8),
    BCD(12, "BCD", "4位二进码十进制,有符号,16", 2),
    LBCD(13, "LBCD", "长4位二进码十进制,有符号,32", 4),
    STRING(14, "String", "字符串", null),
    DATE(15, "Date", "日期", null),
    DATA_MODEL(30, "DataModel", "数据模型", null),
    VOID(31, "Void", "无", null)
    ;
    private static final long serialVersionUID = 1L;
    @Getter
    private Integer value;

    @Getter
    private String name;

    @Getter
    private String nameDesc;

    @Getter
    private Integer bytes;

    ThingDataTypeEnum(Integer value, String name, String nameDesc, Integer bytes) {
        this.value = value;
        this.name = name;
        this.nameDesc = nameDesc;
        this.bytes = bytes;
    }

    public static ThingDataTypeEnum typeOfValue(Integer value){
        ThingDataTypeEnum[] values = ThingDataTypeEnum.values();
        for (ThingDataTypeEnum v : values) {
            if (v.value.equals(value)) {
                return v;
            }
        }
        return null;
    }

    public static ThingDataTypeEnum typeOfName(String name){
        ThingDataTypeEnum[] values = ThingDataTypeEnum.values();
        for (ThingDataTypeEnum v : values) {
            if (v.name.equals(name)) {
                return v;
            }
        }
        return null;
    }

    public static ThingDataTypeEnum typeOfNameDesc(String nameDesc){
        ThingDataTypeEnum[] values = ThingDataTypeEnum.values();
        for (ThingDataTypeEnum v : values) {
            if (v.nameDesc.equals(nameDesc)) {
                return v;
            }
        }
        return null;
    }

    @Override
    public String toString(){
        return this.name;
    }
}
