package com.nti56.nlink.product.device.client.feign.fallback;

import com.alibaba.fastjson.JSON;
import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.R;
import com.nti56.nlink.product.device.client.model.req.ListTagReq;
import com.nti56.nlink.product.device.client.model.req.TagReq;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/3/31 18:48<br/>
 * @since JDK 1.8
 */
//@Slf4j
//public class FeignTagServiceFallBackFactory implements FallbackFactory<IFeignTagService> {
//  @Override
//  public IFeignTagService create(Throwable cause) {
//    return new IFeignTagService() {
//      @Override
//      public R add( TagReq req) {
//        log.error("feign add tag error {}",
//                JSON.toJSONString(req));
//        return R.error("新增标记失败");
//      }
//
//      @Override
//      public R list( ListTagReq req) {
//        log.error("feign list tag error {}",
//                JSON.toJSONString(req));
//        return R.error("获取标记列表失败");
//      }
//
//      @Override
//      public R listByIds( List<Long> ids) {
//        log.error("feign listByIds tag error {}",
//                JSON.toJSONString(ids));
//        return R.error("根据ids标记列表失败");
//      }
//
//      @Override
//      public R export(TenantIsolation tenantIsolation) {
//        return R.error("导出失败");
//      }
//
//    };
//  }
//}
