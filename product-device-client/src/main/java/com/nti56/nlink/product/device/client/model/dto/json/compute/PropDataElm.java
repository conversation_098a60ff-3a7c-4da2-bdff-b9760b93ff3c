package com.nti56.nlink.product.device.client.model.dto.json.compute;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-05-26 09:02:24
 * @since JDK 1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PropDataElm implements Serializable {
    private static final long serialVersionUID = 1L;
    
    private String property;
    private Long labelId;
    
    private String dataType;
    private Boolean isArray;
    private Integer length;
    private Integer stringBytes;
}
