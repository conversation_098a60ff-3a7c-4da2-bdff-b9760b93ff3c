package com.nti56.nlink.product.device.client.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 通道参数表
 * 
 * author: sushangqun
 * create time: 2022-03-15 09:02:13
 */ 
@Data
@Schema(description = "通道参数对象")
public class ChannelParamDTO  implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */ 
    private Long id;

    /**
     * 所属通道id
     */
    @Schema(description = "通道ID")
    private Long channelId;

    /**
     * 通道参数名称，如ip/port/rack/slot等
     */
    @Schema(description = "参数名称")
    private String name;

    /**
     * 描述
     */
    @Schema(description = "描述")
    private String descript;

    /**
     * 通道参数值
     */
    @Schema(description = "通道参数值")
    private String value;

    @Schema(description = "是否必须")
    private Boolean necessary;

    @Schema(description = "租户id")
    private Long tenantId;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

}
