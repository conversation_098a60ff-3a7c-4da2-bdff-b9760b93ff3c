package com.nti56.nlink.product.device.client.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 类说明: 连接器详情项DTO
 * <AUTHOR>
 * @version 1.0
 * @since JDK 1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "连接器详情项")
public class ConnectorItemDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    
    /**
     * id
     */
    private Long id;
    
    /**
     * 连接器名称
     */
    @Schema(description = "连接器名称")
    private String name;
    
    /**
     * topic
     */
    @Schema(description = "topic")
    private String topic;
    
    /**
     * 连接器描述
     */
    @Schema(description = "连接器描述")
    private String descript;
    
    /**
     * 处理代码
     */
    @Schema(description = "处理代码")
    private String processCode;
    
    /**
     * 连接器id
     */
    @Schema(description = "连接器id")
    private Long connectorId;
    
    /**
     * 租户ID
     */
    @Schema(description = "租户ID")
    private Long tenantId;
    
    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}
