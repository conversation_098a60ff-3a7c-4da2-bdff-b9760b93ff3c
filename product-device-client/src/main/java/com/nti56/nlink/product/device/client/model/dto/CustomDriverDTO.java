package com.nti56.nlink.product.device.client.model.dto;

import com.nti56.nlink.product.device.client.model.dto.json.CustomDriverRuntimeInfoField;

import com.nti56.nlink.product.device.client.model.dto.json.CustomDriverSpecialConfigField;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class CustomDriverDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;

    private String driverName;

    private String descript;

    private Integer status;

    private Integer formatType;

    private Integer endian;

    private String readLengthFields;

    private CustomDriverRuntimeInfoField runtimeInfo;

    private LocalDateTime createTime;
    private LocalDateTime updateTime;

    private Long tenantId;

    private Integer extraLength;

    private CustomDriverSpecialConfigField specialConfig;

    private List<CustomFieldDTO> fixEntityList;
}
