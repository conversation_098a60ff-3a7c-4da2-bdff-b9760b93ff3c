package com.nti56.nlink.product.device.client.model.dto.json.custom;

import lombok.Data;
import java.io.Serializable;

@Data
public class DynamicLengthConditionElm implements Serializable {
    private static final long serialVersionUID = 1L;

    private String conditionField;
    private String conditionValue;
    private String resultDataType;
    private Integer resultBytes;

    public DynamicLengthConditionElm(){
        
    }

    public DynamicLengthConditionElm(
        String conditionField, 
        String conditionValue, 
        String resultDataType,
        Integer resultBytes
    ) {
        this.conditionField = conditionField;
        this.conditionValue = conditionValue;
        this.resultDataType = resultDataType;
        this.resultBytes = resultBytes;
    }

    
}
