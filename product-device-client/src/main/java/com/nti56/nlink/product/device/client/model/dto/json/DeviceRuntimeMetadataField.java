package com.nti56.nlink.product.device.client.model.dto.json;

import com.alibaba.fastjson.JSON;
import com.nti56.nlink.common.export.json.AbstractExport;
import com.nti56.nlink.product.device.client.model.dto.json.device.PropertyMetadataItem;
import com.nti56.nlink.product.device.client.model.dto.json.dpo.EventDpo;
import com.nti56.nlink.product.device.client.model.dto.json.dpo.SubscriptionDpo;
import com.nti56.nlink.product.device.client.model.dto.json.scriptApi.Service;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-05-26 09:02:59
 * @since JDK 1.8
 */
@Data
public class DeviceRuntimeMetadataField extends AbstractExport implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "属性")
    private Map<String, PropertyMetadataItem> properties;

    @Schema(description = "服务")
    private Map<String, Service> services;

    @Schema(description = "事件")
    private List<EventDpo> events;

    @Schema(description = "订阅")
    private Map<String, SubscriptionDpo> subscriptions;

    private Long id;

    private String deviceName;

    private Long edgeGatewayId;

    private String edgeGatewayName;

    private String resourceId;

    private Long tenantId;

    @Override
    public boolean isJSON() {
        return true;
    }

    @Override
    public String toSqlString() {
        if (isJSON()) {
            return  JSON.toJSONString(this);
        }
        return null;
    }
}
