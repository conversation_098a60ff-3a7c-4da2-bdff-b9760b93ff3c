package com.nti56.nlink.product.device.client.model.dto.json.modelfield;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-02 13:07:46
 * @since JDK 1.8
 */
@Data
@Schema
public class EventElm implements Serializable {
    private static final long serialVersionUID = 1L;
    @Schema(description = "主键")
    private Integer id;
    @Schema(description = "物模型名称")
    private String name;
    @Schema(description = "是否是必选事件")
    private Boolean required;

    @Schema(description = "事件方法，根据id生成，作为ot向gw订阅事件的主题topic")
    private String method;
    @Schema(description = "事件类型")
    private String type;
    @Schema(description = "事件定义")
    private EventDefineElm eventDefine;
    @Schema(description = "事件描述")
    private String descript;
    @Schema(description = "调用服务id")
    private Long thingServiceId;

  /*  @Schema(description = "事件类型 0-普通事件 1-fault事件")
    private Long eventType;*/


}
