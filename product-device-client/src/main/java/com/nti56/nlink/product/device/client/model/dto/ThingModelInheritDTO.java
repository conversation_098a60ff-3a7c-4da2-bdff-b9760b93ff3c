package com.nti56.nlink.product.device.client.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 物模型继承表 实体类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2022-04-13 15:37:55
 * @since JDK 1.8
 */
@Data
@Schema( description = "物模型继承表")
public class ThingModelInheritDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    private Long id;

    /**
     * 物模型id
     */
    @Schema(description = "物模型id")
    private Long thingModelId;

    /**
     * 继承的物模型id
     */
    @Schema(description = "继承的物模型id")
    private Long inheritThingModelId;

    /**
     * 继承顺序，从小到大排列，后面模型的属性名、事件名不能跟前面的同名，服务允许覆盖才允许同名
     */
    @Schema(description = "继承顺序，从小到大排列，后面模型的属性名、事件名不能跟前面的同名，服务允许覆盖才允许同名")
    private Integer sortNo;

    @Schema(description = "租户id")
    private Long tenantId;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

}
