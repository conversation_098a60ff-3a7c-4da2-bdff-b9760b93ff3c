package com.nti56.nlink.product.device.client.model.dto;

import com.nti56.nlink.product.device.client.model.dto.json.ModelField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 类说明: 物模型表
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 14:08:44
 * @since JDK 1.8
 */
@Data
public class ThingModelDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @Schema(description = "物模型主键ID")
    private Long id;
    /**
     * 物模型名称，如：灯、风扇
     */
    @Schema(description = "物模型名称")
    private String name;
    /**
     * 物模型定义
     */
    @Schema(description = "物模型定义")
    private ModelField model;

    @Schema(description = "模型类型")
    private Integer modelType;

    /**
     * 描述
     */
    private String descript;

    @Schema(description = "租户id")
    private Long tenantId;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

  /*  @Schema(description = "版本号")
    private Integer version;

    @Schema(description = "逻辑删除，1-删除")
    private Boolean deleted;*/

}
