package com.nti56.nlink.product.device.client.feign;

import com.nti56.nlink.common.dto.TenantIsolation;
import com.nti56.nlink.common.util.R;
import com.nti56.nlink.product.device.client.model.req.ListTagReq;
import com.nti56.nlink.product.device.client.model.req.TagReq;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/3/31 18:42<br/>
 * @since JDK 1.8
 */
//@FeignClient(name ="${nlink.common.serverName:product-device-server}", contextId = "tagService",
//        fallbackFactory = FeignTagServiceFallBackFactory.class,
//        decode404 = true)
//public interface IFeignTagService {
//
//    /**
//     * 新增标记
//     * @param req
//     * @return
//     */
//    @PostMapping(value="tags", produces = MediaType.APPLICATION_JSON_VALUE)
//    R add(@RequestBody TagReq req);
//
//    /**
//     * 查询标记
//     * @param req
//     * @return
//     */
//    @GetMapping(value = "tags/lists", produces = MediaType.APPLICATION_JSON_VALUE)
//    R list(@RequestParam("req") ListTagReq req);
//
//    /**
//     * 根据标记id获取标记列表
//     * @param ids
//     * @return
//     */
//    @PostMapping(value="tags/lists/ids",produces = MediaType.APPLICATION_JSON_VALUE)
//    R listByIds(@RequestBody List<Long> ids);
//
//    /**
//     *
//     * @param tenantIsolation
//     * @return
//     */
//    @PostMapping(value = "export/common", produces = MediaType.APPLICATION_JSON_VALUE)
//    R export(@RequestBody TenantIsolation tenantIsolation);
//}
