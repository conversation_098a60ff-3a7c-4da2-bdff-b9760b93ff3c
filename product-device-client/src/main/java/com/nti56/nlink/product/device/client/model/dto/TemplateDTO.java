package com.nti56.nlink.product.device.client.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 类说明: 模板表<br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/11/4 13:09<br/>
 * @since JDK 1.8
 */
@Data
@Schema(description = "模板对象")
public class TemplateDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @Schema(description = "模板名称")
    private String name;

    @Schema(description = "描述")
    private String description;

    /**
     * 模板标题
     */
    @Schema(description = "模板标题")
    private String title;

    /**
     * 通知类型 0邮件 1短信
     */
    @Schema(description = "通知类型 0邮件 1短信")
    private Integer notifyType;

    /**
     * 模板内容
     */
    @Schema(description = "模板内容")
    private String content;

    /**
     * 模板code，短信需要
     */
    @Schema(description = "模板code，短信需要")
    private String templateCode;

    /**
     * 模板状态 0审核中 1审核通过 2审核未通过
     */
    @Schema(description = "模板状态 0审核中 1审核通过 2审核未通过")
    private Integer auditStatus;

    @Schema(description = "审核提示")
    private String auditPrompt;

    /**
     * 模板绑定的ChannelId
     */
    @Schema(description = "模板绑定的ChannelId")
    private Long notifyChannelId;



    @Schema(description = "申请模板理由")
    private String reason;

    /**
     * 逻辑删除，1-删除
     */
    @Schema(description = "逻辑删除，1-删除")
    private Boolean deleted;

    @Schema(description = "租户ID")
    private Long tenantId;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

}
