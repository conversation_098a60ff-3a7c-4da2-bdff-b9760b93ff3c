package com.nti56.nlink.product.device.client.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 设备模型继承表 实体类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2022-04-27 19:24:57
 * @since JDK 1.8
 */
@Data

public class DeviceModelInheritDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * id
     */

    private Long id;

    /**
     * 设备id
     */
    private Long deviceId;

    /**
     * 继承的物模型id
     */
    private Long inheritThingModelId;

    /**
     * 继承顺序
     */
    private Integer sortNo;

    @Schema(description = "租户id")
    private Long tenantId;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

}
