package com.nti56.nlink.product.device.client.model.dto.json.dpo;

import lombok.Data;

import java.io.Serializable;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-05-26 08:58:38
 * @since JDK 1.8
 */
@Data
public class EventDpo implements Serializable {
    private static final long serialVersionUID = 1L;
    private Long thingModelId;

    private String thingModelName;
    
    private String name;
    private String type;
    private String descript;
    private EventDefineDpo eventDefine;

    private Long thingServiceId;

    private Long eventId;

}
