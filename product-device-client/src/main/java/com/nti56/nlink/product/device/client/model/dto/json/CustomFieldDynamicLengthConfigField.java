package com.nti56.nlink.product.device.client.model.dto.json;

import java.io.Serializable;
import java.util.List;
import com.alibaba.fastjson.JSON;

import com.nti56.nlink.common.export.json.AbstractExport;
import com.nti56.nlink.product.device.client.model.dto.json.custom.ConfigDynamicLengthConditionElm;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023-02-02 13:55:45
 * @since JDK 1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomFieldDynamicLengthConfigField extends AbstractExport implements Serializable  {
    private static final long serialVersionUID = 1L;
  
    private List<ConfigDynamicLengthConditionElm> dynamicLengthConditions;
    
    @Override
    public boolean isJSON() {
        return true;
    }

    @Override
    public String toSqlString() {
        if (isJSON()) {
        return  JSON.toJSONString(this);
        }
        return null;
    }

}
