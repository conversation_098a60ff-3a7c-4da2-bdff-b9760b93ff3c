package com.nti56.nlink.product.device.client.model.dto.json.modelfield;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-02 13:07:54
 * @since JDK 1.8
 */
@Data
@Schema
public class PropertyElm implements Serializable {
    private static final long serialVersionUID = 1L;
    @Schema(description = "主键")
    private Integer id;
    @Schema(description = "属性名称")
    private String name;
    @Schema(description = "是否只读")
    private Boolean readOnly;
    @Schema(description = "是否持久化")
    private Boolean persist;
    @Schema(description = "r/w/rw, //属性读写类型，只读(r)，只写(w)，读写(rw)")
    private String access;
    @Schema(description = "是否是必选属性")
    private Boolean required;
    @Schema(description = "是否绑定标签")
    private Boolean bindLabel;
    @Schema(description = "属性类型")
    private DataTypeElm dataType;
    @Schema(description = "上报类型")
    private Integer reportType;
    @Schema(description = "属性描述")
    private String descript;

}
