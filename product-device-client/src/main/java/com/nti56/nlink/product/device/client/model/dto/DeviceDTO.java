package com.nti56.nlink.product.device.client.model.dto;

import com.nti56.nlink.product.device.client.model.dto.json.ModelField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * 类说明: 设备表
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 14:08:00
 * @since JDK 1.8
 */
@Data
@Schema(description = "设备对象")
public class DeviceDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */ 
    private Long id;
    /**
     * 设备名称
     */ 
    private String name;
    /**
     * 设备模型定义
     */
    @Schema(description = "设备模型定义")
    private ModelField model;
    /**
     * 描述
     */ 
    private String descript;

    /**
     * 网关设备id，当节点类型是网关子设备的时候，其连接的网关设备id，其他节点类型为null
     */ 
    @Schema(description = "所属网关id")
    private Long edgeGatewayId;

    /**
     * 设备状态，2-已离线， 1-已上线，0-未激活
     */
    @Schema(description = "设备状态，2-已上线， 1-已离线，0-未激活")
    private Integer status;


    @Schema(description = "设备同步状态，1-已最新，0-有更新")
    private Integer syncStatus;

    @Schema(description = "最后同步时间")
    private LocalDateTime lastSyncTime;

    @Schema(description = "激活时间")
    private LocalDateTime activationTime;

    @Schema(description = "模型最后变动时间")
    private LocalDateTime modelUpdateTime;

    @Schema(description = "设备绑定的分组")
    private String source;

    @Schema(description = "设备绑定的通道")
    private String channel;

    @Schema(description = "租户id")
    private Long tenantId;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private String resourceId;

    /*@Schema(description = "版本号")
    private Integer version;

    @Schema(description = "逻辑删除，1-删除")
    private Boolean deleted;*/
}
