package com.nti56.nlink.product.device.client.model.dto;

import com.nti56.nlink.product.device.client.model.dto.json.ComputeContentField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 计算任务表
 * 
 * author: sushangqun
 * create time: 2022-03-15 09:02:13
 */
//todo:gs多对多
@Data
public class ComputeTaskDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */ 
    private Long id;
    /**
     * 所属网关id
     */ 
    private Long edgeGatewayId;
    
    /**
     * 设备id
     */ 
    private Long deviceId;
    
    /**
     * 事件名
     */ 
    private String eventName;
    
    /**
     * 任务名称
     */ 
    private String name;
    /**
     * 任务内容
     */ 
    private ComputeContentField content;
    /**
     * 启用状态，0-关闭，1-启用
     */ 
    private Integer enableStatus;

    @Schema(description = "租户id")
    private Long tenantId;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

}
