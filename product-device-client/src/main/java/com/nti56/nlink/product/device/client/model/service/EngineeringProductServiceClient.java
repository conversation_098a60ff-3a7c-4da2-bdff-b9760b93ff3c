package com.nti56.nlink.product.device.client.model.service;

import com.nti56.nlink.common.util.R;
import com.nti56.nlink.product.device.client.model.dto.engineering.ProductDeviceServerDataDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/11/4 9:03<br/>
 * @since JDK 1.8
 */
//@FeignClient(name = "product-device-server")
//public interface EngineeringProductServiceClient {
//    String API_PATH = "engineering/product";
//
//    /**
//     * 根据租户删除product服务数据
//     * @return
//     */
//    @DeleteMapping(API_PATH+"/{tenantId}")
//    R deleteProductDataByTenantId(@PathVariable("tenantId") Long tenantId);
//
//    /**
//     * 根据租户查询product服务数据
//     * @return
//     */
//    @GetMapping(API_PATH+"/{tenantId}")
//    R getProductDataByTenantId(@PathVariable("tenantId") Long tenantId);
//
//
//    /**
//     * 新增product数据
//     * @return
//     */
//    @PostMapping(API_PATH)
//    R createProductData(@RequestBody ProductDeviceServerDataDTO dto);
//
//    /**
//     * 新增product数据
//     * @return
//     */
//    @PostMapping(API_PATH+"/{tenantId}")
//    R initProductData(@RequestBody ProductDeviceServerDataDTO dto,@PathVariable("tenantId") Long tenantId);
//}
