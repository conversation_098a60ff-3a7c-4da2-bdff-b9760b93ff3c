package com.nti56.nlink.product.device.client.model.dto.json;

import com.alibaba.fastjson.JSON;
import com.nti56.nlink.common.export.json.AbstractExport;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @ClassName OutputData
 * @date 2022/4/12 16:52
 * @Version 1.0
 */
@Data
@Schema
public class InputDataField extends AbstractExport implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "属性名")
    private String name;

    @Schema(description = "数据类型，bool/byte/short/int/float/string/dataModel/ushort/double/uint")
    private Integer dataType;

    @Schema(description = "是否是数组，true/false")
    private Boolean isArray;

    @Schema(description = "属性描述")
    private String descript;

    @Schema(description = "如果dataType是dataModel类型需要关联dataModelId")
    private Long dataModelId;

    @Schema(description = "入参是否可编辑")
    private Boolean editable;

    @Override
    public String toSqlString() {
        if (isJSON()) {
            return  JSON.toJSONString(this);
        }
        return null;
    }


    @Override
    public boolean isJSON() {
        return true;
    }
}
