package com.nti56.nlink.product.device.client.model.dto.json.dpo;

import com.nti56.nlink.product.device.client.model.dto.json.condition.TriggerConditionElm;
import com.nti56.nlink.product.device.client.model.dto.json.modelfield.FaultLevelDefineElm;
import com.nti56.nlink.product.device.client.model.dto.json.modelfield.PropertyInputMapElm;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-05-26 08:58:32
 * @since JDK 1.8
 */
@Data
public class EventDefineDpo implements Serializable {
    private static final long serialVersionUID = 1L;
    private List<TriggerConditionElm> trigger;
    private List<String> properties;
    private String property;

    private Integer faultBeginThreshold; //故障开始阈值
    private Integer faultEndThreshold; //故障结束阈值
    private Map<String, String> faultInputMap;
    private Long thingServiceId;
    private List<PropertyInputMapElm> faultInput;
    private FaultLevelDefineElm faultLevelDefine;

}
