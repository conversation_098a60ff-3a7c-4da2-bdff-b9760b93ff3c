package com.nti56.nlink.product.device.client.model.service.fallback;

import com.nti56.nlink.common.util.R;
import com.nti56.nlink.product.device.client.model.dto.engineering.ProductDeviceServerDataDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/4/20 10:25<br/>
 * @since JDK 1.8
 */
//@Slf4j
//public class EngineeringProductServiceFallbackFactory implements FallbackFactory<EngineeringProductServiceClient> {
//
//    @Override
//    public EngineeringProductServiceClient create(Throwable throwable) {
//        return new EngineeringProductServiceClient() {
//            @Override
//            public R deleteProductDataByTenantId(Long tenantId) {
//                return  R.error("删除失败");
//            }
//
//            @Override
//            public R getProductDataByTenantId(Long tenantId) {
//                return  R.error("获取失败");
//            }
//
//            @Override
//            public R createProductData(ProductDeviceServerDataDTO dto) {
//                return  R.error("新增失败");
//            }
//
//            @Override
//            public R initProductData(ProductDeviceServerDataDTO dto, Long tenantId) {
//                return  R.error("初始化失败");
//            }
//
//        };
//    }
//}
