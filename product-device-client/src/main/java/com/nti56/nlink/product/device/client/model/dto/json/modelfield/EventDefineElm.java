package com.nti56.nlink.product.device.client.model.dto.json.modelfield;

import com.nti56.nlink.product.device.client.model.dto.json.condition.TriggerConditionElm;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-02 13:07:30
 * @since JDK 1.8
 */
@Data
public class EventDefineElm implements Serializable {
    private static final long serialVersionUID = 1L;

    //event.type == trigger的时候，使用properties
    private List<String> properties;
    
    //event.type == change的时候，使用property
    private String property;

    private List<TriggerConditionElm> trigger;
    
    private Integer faultBeginThreshold; //故障开始阈值
    private Integer faultEndThreshold; //故障结束阈值
    
    private String topic;

    /**
     * 故障参数映射
     */
    private List<PropertyInputMapElm> faultInput;

    private Long thingServiceId;

    /**
     * 等级条件
     */
    private FaultLevelDefineElm faultLevelDefine;
}
