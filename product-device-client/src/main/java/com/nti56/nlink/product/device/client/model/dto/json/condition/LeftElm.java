package com.nti56.nlink.product.device.client.model.dto.json.condition;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/4/8 10:16<br/>
 * @since JDK 1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LeftElm implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 标签id
     */
    private Long labelId;
    /**
     * 设备id
     */
    private Long deviceId;
    /**
     * 属性
     */
    private String property;
    /**
     * 事件
     */
    private String event;
}
