package com.nti56.nlink.product.device.client.model.dto.json;

import com.alibaba.fastjson.JSON;
import com.nti56.nlink.common.export.json.AbstractExport;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-05-26 09:02:50
 * @since JDK 1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChannelRuntimeInfoField extends AbstractExport implements Serializable {
  private static final long serialVersionUID = 1L;
  private Long channelId;
  private Boolean isServer;
  private String channelKey;
  private String driver;
  private String customDriverName;
  private String customDriverMode;
  private String ip;
  private Integer port;
  private Integer rack;
  private Integer slot;
  private String spec;
  private Integer maxVars;
  private Integer reconnectGapMs;
  private Integer maxConnection;
  private Integer delayIdleMs;
  private String userName;
  private String password;
  private Integer slaveId;
  private Integer retryCount;
  private Integer queueTimeout;
  private Boolean debug;

  private Integer comId;
  private Integer baudRate;
  private Integer dataBits;
  private Integer stopBits;
  private Integer parity;

  private Integer deviceId;

  private String endianness;
  private String firstAddress;

  private String targetNetId;
  private String senderNetId;

  private Integer commonAddress;

  private Integer unitNo;

  private Integer station;

  private String meterAddress;

  private String url;
  private String method;
  private String header;
  private String body;

  @Override
  public boolean isJSON() {
    return true;
  }

  @Override
  public String toSqlString() {
    if (isJSON()) {
      return  JSON.toJSONString(this);
    }
    return null;
  }


}
