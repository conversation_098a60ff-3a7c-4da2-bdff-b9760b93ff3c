package com.nti56.nlink.product.device.client.model.dto.json;

import com.alibaba.fastjson.JSON;
import com.nti56.nlink.common.export.json.AbstractExport;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-05-26 09:21:29
 * @since JDK 1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GatherParamField extends AbstractExport implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long labelId;
    private String labelName; //参数名
    private String paramType; //参数类型
    private Boolean isArray; //参数是否是数组
    private String address;
    private Integer length;
    private Integer stringBytes;

    private Long channelId;
    
    private Integer interval; //ms

    private Long edgeGatewayId;


    @Override
    public boolean isJSON() {
        return true;
    }

    @Override
    public String toSqlString() {
        if (isJSON()) {
            return  JSON.toJSONString(this);
        }
        return null;
    }
}
