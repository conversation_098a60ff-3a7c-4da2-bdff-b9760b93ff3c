package com.nti56.nlink.product.device.client.model.dto.json.modelfield;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 类说明:
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-02 13:07:54
 * @since JDK 1.8
 */
@Data
@Schema
public class PropertyInputMapElm implements Serializable {

    @Schema(description = "服务入参名")
    private String input;

    @Schema(description = "设备属性名")
    private String property;

    @Schema(description = "类型")
    private Integer inputDataType;

}
