package com.nti56.nlink.product.device.client.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/3/1 13:01<br/>
 * @since JDK 1.8
 */
@Data
@Schema(name = "TagReq", description = "标记请求对象")
public class TagReq {


  /**
   * 空间
   */
  @Schema(name = "spaceId", description = "空间id", hidden = true, defaultValue = "2")
  private Long spaceId;
  /**
   * 工程
   */
  @Schema(name = "engineeringId", description = "工程id", hidden = true, defaultValue = "2")
  private Long engineeringId;


  @Schema(name = "moduleId", description = "模块id", hidden = true, defaultValue = "2")
  private Long moduleId;

  @Schema(name = "tenantId", description = "租户id", hidden = true, defaultValue = "2")
  private Long tenantId;

  /**
   *
   */
  @NotNull(message = "标记key为空")
  @Length( max = 32, message = "标记key超过定义长度")
  @Schema(name = "tagKey", description = "键", defaultValue = "key")
  private String tagKey;
  /**
   *
   */
  @NotNull(message = "标记value为空")
  @Length( max = 32, message = "标记value超过定义长度")
  @Schema(name = "tagValue", description = "值", defaultValue = "value")
  private String tagValue;


}
