package com.nti56.nlink.product.device.client.model.dto.json;

import com.alibaba.fastjson.JSON;
import com.nti56.nlink.common.export.json.AbstractExport;
import com.nti56.nlink.product.device.client.model.dto.json.custom.CopyFieldElm;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AutoResponseConfigField extends AbstractExport implements Serializable {
    private static final long serialVersionUID = 1L;
    
    private List<CopyFieldElm> copyFieldList;
    
    private String expression;

    @Override
    public boolean isJSON() {
      return true;
    }
  
    @Override
    public String toSqlString() {
      if (isJSON()) {
        return  JSON.toJSONString(this);
      }
      return null;
    }
}
