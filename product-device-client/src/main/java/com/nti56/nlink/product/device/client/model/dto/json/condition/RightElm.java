package com.nti56.nlink.product.device.client.model.dto.json.condition;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/4/8 10:17<br/>
 * @since JDK 1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RightElm implements Serializable {
    private static final long serialVersionUID = 1L;
    private Integer idx;
    private String value;
}
