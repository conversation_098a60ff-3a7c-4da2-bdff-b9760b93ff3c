package com.nti56.nlink.product.device.client.model.dto;

import com.nti56.nlink.product.device.client.model.dto.json.ChannelRuntimeInfoField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 类说明: 通道表
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-18 17:40:01
 * @since JDK 1.8
 */
@Data
@Schema(description = "通道对象")
public class ChannelDTO implements Serializable {
    private static final long serialVersionUID = 1L;


    private Long id;

    @Schema(description = "通道名字")
    private String name;

    @Schema(description = "所属驱动类型，1-Snap7，2-Modbus，3-OPC UA，4-ZigBee，5-BLE")
    private Integer driver;

    @Schema(description = "自定义协议名称")
    private String customDriverName;

    @Schema(description = "描述")
    private String descript;

    @Schema(description = "边缘网关")
    private Long edgeGatewayId;

    @Schema(description = "时间间隔，单位毫秒")
    private Integer intervalMs;

    @Schema(description = "通道状态：0-停用，1-启用")
    private Integer status;

    @Schema(description = "通道运行时信息")
    private ChannelRuntimeInfoField runtimeInfo;

    @Schema(description = "md5校对")
    private String md5Proofread;

    @Schema(description = "租户id")
    private Long tenantId;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

}
