package com.nti56.nlink.product.device.client.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/3/1 13:01<br/>
 * @since JDK 1.8
 */
@Data
@Schema(name = "ListTagReq" ,description = "标记查询")
public class ListTagReq {


    /**
     * 空间
     */
    @Schema(name = "spaceId" ,description = "空间id",defaultValue = "2")
    private Long spaceId;
    /**
     * 工程
     */
    @NotNull(message = "工程id为空")
    @Schema(name = "engineeringId" ,description = "工程id",defaultValue = "2")
    private Long engineeringId;

    @NotNull(message = "模块id为空")
    @Schema(name = "moduleId" ,description = "模块id",defaultValue="2")
    private Long moduleId;

    /**
     *
     */
    @Schema(name = "search" ,description = "键或值",defaultValue = "key")
    private String search;



}
