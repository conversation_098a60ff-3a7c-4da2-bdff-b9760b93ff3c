package com.nti56.nlink.product.device.client.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 标签绑定关系表 实体类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2022-04-11 17:27:49
 * @since JDK 1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LabelBindRelationDTO  implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 关联的直属的物模型 方便物模型更改时查找影响
     */
    @Schema(description = "关联的直属的模型 方便物模型更改时查找影响")
    private Long directlyModelId;

    @Schema(description = "属性直属模型类型")
    private Integer modelType;

    /**
     * 绑定标签
     */
    @Schema(description = "绑定标签")
    private Long labelId;

    /**
     * 所属设备
     */
    @Schema(description = "所属设备")
    private Long deviceId;

    /**
     * 属性名，应该是带层级的，可以直接匹配的
     */
    @Schema(description = "属性名，应该是带层级的，可以直接匹配的")
    private String propertyName;


    /**
     * 关联的直属数据模型 方便数据模型更改是查找影响（如果属性是数据模型里的）
     */
    @Schema(description = "关联的直属数据模型 方便数据模型更改是查找影响（如果属性是数据模型里的）")
    private Long dataModelId;

    @Schema(description = "租户id")
    private Long tenantId;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    @Schema(description = "创建人ID")
    private Long creatorId;

    @Schema(description = "创建人")
    private String creator;

    @Schema(description = "属性绑定的数据来源完整路径，labelGroupName")
    private String labelGroupName;

    @Schema(description = "属性绑定的数据来源完整路径，channelName")
    private String channelName;
    
    @Schema(description = "属性绑定的标签名称，labelName")
    private String labelName;

    @Schema(description = "标签所属网关id")
    private Long edgeGatewayId;

}
