package com.nti56.nlink.product.device.client.model.dto;


import com.alibaba.fastjson.JSON;
import com.nti56.nlink.product.device.client.model.dto.json.InputDataField;
import com.nti56.nlink.product.device.client.model.dto.json.OutputDataField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <p>
 * 物服务表 实体类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2022-04-13 10:28:52
 * @since JDK 1.8
 */
@Data
@Schema(description = "ThingServiceEntity对象")
public class ThingServiceDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 所属物模型ID
     */
    @Schema(description = "所属物模型ID")
    private Long thingModelId;

    /**
     * 服务名称
     */
    @Schema(description = "服务名称")
    private String serviceName;

    /**
     * 描述
     */
    @Schema(description = "描述")
    private String descript;

    /**
     * 是否允许覆盖
     */
    @Schema(description = "是否允许覆盖")
    private Boolean override;

    /**
     * 调用方式:0-sync（同步调用） 1-async（异步调用）
     */
    @Schema(description = "调用方式:0-sync（同步调用） 1-async（异步调用）")
    private Boolean async;

    /**
     * 输入参数 JSON对象
     */
    @Schema(description = "输入参数 JSON对象")
    private InputDataField[] inputData;

    /**
     * 结果 JSON对象
     */
    @Schema(description = "结果 JSON对象")
    private OutputDataField outputData;

    /**
     * 代码文本 需要限制代码长度
     */
    @Schema(description = "代码文本 需要限制代码长度")
    private String serviceCode;

    @Schema(description = "租户id")
    private Long tenantId;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;




    @Override
    public boolean equals(Object o) {
        //自反性
        if (this == o) {
            return true;
        }
        //任何对象不等于null，比较是否为同一类型
        if (!(o instanceof ThingServiceDTO)) {
            return false;
        }
        //强制类型转换
        ThingServiceDTO thingServiceDTO = (ThingServiceDTO) o;
        //比较属性值
        return Objects.equals(getServiceName(), thingServiceDTO.getServiceName())
                && Objects.equals(getServiceCode(), thingServiceDTO.getServiceCode())
                && Objects.equals(getOverride(), thingServiceDTO.getOverride())
                && Objects.equals(getAsync(), thingServiceDTO.getAsync())
                && Objects.equals(JSON.toJSONString(getInputData()), JSON.toJSONString(thingServiceDTO.getInputData()))
                && Objects.equals(JSON.toJSONString(getOutputData()), JSON.toJSONString(thingServiceDTO.getOutputData()));
    }

    @Override
    public int hashCode() {
        return Objects.hash(getServiceName(),getServiceCode(),getOverride(),getAsync(),JSON.toJSONString(getInputData()),JSON.toJSONString(getOutputData()));
    }
}
