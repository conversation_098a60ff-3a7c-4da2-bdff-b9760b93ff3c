package com.nti56.nlink.product.device.client.model.dto.json;

import com.alibaba.fastjson.JSON;
import com.nti56.nlink.common.export.json.AbstractExport;
import com.nti56.nlink.product.device.client.model.dto.json.device.PropertyMetadataItem;
import com.nti56.nlink.product.device.client.model.dto.json.dpo.EventDpo;
import com.nti56.nlink.product.device.client.model.dto.json.dpo.SubscriptionDpo;
import com.nti56.nlink.product.device.client.model.dto.json.scriptApi.Service;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023-02-20 12:02:59
 * @since JDK 1.8
 */
@Data
public class EdgeGatewayRuntimeInfoField extends AbstractExport implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "通道设备关系")
    private Map<String,List<Long>> channelDeviceIds;

    @Override
    public boolean isJSON() {
        return true;
    }

    @Override
    public String toSqlString() {
        if (isJSON()) {
            return  JSON.toJSONString(this);
        }
        return null;
    }
}
