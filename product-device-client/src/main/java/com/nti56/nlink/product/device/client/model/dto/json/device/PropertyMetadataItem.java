package com.nti56.nlink.product.device.client.model.dto.json.device;

import com.nti56.nlink.common.util.BeanUtilsIntensifier;
import com.nti56.nlink.product.device.client.model.dto.json.ChannelRuntimeInfoField;
import com.nti56.nlink.product.device.client.model.dto.json.modelfield.DataTypeElm;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-25 10:20:23
 * @since JDK 1.8
 */
@Data
@Slf4j
public class PropertyMetadataItem implements Serializable {
    private static final long serialVersionUID = 1L;

    private String property;

    private DataTypeElm dataType;

    private Boolean isArray;

    private String address;

    private Long labelId;

    private String labelPath;

    private Integer length;

    private Integer stringBytes;

    private Boolean readOnly;

    private Boolean persist;

    private Long channelId;

    public AccessElm toAccess(ChannelRuntimeInfoField channel){
        AccessElm accessElm = BeanUtilsIntensifier.copyBean(this,AccessElm.class);
        accessElm.setDataType(this.getDataType().getType());
        accessElm.setLabelPath(this.labelPath);
        accessElm.setChannel(BeanUtilsIntensifier.copyBean(channel, ChannelElm.class));
        return accessElm;
    }

}
