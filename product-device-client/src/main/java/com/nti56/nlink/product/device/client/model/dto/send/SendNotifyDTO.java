package com.nti56.nlink.product.device.client.model.dto.send;

import com.nti56.nlink.common.dto.TenantIsolation;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/3/8 22:19<br/>
 * @since JDK 1.8
 */
@Data
@Schema(description = "发送通知dto")
public class SendNotifyDTO {

    @Valid
    @NotNull(message = "通知体不能为空")
    private NotifyDTO notify;

    @Schema(description = "租户隔离信息")
    @NotNull(message = "租户隔离信息不能为空")
    private TenantIsolation tenantIsolation;

}
