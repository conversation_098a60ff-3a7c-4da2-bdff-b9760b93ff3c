package com.nti56.nlink.product.device.client.model.dto.json.custom;

import java.io.Serializable;
import java.util.List;

import com.nti56.nlink.product.device.client.model.dto.json.AutoResponseConfigField;

import lombok.Data;

/**
 * 类说明: 报文
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-11-04 11:03:38
 * @since JDK 1.8
 */
@Data
public class MessageElm implements Serializable {
    private static final long serialVersionUID = 1L;
    
    private String messageName;
    private Integer direction;
    
    private Boolean autoResponse;
    private String autoResponseMessageName;
    private AutoResponseConfigField autoResponseConfig;
    
    private Boolean autoSend;
    private Long autoSendInterval;
    private String autoSendVarStr;
    
    private String clientKeyFieldName;

    private List<MessageItemElm> fieldList;
    
}
