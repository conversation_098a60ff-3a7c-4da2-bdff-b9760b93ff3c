package com.nti56.nlink.product.device.client.model.dto.engineering;

import com.nti56.nlink.product.device.client.model.dto.NotifyChannelDTO;
import com.nti56.nlink.product.device.client.model.dto.TemplateDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 类说明: <br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/11/4 13:19<br/>
 * @since JDK 1.8
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class NotifyServiceDataDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private List<NotifyChannelDTO> channelList;

    private List<TemplateDTO> templateList;
}
