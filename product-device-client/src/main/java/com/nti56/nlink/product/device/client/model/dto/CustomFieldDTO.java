package com.nti56.nlink.product.device.client.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.nti56.nlink.product.device.client.model.dto.json.CustomFieldDynamicLengthConfigField;
import com.nti56.nlink.product.device.client.model.dto.json.CustomFieldTransformField;

@Data
public class CustomFieldDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;

    private Integer targetType;

    private Long targetId;

    private Integer partType;

    private Integer sortNo;

    private String fieldName;

    private String fieldDescript;

    private Integer startByte;
    private Integer startBit;

    private Integer endByte;
    private Integer endBit;

    private Integer dataType;

    private Integer bytes;

    private Integer fieldType;
    private String fieldValue;

    private Integer checkType;
    
    private CustomFieldTransformField fieldTransform;
    
    private CustomFieldDynamicLengthConfigField dynamicLengthConfig;


    private LocalDateTime createTime;
    private LocalDateTime updateTime;

    private Long tenantId;


}
