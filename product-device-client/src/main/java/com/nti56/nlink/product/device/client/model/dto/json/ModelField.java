package com.nti56.nlink.product.device.client.model.dto.json;

import com.alibaba.fastjson.JSON;
import com.nti56.nlink.common.export.json.AbstractExport;
import com.nti56.nlink.product.device.client.model.dto.json.modelfield.EventElm;
import com.nti56.nlink.product.device.client.model.dto.json.modelfield.PropertyElm;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 类说明: 物模型定义/设备模型定义
 * device.model字段
 * thing_model.model字段
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 14:06:37
 * @since JDK 1.8
 */
@Data
@Schema
public class ModelField extends AbstractExport implements Serializable {
  private static final long serialVersionUID = 1L;

  @Schema(description = "属性")
  private List<PropertyElm> properties;

  @Schema(description = "事件")
  private List<EventElm> events;


  @Override
  public boolean isJSON() {
    return true;
  }

  @Override
  public String toSqlString() {
    if (isJSON()) {
      return  JSON.toJSONString(this);
    }
    return null;
  }

}
