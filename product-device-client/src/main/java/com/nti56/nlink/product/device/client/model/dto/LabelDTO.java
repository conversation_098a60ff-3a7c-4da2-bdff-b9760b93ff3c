package com.nti56.nlink.product.device.client.model.dto;

import com.nti56.nlink.product.device.client.model.dto.json.GatherParamField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * 类说明: 标签表
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 14:08:25
 * @since JDK 1.8
 */
@Data
@Schema(description = "标签")
public class LabelDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */ 
    private Long id;
    /**
     * 标签分组id
     */
    @Schema(description = "标签分组id")
    private Long labelGroupId;
    /**
     * 标签名称
     */
    private String name;

    @Schema(description = "别名")
    private String alias;
    /**
     * 标签描述
     */
    @Schema(description = "标签描述")
    private String descript;
    /**
     * 地址，如DB50.DBB1
     */
    @Schema(description = "地址，如DB50.DBB1")
    private String address;
    /**
     * 长度
     */
    @Schema(description = "长度")
    private Integer length;
    /**
     * 采集参数
     */
    private GatherParamField gatherParam;
    /**
     * 数据类型，bool/byte/short/int/float/string
     */
    @Schema(description = "数据类型，bool/byte/short/int/float/string/ushort/double")
    private String dataType;
    /**
     * 是否数组
     */
    @Schema(description = "是否数组")
    private Boolean isArray;
    /**
     * type是string类型时，表示string元素的byte长度，其他type类型放空
     */
    @Schema(description = "type是string类型时，表示string元素的byte长度，其他type类型放空")
    private Integer stringBytes;

    @Schema(description = "时间间隔，单位毫秒")
    private Integer intervalMs;

    @Schema(description = "是否只读")
    private Boolean readOnly;

    /**
     * 标签标识
     */
    @Schema(description = "标签标识")
    private String tag;

    @Schema(description = "租户id")
    private Long tenantId;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    @Schema(description = "创建人ID")
    private Long creatorId;

    @Schema(description = "创建人")
    private String creator;
}
