package com.nti56.nlink.product.device.client.model.dto.json.compute;

import com.nti56.nlink.product.device.client.model.dto.json.modelfield.FaultLevelDefineElm;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Set;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ComputeEventItem implements Serializable {
    
    private String type;
    private String eventName;

    private String trigger;
    private Set<String> conditionProperties;

    private Integer faultBeginThreshold; //故障开始阈值
    private Integer faultEndThreshold; //故障结束阈值
    private Map<String, String> faultInputMap; //故障参数映射，属性 -> 参数

    private Map<String, Object> metadata;
    private List<PropDataElm> properties;

    private String topic;

    private FaultLevelDefineElm faultLevelDefineElm;

    private Long deviceId;
    
}
