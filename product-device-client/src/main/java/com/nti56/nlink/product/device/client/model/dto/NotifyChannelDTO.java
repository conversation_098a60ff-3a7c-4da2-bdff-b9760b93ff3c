package com.nti56.nlink.product.device.client.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 类说明: 渠道表<br/>
 *
 * <AUTHOR> <br/>
 * @version 1.0
 * @date 2022/11/4 13:04<br/>
 * @since JDK 1.8
 */
@Data
@Schema(description = "渠道对象")
public class NotifyChannelDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    /**
     * 渠道名称
     */
    @Schema(description = "渠道名称")
    private String name;

    @Schema(description = "渠道描述")
    private String description;

    /**
     * 通知类型 0邮件 1短信
     */
    @Schema(description = "通知类型 0邮件 1短信 2钉钉机器人")
    private Integer notifyType;

    /**
     * 渠道参数
     */
    @Schema(description = "渠道参数")
    private String params;

    /**
     * 逻辑删除，1-删除
     */
    @Schema(description = "逻辑删除，1-删除")
    private Boolean deleted;

    @Schema(description = "租户ID")
    private Long tenantId;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

}
