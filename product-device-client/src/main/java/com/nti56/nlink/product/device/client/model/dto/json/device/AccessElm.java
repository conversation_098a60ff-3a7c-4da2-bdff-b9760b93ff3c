package com.nti56.nlink.product.device.client.model.dto.json.device;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-05-18 17:12:21
 * @since JDK 1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AccessElm implements Serializable {
   private static final long serialVersionUID = 1L;
   /*
   {
      "labelId": 123,
      "property": "temperature",
      "labelName": "temperatureLabel",
      "address": "DB50.BIT0",
      "dataType": "bool",
      "isArray": true,
      "length": 3,
      "stringBytes": 0,
      "readOnly": true,
      "channel": 123,
   }
   */
   private Long labelId;
   private String property;
   private String labelName;
   private String labelPath;
   private String address;
   private String dataType;
   private Boolean isArray;
   private Integer length;
   private Integer stringBytes;
   private Boolean readOnly;
   private Long channelId;
   private ChannelElm channel;
}
