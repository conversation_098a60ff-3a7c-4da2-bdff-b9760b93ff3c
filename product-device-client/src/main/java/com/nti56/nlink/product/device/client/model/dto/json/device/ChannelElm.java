package com.nti56.nlink.product.device.client.model.dto.json.device;

import lombok.Data;

import java.io.Serializable;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-05-26 09:02:33
 * @since JDK 1.8
 */
@Data

public class ChannelElm implements Serializable {
    private static final long serialVersionUID = 1L;
    private String driver;
    private Long channelId;
    private String channelKey;
    private String ip;
    private Integer port;
    private Integer retryCount;
    private Integer queueTimeout;
    private Integer rack;
    private Integer slot;
    private String spec;
    private Integer maxVars;
    private String customDriverMode;
    private String customDriverName;
    private Boolean debug;

    private Integer reconnectGapMs;
    private Integer maxConnection;
    private Integer delayIdleMs;

    private String userName;
    private String password;
    private Integer slaveId;

    private Integer comId;
    private Integer baudRate;
    private Integer dataBits;
    private Integer stopBits;
    private Integer parity;

    private Integer deviceId;

    private String endianness;
    private String firstAddress;

    private String targetNetId;
    private String senderNetId;

    private Integer commonAddress;

    private Integer unitNo;

    private Integer station;

    private String meterAddress;

    private String url;
    private String method;
    private String header;
    private String body;
  
}
