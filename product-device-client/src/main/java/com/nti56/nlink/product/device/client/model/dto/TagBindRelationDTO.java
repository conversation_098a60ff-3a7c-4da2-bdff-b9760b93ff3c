package com.nti56.nlink.product.device.client.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 标志关系表 实体类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2022-04-11 17:27:49
 * @since JDK 1.8
 */
@Data
@Schema( description = "标志关系表")
public class TagBindRelationDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 关联标识ID表
     */
    @Schema(description = "关联标识ID表")
    private Long tagId;

    /**
     * 资源类型:1-产品 2-设备 3-网关
     */
    @Schema(description = "资源类型:1-产品 2-设备 3-网关 4-标签 5-物模型")
    private Integer resourceType;

    /**
     * 目标对象ID
     */
    @Schema(description = "目标对象ID")
    private Long targetId;

    @Schema(description = "租户id")
    private Long tenantId;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

}
