package com.nti56.nlink.product.device.client.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.nti56.nlink.product.device.client.model.dto.json.AutoResponseConfigField;

@Data
public class CustomMessageDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;

    
    private String messageName;

    private String descript;

    private Integer direction;

    private Boolean autoResponse;
    private String autoResponseMessageName;
    private AutoResponseConfigField autoResponseConfig;
    private String clientKeyFieldName;

    private Long customDriverId;
    
    private LocalDateTime createTime;
    private LocalDateTime updateTime;

    private Long tenantId;


}
