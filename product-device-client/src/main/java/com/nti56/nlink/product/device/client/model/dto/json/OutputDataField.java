package com.nti56.nlink.product.device.client.model.dto.json;

import com.alibaba.fastjson.JSON;
import com.nti56.nlink.common.export.json.AbstractExport;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @ClassName OutputData
 * @date 2022/4/12 16:52
 * @Version 1.0
 */
@Data
@Schema
public class OutputDataField extends AbstractExport implements Serializable {
  private static final long serialVersionUID = 1L;

  @Schema(description = "数据类型，bool/byte/short/int/float/string/dataModel/void")
  private Integer dataType;

  @Schema(description = "是否是数组，true/false")
  private Boolean isArray;

  @Schema(description = "属性描述")
  private String descript;

  @Schema(description = "如果dataType是dataModel类型需要关联dataModelId")
  private Long dataModelId;

  @Schema(description = "结果描述")
  private String outputDataDescript;

  @Override
  public boolean isJSON() {
    return true;
  }

  @Override
  public String toSqlString() {
    if (isJSON()) {
      return  JSON.toJSONString(this);
    }
    return null;
  }
}
