package com.nti56.nlink.product.device.client.feign;


import com.nti56.nlink.common.util.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

//@FeignClient(name = "product-device-server",contextId = "product-device-server1")
//public interface IRedirectService {
//
//    @GetMapping(value = "redirect/execRedirect", produces = MediaType.APPLICATION_JSON_VALUE)
//    R execRedirect(@RequestHeader("ot_headers") String tenantIsolation, @RequestParam ("redirectId") Long redirectId);
//
//    @GetMapping(value = "redirect/execRedirectWithPayload", produces = MediaType.APPLICATION_JSON_VALUE)
//    R execRedirectWithPayload(@RequestHeader("ot_headers") String tenantIsolation, @RequestParam ("redirectId") Long redirectId
//            , @RequestParam ("payload") String payload);
//
//}
