package com.nti56.nlink.product.device.client.model.dto.json;

import java.io.Serializable;
import java.util.List;
import com.alibaba.fastjson.JSON;

import com.nti56.nlink.common.export.json.AbstractExport;
import com.nti56.nlink.product.device.client.model.dto.json.custom.FlagCarJimiElm;
import com.nti56.nlink.product.device.client.model.dto.json.custom.MessageElm;
import com.nti56.nlink.product.device.client.model.dto.json.custom.MessageItemElm;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-11-04 10:24:35
 * @since JDK 1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomDriverRuntimeInfoField extends AbstractExport implements Serializable  {
    private static final long serialVersionUID = 1L;
  
    private String driverName;
    private String descript;
    private String formatType;
    private String endian;
    private List<String> readLengthFields;
    private List<MessageItemElm> fixHeader;
    private List<MessageItemElm> fixTail;
    private List<MessageElm> messages;
    private Integer extraLength;
    private FlagCarJimiElm flagCarJimi;
    
    @Override
    public boolean isJSON() {
        return true;
    }

    @Override
    public String toSqlString() {
        if (isJSON()) {
        return  JSON.toJSONString(this);
        }
        return null;
    }

}
