package com.nti56.nlink.product.device.client.model.dto.json;

import java.io.Serializable;
import com.alibaba.fastjson.JSON;

import com.nti56.nlink.common.export.json.AbstractExport;
import com.nti56.nlink.product.device.client.model.dto.json.custom.SpecialFlagCarJimiElm;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 类说明: 
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-11-04 10:24:35
 * @since JDK 1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomDriverSpecialConfigField extends AbstractExport implements Serializable  {
    private static final long serialVersionUID = 1L;
    
    private SpecialFlagCarJimiElm flagCarJimi;
    
    @Override
    public boolean isJSON() {
        return true;
    }

    @Override
    public String toSqlString() {
        if (isJSON()) {
        return  JSON.toJSONString(this);
        }
        return null;
    }

}
