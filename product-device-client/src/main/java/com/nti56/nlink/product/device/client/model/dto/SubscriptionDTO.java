package com.nti56.nlink.product.device.client.model.dto;

import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <p>
 * 订阅表 实体类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Ddate 2022-09-22 15:17:38
 * @since JDK 1.8
 */
@Data
@Schema(description = "SubscriptionEntity对象")
public class SubscriptionDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 订阅名称
     */
    @Schema(description = "订阅名称")
    private String name;

    /**
     * 订阅描述
     */
    @Schema(description = "订阅描述")
    private String descript;

    /**
     * 订阅属性，用“,”隔开
     */
    @Schema(description = "订阅属性，用“,”隔开")
    private String properties;

    /**
     * 订阅事件，用“,”隔开
     */
    @Schema(description = "订阅事件，用“,”隔开")
    private String events;

    /**
     * 事件类型，1-anyDataChange，2-dataChange,0-fault
     */
    @Schema(description = "事件类型，1-anyDataChange，2-dataChange,0-fault")
    private Integer eventType;

    /**
     * 回调ID
     */
    @Schema(description = "回调ID")
    private Long callbackId;

    /**
     * 直属模型ID
     */
    @Schema(description = "直属模型ID")
    private Long directlyModelId;

    /**
     * 直属模型类型，1-物模型，2-设备模型
     */
    @Schema(description = "直属模型类型，1-物模型，2-设备模型")
    private Integer modelType;

    @Schema(description = "是否逐个发送，0-否，1-是")
    private Boolean sendOneByOne;

    @Schema(description = "数据传出类型，0-资源回调，1-设备服务")
    private Integer outType;

    @Schema(description = "传出类型是设备服务时的目标设备ID，如果是自身调用则为0")
    private Long targetDevice;

    @Schema(description = "目标服务")
    private String targetService;

    @Schema(description = "接收账号，用逗号隔开")
    private String receivers;

    /**
     * 是否开启订阅
     */
    @Schema(description = "是否开启订阅，0-否，1-是")
    private Boolean enable = true;
    
    /**
     * 数据转换代码
     */
    @Schema(description = "数据转换代码")
    private String dataConversionCode;

    @Schema(description = "未改变持续时长，单位秒")
    private Integer noChangeSeconds;

    @Schema(description = "租户id")
    private Long tenantId;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;
    
    /**
     * 来源id集合
     */
    @Schema(description = "来源id集合")
    private String fromId;

    private Long deviceId;



    @Override
    public boolean equals(Object o) {
        //自反性
        if (this == o) {
            return true;
        }
        //任何对象不等于null，比较是否为同一类型
        if (!(o instanceof SubscriptionDTO)) {
            return false;
        }
        //强制类型转换
        SubscriptionDTO thingServiceDTO = (SubscriptionDTO) o;
        //比较属性值
        return Objects.equals(getName(), thingServiceDTO.getName())
                && Objects.equals(getProperties(), thingServiceDTO.getProperties())
                && Objects.equals(getEvents(), thingServiceDTO.getEvents())
                && Objects.equals(getEventType(), thingServiceDTO.getEventType())
                && Objects.equals(getCallbackId(), thingServiceDTO.getCallbackId())
//                && Objects.equals(getDirectlyModelId(), thingServiceDTO.getDirectlyModelId())
                && Objects.equals(getSendOneByOne(), thingServiceDTO.getSendOneByOne())
                && Objects.equals(getOutType(), thingServiceDTO.getOutType())
                && Objects.equals(getTargetService(), thingServiceDTO.getTargetService())
                && Objects.equals(getReceivers(), thingServiceDTO.getReceivers())
                && Objects.equals(getEnable(), thingServiceDTO.getEnable())
                && Objects.equals(getDataConversionCode(), thingServiceDTO.getDataConversionCode());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getName(),getProperties(),getEvents(),getEventType(),getCallbackId(),getDirectlyModelId(),getSendOneByOne(),getOutType(),getTargetService(),getReceivers(),getEnable(),getDataConversionCode());
    }
}
