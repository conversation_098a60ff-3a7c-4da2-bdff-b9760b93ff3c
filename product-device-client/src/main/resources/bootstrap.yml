#spring:
#  application:
#    name: ${APPLICATION_NAME:product-device-client}
#  cloud:
#    nacos:
#      config:
#        server-addr: ${NACOS_SERVER_HOST:***************}:${NACOS_SERVER_PORT:28848}
#        file-extension: yml
#        prefix: ${spring.application.name}
#        namespace: ${NACOS_SERVER_NAMESPACE:nti-nlink-dev}
#      discovery:
#        server-addr: ${NACOS_SERVER_HOST:***************}:${NACOS_SERVER_PORT:28848}
#        namespace: ${NACOS_SERVER_NAMESPACE:nti-nlink-dev}
